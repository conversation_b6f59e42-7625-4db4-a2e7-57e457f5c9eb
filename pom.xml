<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/SETTINGS/1.2.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.2.0 http://maven.apache.org/xsd/settings-1.2.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.5.8.RELEASE</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.equinoxfitness</groupId>
	<artifactId>hypergate-freeze</artifactId>
	<version>0.0.0</version>
	<name>freeze-service</name>
	<description>Project for freezing membership</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<sonar.projectKey>equinox-platformx_hypergate.freeze_63288779-a99a-4901-adc4-c49edc2979d3</sonar.projectKey>
		<sonar.coverage.jacoco.xmlReportPaths>site/jacoco/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>

	</properties>
	<distributionManagement>
		<repository>
			<id>EquinoxReleases</id>
			<name>EquinoxReleases</name>
			<url>https://jfrog.equinoxfitness.com/artifactory/jfrog-libs-release-local/</url>
		</repository>
		<snapshotRepository>
			<id>EquinoxSnapshots</id>
			<name>EquinoxSnapshots</name>
			<url>https://jfrog.equinoxfitness.com/artifactory/jfrog-libs-snapshot-local/</url>
			<uniqueVersion>false</uniqueVersion>
		</snapshotRepository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>EquinoxJfrogSnapshots</id>
			<name>EquinoxJfrogSnapshots</name>
			<url>https://jfrog.equinoxfitness.com/artifactory/jfrog-libs-snapshot-local/</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>equinox-jfrog-release</id>
			<url>https://jfrog.equinoxfitness.com/artifactory/jfrog-libs-release-local/</url>
		</repository>
		<repository>
			<id>equinox-jfrog-3rd-party</id>
			<url>https://jfrog.equinoxfitness.com/artifactory/jfrog-ext-release-local/</url>
		</repository>
	</repositories>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.4.0</version>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.4.0</version>
		</dependency>

		<dependency>
			<groupId>com.equinoxfitness</groupId>
			<artifactId>hypergate-commons-lib</artifactId>
			<version>2.0.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>3.1</version>
		</dependency>
		<!-- <dependency> <groupId>joda-time</groupId> <artifactId>joda-time</artifactId> 
			<version>2.9.9</version> </dependency> <dependency> <groupId>org.codehaus.jackson</groupId> 
			<artifactId>jackson-mapper-asl</artifactId> <version>1.8.5</version> </dependency> 
			<dependency> <groupId>com.jcraft</groupId> <artifactId>jsch</artifactId> 
			<version>0.1.54</version> </dependency> -->
		<dependency>
			<groupId>org.springframework.ws</groupId>
			<artifactId>spring-ws-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.equinoxfitness</groupId>
			<artifactId>common-properties-lib</artifactId>
			<version>2.0.30</version>
		</dependency>
		<!-- <dependency> <groupId>org.springframework.cloud</groupId> <artifactId>spring-cloud-starter-eureka</artifactId> 
			</dependency> <dependency> <groupId>org.springframework.cloud</groupId> <artifactId>spring-cloud-starter-config</artifactId> 
			</dependency> -->
		<dependency>
			<groupId>com.equinoxfitness</groupId>
			<artifactId>hypergate-redis-util</artifactId>
			<version>2.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.equinoxfitness</groupId>
			<artifactId>common-redis-lib</artifactId>
			<version>2.0.7</version>
		</dependency>
		<dependency>
			<groupId>com.equinoxfitness</groupId>
			<artifactId>common-moso-lib</artifactId>
			<version>2.0.33</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.equinoxfitness</groupId>
			<artifactId>common-salesforce-lib</artifactId>
			<version>2.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.equinoxfitness</groupId>
			<artifactId>email-communcation-lib</artifactId>
			<version>2.0.2</version>
		</dependency>
		<dependency>
			<groupId>info.cukes</groupId>
			<artifactId>cucumber-java</artifactId>
			<version>1.2.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>info.cukes</groupId>
			<artifactId>cucumber-junit</artifactId>
			<version>1.2.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>info.cukes</groupId>
			<artifactId>cucumber-spring</artifactId>
			<version>1.2.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
			<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>1.7.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito</artifactId>
			<version>1.7.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.newrelic.agent.java</groupId>
			<artifactId>newrelic-agent</artifactId>
			<version>LATEST</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<version>10.2.0.jre8</version>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>Camden.SR5</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<!-- <plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<phase>prepare-package</phase>
						<goals>
							<goal>unpack-dependencies</goal>
						</goals>
						<configuration>
							<includeArtifactIds>newrelic-agent</includeArtifactIds>
							<outputDirectory>${project.build.outputDirectory}</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin> -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifestEntries>
							<Premain-Class>com.newrelic.bootstrap.BootstrapAgent</Premain-Class>
							<Can-Redefine-Classes>true</Can-Redefine-Classes>
							<Can-Retransform-Classes>true</Can-Retransform-Classes>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.equinoxfitness.freezeservice.FreezeServiceApplication</mainClass>
				</configuration>
			</plugin>
			
			<!-- Jacoca code coverage plougin -->
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.7</version>
				<executions>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<!-- <execution>
						<id>report</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution> -->
					<execution>
						<id>post-unit-test</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
						<configuration>
							<!-- Sets the path to the file which contains the execution data. -->

							<dataFile>target/jacoco.exec</dataFile>
							<includes>
								<include>com/equinoxfitness/**/*</include>
							</includes>
							<!-- <excludes>
								<exclude>com/equinoxfitness/TrainerClientApplication.class</exclude>
								<exclude>org/hibernate/**/*</exclude>
								<exclude>com/equinoxfitness/config/*</exclude>
								<exclude>castiron/*/*/*</exclude>
								<exclude>castiron/*/*/*/*</exclude>
							</excludes> -->
							<!-- Sets the output directory for the code coverage report. -->
							<outputDirectory>target/jacoco-ut</outputDirectory>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<systemPropertyVariables>
						<jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
					</systemPropertyVariables>
					
				</configuration>
			</plugin>
			
			<!--  plug-in for docker image , kindly comment out this plug-in if not required -->
            <plugin>
	            <groupId>com.spotify</groupId>
	            <artifactId>docker-maven-plugin</artifactId>
	            <version>0.4.11</version>
	            <configuration>
	                <imageName>${project.artifactId}:${project.version}</imageName>
	                <dockerDirectory>${project.basedir}/src/main/resources</dockerDirectory>
	                <buildArgs>
	                    <finalName>${project.build.finalName}.jar</finalName>
	                </buildArgs>
	                <resources>
	                    <resource>
	                        <targetPath>/app</targetPath>
		                    <directory>${project.build.directory}</directory>
		                    <include>${project.build.finalName}.jar</include>
		                    <!-- <include>new*</include> -->
	                    </resource>
	                </resources>
	            </configuration>
        	</plugin>
		</plugins>
	</build>

</project>
