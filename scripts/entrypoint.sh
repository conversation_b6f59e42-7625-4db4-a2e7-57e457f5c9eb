#!/bin/bash
set -e

args=(
  "ELASTIC_APM_SECRET_TOKEN"
  "ELASTIC_APM_ENDPOINT"
  "APP_NAME"
  "APP_ENV"
)

for arg in "${args[@]}"
do
  if [[ ! -v $arg ]]; then
    echo "You must provide environment variable ${arg}!"
    exit
  fi
done

java \
-javaagent:elastic/elastic-apm-agent-1.30.0.jar \
-Delastic.apm.service_name=${APP_NAME} \
-Delastic.apm.server_urls=${ELASTIC_APM_ENDPOINT} \
-Delastic.apm.secret_token=${ELASTIC_APM_SECRET_TOKEN} \
-Delastic.apm.environment=${APP_ENV} \
-Delastic.apm.application_packages=com.equinoxfitness \
-Delastic.apm.log_ecs_reformatting=OVERRIDE \
-jar "/app/${APP_NAME}.jar"
