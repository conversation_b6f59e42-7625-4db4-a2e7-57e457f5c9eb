FROM openjdk:8-jdk-alpine
RUN apk update && apk add bash
ADD https://repo1.maven.org/maven2/co/elastic/apm/elastic-apm-agent/1.30.0/elastic-apm-agent-1.30.0.jar elastic/

WORKDIR /
RUN apk add ca-certificates wget && update-ca-certificates

ENV TZ=America/New_York
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone


ARG version="0.0.0"
COPY target/*.jar /app/hypergate-freeze.jar
COPY src/main/resources/* /user/src/app/
COPY scripts/entrypoint.sh /bin/entrypoint.sh

ENV JAVA_OPTS="-Xmx512m"

EXPOSE 8000
ENTRYPOINT ["/bin/entrypoint.sh"]
