spring.profiles.include=common-qa


#MoSo Tenant DB
spring.datasource.tenantEquinox.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.tenantEquinox.testOnBorrow=true
spring.datasource.tenantEquinox.validationQuery=SELECT 1

# WebDatabase data source config
spring.datasource.webdb.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.webdb.testOnBorrow=true
spring.datasource.webdb.validationQuery=SELECT 1

#facility 
facility.getFacilityUrl=http://beta-webservices.equinoxfitness.com/facility/v1/
cache.facility.duration.ms=1440000

#Moso
freeze.moso.freezedata.url=https://equinoxtest.mosocloud.com:443/api/2/agreements/freezedata?suspensionName={suspensionName}
freeze.moso.getSession.url=https://equinoxtest.mosocloud.com:443/api/2/session/start
freeze.moso.itemSearch.url=https://equinoxtest.mosocloud.com:443/api/2/invoices/itemsearch?businessUnitCode={businessUnitCode}&name={name}
freeze.moso.taxRate.url=https://equinoxtest.mosocloud.com:443/api/2/invoices/configvalues?businessUnitCode={businessUnitCode}&type={type}
freeze.moso.freezeExtension.url=https://equinoxtest.mosocloud.com:443/api/2/agreements/freezeagreement
freeze.moso.createAndFinalizeInvoice.url=https://equinoxtest.mosocloud.com:443/api/2/invoices/createandfinalizeinvoice
freeze.moso.freezeAgreement.url=https://equinoxtest.mosocloud.com:443/api/2/agreements/freezeagreement
freeze.moso.addNote.url=https://equinoxtest.mosocloud.com:443/api/2/members/addnote
unfreeze.moso.frezeeEnd.url=https://equinoxtest.mosocloud.com:443/api/2/agreements/freezeagreement
freeze.moso.obligationExpirationDate.url=https://equinoxtest.mosocloud.com:443/api/2/agreements/changeobligationexpirationdate?id={id}&obligationExpirationDate={obligationExpirationDate}
freeze.defaultFacilityByCountryCode.map={1:"252",5:"871",6:"852"}
freeze.defaultFacility=252

##sales force login details
sfdc.authtoken.user.name=<EMAIL>
sfdc.authtoken.loginurl=https://test.salesforce.com/services/oauth2/token?grant_type=password

#DynamoDB events service urls
log.events.url=https://test-hypergate-services-2.equinoxfitness.com/events/v1/logEvents
log.authorize.url=https://test-hypergate-services-2.equinoxfitness.com/events/v1/logAuthorize
log.capture.url=https://test-hypergate-services-2.equinoxfitness.com/events/v1/logCapture

token.hypergate.url=https://stag-hypergate-services.equinoxfitness.com/token/v1

website.baseURL=https://qa.equinox.com
#Email
emailcommunication.sendemail.url=https://test-hypergate-services-2.equinoxfitness.com/communication-email/v1/sendEmail

moso.session.hypergate.url=https://test-hypergate-services-2.equinoxfitness.com/moso-service/v1/moso/token
moso.delete.session.hypergate.url=https://test-hypergate-services-2.equinoxfitness.com/moso-service/v1/moso/token

# Redis
redis.freeze.session.database=1

freeze.medical.record.type.id = 012dz000000IUFlAAO
api.cast.iron.user.id = 005A0000004o2JQIAY