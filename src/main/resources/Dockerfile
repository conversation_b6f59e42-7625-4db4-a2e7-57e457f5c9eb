FROM openjdk:8-jdk-alpine

WORKDIR /
RUN wget -q -O nr.zip http://central.maven.org/maven2/com/newrelic/agent/java/newrelic-java/5.0.0/newrelic-java-5.0.0.zip && \
    unzip nr.zip && rm -f nr.zip
VOLUME /newrelic
ENV NEW_RELIC_APP_NAME freeze-docker
ENV NEW_RELIC_LICENSE_KEY 2692fda2fae581dd49df5b6735e98bd6083ac818

ARG finalName
ADD /app/$finalName /app/freeze-0.0.0.jar

ADD . /user/src/app

EXPOSE 8011
ENTRYPOINT ["java", "-javaagent:/newrelic/newrelic.jar","-Dnewrelic.config.app_name=freeze-docker", "-jar", "/app/freeze-0.0.0.jar"]
CMD []