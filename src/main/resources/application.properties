server.port=8000
spring.application.name=freeze
server.contextPath=/freeze
spring.profiles.active=staging
# Utils
accountingCodeToFacilityId.map={'101':'001','10101':'101B','102':'002','103':'003','104':'004','105':'005','106':'006','107':'007','108':'008','109':'009','110':'010','111':'011','112':'014','11301':'113-E','140':'052','141':'054','14801':'148-E','401':'060','701':'071','702':'072','999':'CORP','9991':'060_1','9992':'071_T'}
#spring.jackson.date-format=yyyy-MM-dd'T'HH:mm:ss.SSSX
country.currency.map={'US':'$', 'UK':'GBP ', 'CA':'CA$'}

app.name=hypergate.freeze
freeze.regular.confirmation.email=FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_REASON
freeze.medical.pregnancy.request.email=FREEZE_REQUESTED_FOR_MEDICAL_PREGNANCY_REASON_TO_MEMBER
freeze.medical.pregnancy.confirmation.email=FREEZE_CONFIRMATION_TO_MEMBER_FOR_MEDICAL_PREGNANCY_REASON
freeze.regular.confirmation.email.inOb=FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_IN_OB
freeze.regular.confirmation.email.outOfOb=FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_OUT_OF_OB
freeze.confirmation.email.to.pif.members=FREEZE_CONFIRMATION_MAIL_TO_PIF_MEMBERS
freeze.confirmation.to.member.for.regular.out.of.ob.days.flow=FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_OUT_OF_OB_DAYS_FLOW
freeze.confirmation.mail.to.pif.members.days.flow=FREEZE_CONFIRMATION_MAIL_TO_PIF_MEMBERS_DAYS_FLOW
freeze.confirmation.to.member.for.regular.in.ob.days.flow=FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_IN_OB_DAYS_FLOW
medical.freeze.request.email= MEDICAL_FREEZE_REQUEST_FEE
medical.freeze.confirmation.email= MEDICAL_FREEZE_CONFIRMATION_FEE

sql.email.freeze.medical.pregnancy.store.webdb = INSERT INTO dbo.Freeze_Confirmation_Email (MemberId, MemberName, MemberEmail, FreezeStartDate, FreezeEndDate, FreezeDuration, FreezeReason, ScheduledTimeToEmail, IsEmailSent, CreatedBy, CountryCode, PilotFreezeMedFee) VALUES(?,?,?,?,?,?,?,?,?,?,?,?)

#------------------Swagger implementation properties------------------  
 #The title for the spring boot service to be displayed on swagger UI. It will refer to the above value.  
 swagger.title=FreezeMembership API
 #The description of the spring boot service. It will refer to the above value.  
 swagger.description=It contains freeze methods APIs
 #The version of the service. It will refer to the version specified in pom.xml.  
 swagger.version=V1 
 #The terms of service url for the service if applicable.  
 swagger.termsOfServiceUrl=https://www.apache.org/licenses/LICENSE-2.0  
 #The contact name for the service.  
 swagger.contact.name=Integration Team
 #The contact url for the service  
 swagger.contact.url=https://www.equinox.com
 #The contact email for the service  
 swagger.contact.email=<EMAIL>
 #The license information about this service  
 swagger.license=Apache License 2.0
 
##logging configuration
logging.pattern.file=%d %-4relative [%(${spring.application.name})] [%(${hostName})] [%(${server.port})] [%thread] [%X{x-amzn-trace-id}] [%X{Correlation-ID}] %-5level %logger{35} - %msg%n"

member.freeze.verification.template=FREEZE_LINK_TO_MEMBER_FROM_CLUBAPP

mosoUserApiKey.map={'webapi':'bc91677bac2a4da6aa43bb3239aa3497','ptapi':'633fa94b668f4f38879e5da8f8d7e570','pilapi':'f2275cfd4df24915aa5b60d3e0747f36','haapi':'43734d2d5bf44b10995f4cf8c90cfe97','clubapi':'c63ff1e82f86413fb565ba71cf99f2ac'}
redis.tokenExpiry.time=43200

ccType.map={'VISA':'VISA','MC':'MASTERCARD','AMEX':'AMERICANEXPRESS','DISC':'DISCOVER','DINERS':'DINERS'}

cancellationStateId.list=6

moso.api.user.name = webapi
mosoUserCredentials.map={'webapi':'WEBAPI@equinox','ptapi':'PTAPI@equinox','pilapi':'PILAPI@equinox','haapi':'HAAPI@equinox','clubapi':'CLUBAPI@equinox'}
defaultFacilityByCountryCode.map={1:"252",5:"871",6:"852"}