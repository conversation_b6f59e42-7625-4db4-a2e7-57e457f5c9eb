spring.profiles.include=common-prod


#MoSo Tenant DB
spring.datasource.tenantEquinox.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.tenantEquinox.testOnBorrow=true
spring.datasource.tenantEquinox.validationQuery=SELECT 1

 # papertraildatabase prod data source config
spring.datasource.papertrail.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.papertrail.testOnBorrow=true
spring.datasource.papertrail.validationQuery=SELECT 1

# WebDatabase data source config
spring.datasource.webdb.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.webdb.testOnBorrow=true
spring.datasource.webdb.validationQuery=SELECT 1

#facility 
facility.getFacilityUrl=http://beta-webservices.equinoxfitness.com/facility/v1/
cache.facility.duration.ms=1440000

#Moso
freeze.moso.freezedata.url=https://equinoxapi.mosocloud.com:443/api/2/agreements/freezedata?suspensionName={suspensionName}
freeze.moso.getSession.url=https://equinoxapi.mosocloud.com:443/api/2/session/start
freeze.moso.itemSearch.url=https://equinoxapi.mosocloud.com:443/api/2/invoices/itemsearch?businessUnitCode={businessUnitCode}&name={name}
freeze.moso.taxRate.url=https://equinoxapi.mosocloud.com:443/api/2/invoices/configvalues?businessUnitCode={businessUnitCode}&type={type}
freeze.moso.freezeExtension.url=https://equinoxapi.mosocloud.com:443/api/2/agreements/freezeagreement
freeze.moso.createAndFinalizeInvoice.url=https://equinoxapi.mosocloud.com:443/api/2/invoices/createandfinalizeinvoice
freeze.moso.freezeAgreement.url=https://equinoxapi.mosocloud.com:443/api/2/agreements/freezeagreement
freeze.moso.addNote.url=https://equinoxapi.mosocloud.com:443/api/2/members/addnote
freeze.moso.obligationExpirationDate.url=https://equinoxapi.mosocloud.com:443/api/2/agreements/changeobligationexpirationdate?id={id}&obligationExpirationDate={obligationExpirationDate}
unfreeze.moso.frezeeEnd.url=https://equinoxapi.mosocloud.com:443/api/2/agreements/freezeagreement
freeze.defaultFacilityByCountryCode.map={1:"252",5:"871",6:"852"}
freeze.defaultFacility=252

##sales force login details
sfdc.authtoken.loginurl=https://login.salesforce.com/services/oauth2/token?grant_type=password

#DynamoDB events service urls
log.events.url=https://hypergate-services-2.equinoxfitness.com/events/v1/logEvents
log.authorize.url=https://hypergate-services-2.equinoxfitness.com/events/v1/logAuthorize
log.capture.url=https://hypergate-services-2.equinoxfitness.com/events/v1/logCapture

token.hypergate.url=https://hypergate-services.equinoxfitness.com/token/v1

website.baseURL=https://www.equinox.com
#Email
emailcommunication.sendemail.url=https://hypergate-services-2.equinoxfitness.com/communication-email/v1/sendEmail

moso.session.hypergate.url=https://hypergate-services-2.equinoxfitness.com/moso-service/v1/moso/token
moso.delete.session.hypergate.url=https://hypergate-services-2.equinoxfitness.com/moso-service/v1/moso/token

# Redis
redis.freeze.session.database=1

freeze.medical.record.type.id = 012WR00000D9eZvYAJ
api.cast.iron.user.id = 005A0000004o2JQIAY