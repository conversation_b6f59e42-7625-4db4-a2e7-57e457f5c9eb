/**
 * 
 */
package com.equinoxfitness.freezeservice.controller;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.equinoxfitness.commons.output.Docs;
import com.equinoxfitness.commons.output.GetFacilityResponse;
import com.equinoxfitness.commons.output.Version;
import com.equinoxfitness.commons.service.EventsService;
import com.equinoxfitness.commons.service.FacilityService;
import com.equinoxfitness.commons.utils.CountryCode;
import com.equinoxfitness.commons.utils.DynamoPK;
import com.equinoxfitness.commons.utils.ErrorMessageHandler;
import com.equinoxfitness.commons.utils.ExceptionMessageEnum;
import com.equinoxfitness.commons.utils.RestResponse;
import com.equinoxfitness.freezeservice.contract.ApiResponse;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput;
import com.equinoxfitness.freezeservice.contract.EngageFreezeEmailIOutput;
import com.equinoxfitness.freezeservice.contract.FreezeEmailIOutput;
import com.equinoxfitness.freezeservice.contract.FreezeEmailInput;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInput;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV3;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput;
import com.equinoxfitness.freezeservice.contract.FreezeMemberResponse;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV3;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV4;
import com.equinoxfitness.freezeservice.contract.MemberFreezeCase;
import com.equinoxfitness.freezeservice.contract.MemberFreezeCaseOutput;
import com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput;
import com.equinoxfitness.freezeservice.contract.UnfreezeMembershipResponse;
import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.exception.DataFetchException;
import com.equinoxfitness.freezeservice.service.FreezeService;
import com.equinoxfitness.freezeservice.service.PaymentService;
import com.equinoxfitness.freezeservice.service.SFDCService;
import com.equinoxfitness.freezeservice.service.UnFreezeService;
import com.equinoxfitness.freezeservice.utils.FacilityConversion;
import com.equinoxfitness.freezeservice.utils.FreezeMembershipConstants;
import com.equinoxfitness.freezeservice.utils.FreezeRequestValidator;
import com.equinoxfitness.freezeservice.utils.FreezeServiceExceptionEnum;
import com.equinoxfitness.freezeservice.utils.FreezeServiceUtils;
import com.equinoxfitness.redis.contract.member.Member;
import com.equinoxfitness.redis.service.RedisApiService;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 *
 */
@RestController
public class FreezeServiceController {

	@Autowired
	FreezeService freezeService;

	@Autowired
	private RestResponse restResponse;
	
	@Autowired
	EventsService eventService;
	
	@Autowired
	private HttpServletRequest httpServletRequest;
	
	@Autowired
	FreezeServiceDAO freezeServiceDAO;
	
	@Autowired
	FacilityConversion facilityConversion;
	
	@Autowired
	private UnFreezeService unfreezeService;
	
	@Autowired
	private SFDCService sdfcService;

	@Autowired
	FreezeRequestValidator freezeRequestValidator;
	
	@Autowired
	FreezeServiceUtils freezeServiceUtils;
	
	@Autowired
	private RedisApiService redisApiService;
	
	@Autowired
	FacilityService facilityService;
	
	@Autowired
	PaymentService paymentService;
	
	@Autowired
	SFDCService sfdcService;
	
	@Autowired
	private ErrorMessageHandler errorMessageHandler;
	
	private static final Logger logger = LoggerFactory.getLogger(FreezeServiceController.class);

	@ApiOperation(value = "Get Version", notes = "Get Version")
	@RequestMapping(method = RequestMethod.GET, value = "v1/version")
	public Version getVersion() {
		Docs docs = new Docs();
		docs.setStatus("Live");
		docs.setUrl("v1/swagger.json");
		Version version = new Version();
		version.setVersion(this.getClass().getPackage().getImplementationVersion());
		version.setDocs(docs);

		return version;
	}

	@ApiOperation(value = "Check Freeze Eligibility", notes = "Check Freeze Eligibility")
	@RequestMapping(method = RequestMethod.POST, value = "v1/freeze-eligible", produces = { "application/json" })
	public @ResponseBody ResponseEntity<CheckFreezeEligiblityOutput> checkFreezeEligibility(
			@RequestBody CheckFreezeEligibilityInput checkFreezeEligibilityInput) throws Exception {

		// Declarations
		CheckFreezeEligiblityOutput response = new CheckFreezeEligiblityOutput();
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");

		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+checkFreezeEligibilityInput.getMemberId(), "FreezeEligibility:::" +checkFreezeEligibilityInput.getMemberId(), checkFreezeEligibilityInput.getThreadId(),
				correlationId, checkFreezeEligibilityInput, "Logging request",HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		logger.debug("Calling Service");
		// Validations
		if (StringUtils.isBlank(checkFreezeEligibilityInput.getMemberId())) {
			response.setEligibleForFreeze(false);
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED.name());
		}

		response = freezeService.checkFreezeEligibility(checkFreezeEligibilityInput);
		if (response.getMessages() == null) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+checkFreezeEligibilityInput.getMemberId(), "FreezeEligibility:::" +checkFreezeEligibilityInput.getMemberId(), checkFreezeEligibilityInput.getThreadId(),
					correlationId, checkFreezeEligibilityInput, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}

	}

	@ApiOperation(value = "Get Freeze Reason", notes = "Get Freeze Reason")
	@RequestMapping(method = RequestMethod.GET, value = "/v1/freeze-info/{mosoMemberId}/")
	public @ResponseBody ResponseEntity<RetrieveFreezeReasonOutput> retrieveFreezeReason(
			@PathVariable(name = "mosoMemberId") String mosoMemberId,
			@RequestParam(required = true, value = "freezeReason") String freezeReason,
			@RequestParam(required = true, value = "duration") int duration) throws Exception {

		// Declaration
		RetrieveFreezeReasonOutput response = new RetrieveFreezeReasonOutput();
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		
		//Logging request in trace table
		//eventService.logEvents("Retrieve-Freeze-Reason:::" +mosoMemberId, correlationId, correlationId, "FreezeReason : "+freezeReason+" Duration :"+duration, "Logging request", HttpStatus.CREATED,
			//			System.currentTimeMillis() - startTime);
		
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoMemberId, "Retrieve-Freeze-Reason:::" +mosoMemberId, correlationId,
				correlationId, "FreezeReason : "+freezeReason+" Duration :"+duration, "Logging request" ,HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		logger.debug("Calling Service");
		// Validations
		if (StringUtils.isBlank(mosoMemberId)) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED.name());
		}
		if (StringUtils.isBlank(freezeReason)) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.getCode(),
					FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.name());
		}
		if (duration <= 0) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_DURATION_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.FREEZE_DURATION_REQUIRED.name());
		}
		response = freezeService.retrieveFreezeReason(mosoMemberId, freezeReason, duration);
		if (response.getMessages() == null) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else {
			//eventService.logEvents("Retrieve-Freeze-Reason:::" +mosoMemberId, correlationId, correlationId, "FreezeReason : "+freezeReason+" Duration :"+duration, response.getMessages()[0].getFriendlyMessage(), HttpStatus.BAD_REQUEST,
				//	System.currentTimeMillis() - startTime);
			
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoMemberId, "Retrieve-Freeze-Reason:::" +mosoMemberId, correlationId,
					correlationId, "FreezeReason : "+freezeReason+" Duration :"+duration, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}
	}

	@ApiOperation(value = "Freeze Membership", notes = "Freeze Membership")
	@RequestMapping(method = RequestMethod.POST, value = "/v1/freeze-membership/")
	public @ResponseBody ResponseEntity<FreezeMemberResponse> freezeMembership(
			@RequestBody FreezeMembershipInput freezeMembershipInput) throws Exception {

		//Declaration
		FreezeMemberResponse response = new FreezeMemberResponse();
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		
		//Logging request in trace table
		//eventService.logEvents("FreezeMembership:::" +freezeMembershipInput.getMemberId(), freezeMembershipInput.getThreadId(), correlationId, freezeMembershipInput, "Logging request", HttpStatus.CREATED,
			//			System.currentTimeMillis() - startTime);
		
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeMembershipInput.getMemberId(), "FreezeMembership:::" +freezeMembershipInput.getMemberId(), freezeMembershipInput.getThreadId(),
				correlationId, freezeMembershipInput, "Logging request" ,HttpStatus.CREATED, System.currentTimeMillis() - startTime);

		
		// Validations
		if (StringUtils.isBlank(freezeMembershipInput.getMemberId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED.name());
		}
		if (StringUtils.isBlank(freezeMembershipInput.getFreezeReasonId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.getCode(),
					FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.name());
		}
		/*
		 * if (freezeMembershipInput.getDurationMonths() <= 0) { return
		 * restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
		 * ExceptionMessageEnum.VALIDATION,
		 * FreezeServiceExceptionEnum.FREEZE_DURATION_REQUIRED.getCode(),
		 * FreezeServiceExceptionEnum.FREEZE_DURATION_REQUIRED.name()); }
		 */
		if (freezeMembershipInput.getStartDate() == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.START_DATE_EMPTY.getCode(),
					FreezeServiceExceptionEnum.START_DATE_EMPTY.name());
		}
		if (freezeMembershipInput.getEndDate() == null && freezeMembershipInput.getDurationMonths() <= 0) {

			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION,
					FreezeServiceExceptionEnum.END_DATE_AND_MONTH_DURATION_BLANK.getCode(),
					FreezeServiceExceptionEnum.END_DATE_AND_MONTH_DURATION_BLANK.name());

		}
		if (StringUtils.isBlank(freezeMembershipInput.getSource())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.SOURCE_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.SOURCE_REQUIRED.name());
		}
		response = freezeService.freezeMembership(freezeMembershipInput);
		if (response.getMessages() == null) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else {
			//eventService.logEvents("FreezeMembership:::" +freezeMembershipInput.getMemberId(), freezeMembershipInput.getThreadId(), correlationId, freezeMembershipInput, response.getMessages()[0].getFriendlyMessage(), HttpStatus.BAD_REQUEST,
				//	System.currentTimeMillis() - startTime);
			
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeMembershipInput.getMemberId(), "FreezeMembership:::" +freezeMembershipInput.getMemberId(), freezeMembershipInput.getThreadId(),
					correlationId, freezeMembershipInput, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}

	}

	@ApiOperation(value = "Freeze Extension", notes = "Freeze Extension")
	@RequestMapping(method = RequestMethod.POST, value = "/v1/freeze-extension/")
	public @ResponseBody ResponseEntity<FreezeExtensionOutput> freezeExtension(
			@RequestBody FreezeExtensionInput freezeExtensionInput) throws Exception {

		//Declarations
		FreezeExtensionOutput response = new FreezeExtensionOutput();
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		
		//Logging request in trace table
		//eventService.logEvents("FreezeExtension:::" +freezeExtensionInput.getMemberId(), freezeExtensionInput.getThreadId(), correlationId, freezeExtensionInput, "Logging request", HttpStatus.CREATED,
			//			System.currentTimeMillis() - startTime);
		
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInput.getMemberId(), "FreezeExtension:::" +freezeExtensionInput.getMemberId(), freezeExtensionInput.getThreadId(),
				correlationId, freezeExtensionInput, "Logging request" ,HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		// Validation
		if (StringUtils.isBlank(freezeExtensionInput.getMemberId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED.name());
		}
		if (freezeExtensionInput.getExtensionDurationMonths() <= 0) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION,
					FreezeServiceExceptionEnum.FREEZE_EXTENSION_MONTH_VALIDATION.getCode(),
					FreezeServiceExceptionEnum.FREEZE_EXTENSION_MONTH_VALIDATION.name());
		}
		if (freezeExtensionInput.getFreezeExtensionEndDate() == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION,
					FreezeServiceExceptionEnum.FREEZE_EXTENSION_ENDDATE_VALIDATION.getCode(),
					FreezeServiceExceptionEnum.FREEZE_EXTENSION_ENDDATE_VALIDATION.name());
		}
		if (StringUtils.isBlank(freezeExtensionInput.getFacilityId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FACILITYID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.FACILITYID_REQUIRED.name());
		}
		response = freezeService.freezeExtension(freezeExtensionInput);
		// For creating case in SF
		if (response.getMessages() != null && response.getMessages()[0].getMessageID() == 201) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else if (response.getMessages() != null && response.getMessages()[0].getMessageID() != 201) {
			//eventService.logEvents("FreezeExtension:::" +freezeExtensionInput.getMemberId(), freezeExtensionInput.getThreadId(), correlationId, freezeExtensionInput, response.getMessages()[0].getFriendlyMessage(), HttpStatus.BAD_REQUEST,
				//	System.currentTimeMillis() - startTime);
			
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInput.getMemberId(), "FreezeExtension:::" +freezeExtensionInput.getMemberId(), freezeExtensionInput.getThreadId(),
					correlationId, freezeExtensionInput, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		} else {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}
	}
	
	@ApiOperation(value = "Check Freeze Eligibility", notes = "Check Freeze Eligibility")
	@RequestMapping(method = RequestMethod.POST, value = "v2/freeze-eligible", produces = { "application/json" })
	public @ResponseBody ResponseEntity<CheckFreezeEligiblityOutput> checkFreezeEligibilityV2(
			@Valid @RequestBody CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput) throws Exception {
		
		//Request validation
		CheckFreezeEligiblityOutput response = new CheckFreezeEligiblityOutput();
		FreezeServiceExceptionEnum FreezeServiceException = freezeRequestValidator.validate(checkFreezeEligibilityInput);
		if (FreezeServiceException != null) {
			response.setEligibleForFreeze(false);
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceException.getCode(),
					FreezeServiceException.name());
		}
		// Declarations		
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+checkFreezeEligibilityInput.getMosoId(), "FreezeEligibility:::" +checkFreezeEligibilityInput.getMosoId(), checkFreezeEligibilityInput.getThreadId(),
				correlationId, checkFreezeEligibilityInput, "Logging initial HG request",HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		logger.debug("Calling Service");
		// Validations
		if (StringUtils.isBlank(checkFreezeEligibilityInput.getMosoId())) {
			response.setEligibleForFreeze(false);
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.name());
		}
		response = freezeService.checkFreezeEligibilityV2(checkFreezeEligibilityInput);
		if(StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()))
			checkFreezeEligibilityInput.setSource("null");
		if (response.getMessages() == null && StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()) || !checkFreezeEligibilityInput.getSource().equalsIgnoreCase("clubapp")) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}else if(StringUtils.isNotEmpty(checkFreezeEligibilityInput.getSource()) && checkFreezeEligibilityInput.getSource().equalsIgnoreCase("clubapp")) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}else if(StringUtils.isNotEmpty(checkFreezeEligibilityInput.getSource()) && checkFreezeEligibilityInput.getSource().equalsIgnoreCase("engage")) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}
		else {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+checkFreezeEligibilityInput.getMosoId(), "FreezeEligibility:::error." +checkFreezeEligibilityInput.getMosoId(), checkFreezeEligibilityInput.getThreadId(),
					correlationId, checkFreezeEligibilityInput, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}

	}

	/**
	 * This API is used to check member freeze eligibility from cancel freeze flow . This eligibility checks is only applicable to
	 * Regular freeze reason.
	 * @param checkFreezeEligibilityInput
	 * @return
	 * @throws Exception
	 */
	@ApiOperation(value = "Check Cancel Freeze Eligibility", notes = "Check Cancel Freeze Eligibility")
	@RequestMapping(method = RequestMethod.POST, value = "v3/freeze-eligible", produces = { "application/json" })
	public @ResponseBody ResponseEntity<CheckFreezeEligiblityOutput> checkFreezeEligibilityV3(
			@Valid @RequestBody CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput) throws Exception {

		//Request validation
		CheckFreezeEligiblityOutput response = new CheckFreezeEligiblityOutput();
		FreezeServiceExceptionEnum FreezeServiceException = freezeRequestValidator.validate(checkFreezeEligibilityInput);
		if (FreezeServiceException != null) {
			response.setEligibleForFreeze(false);
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceException.getCode(),
					FreezeServiceException.name());
		}
		// Declarations
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");

		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+checkFreezeEligibilityInput.getMosoId(), "FreezeEligibility:::" +checkFreezeEligibilityInput.getMosoId(), checkFreezeEligibilityInput.getThreadId(),
				correlationId, checkFreezeEligibilityInput, "Logging initial HG request",HttpStatus.CREATED, System.currentTimeMillis() - startTime);

		logger.debug("Calling Service");
		// Validations
		if (StringUtils.isBlank(checkFreezeEligibilityInput.getMosoId())) {
			response.setEligibleForFreeze(false);
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.name());
		}
		if (checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			response = freezeService.checkFreezeEligibilityV3(checkFreezeEligibilityInput);
		} else {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.REGULAR_REASON_IS_ALLOWED.getCode(),
					FreezeServiceExceptionEnum.REGULAR_REASON_IS_ALLOWED.name());
		}

		if(StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()))
			checkFreezeEligibilityInput.setSource("null");
		if (response.getMessages() == null && StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()) || !checkFreezeEligibilityInput.getSource().equalsIgnoreCase("clubapp")) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}else if(StringUtils.isNotEmpty(checkFreezeEligibilityInput.getSource()) && checkFreezeEligibilityInput.getSource().equalsIgnoreCase("clubapp")) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}else if(StringUtils.isNotEmpty(checkFreezeEligibilityInput.getSource()) && checkFreezeEligibilityInput.getSource().equalsIgnoreCase("engage")) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}
		else {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+checkFreezeEligibilityInput.getMosoId(), "FreezeEligibility:::error." +checkFreezeEligibilityInput.getMosoId(), checkFreezeEligibilityInput.getThreadId(),
					correlationId, checkFreezeEligibilityInput, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}

	}

	@ApiOperation(value = "Freeze Extension", notes = "Freeze Extension")
	@RequestMapping(method = RequestMethod.POST, value = "/v2/freeze-extension/")
	public @ResponseBody ResponseEntity<FreezeExtensionOutput> freezeExtensionV2(
			@RequestBody FreezeExtensionInputV2 freezeExtensionInput) throws Exception {

		//Declarations
		FreezeExtensionOutput response = new FreezeExtensionOutput();
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;
		freezeExtensionInput.setCorrelationId(correlationId);
		
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInput.getMosoId(), "FreezeExtension:::" +freezeExtensionInput.getMosoId(), freezeExtensionInput.getThreadId(),
				correlationId, freezeExtensionInput, "Logging initial HG request" ,HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		// Validation
		if (StringUtils.isBlank(freezeExtensionInput.getMosoId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.name());
		}
		if (freezeExtensionInput.getExtensionDurationMonths() <= 0) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION,
					FreezeServiceExceptionEnum.FREEZE_EXTENSION_MONTH_VALIDATION.getCode(),
					FreezeServiceExceptionEnum.FREEZE_EXTENSION_MONTH_VALIDATION.name());
		}
		if (freezeExtensionInput.getFreezeExtensionEndDate() == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION,
					FreezeServiceExceptionEnum.FREEZE_EXTENSION_ENDDATE_VALIDATION.getCode(),
					FreezeServiceExceptionEnum.FREEZE_EXTENSION_ENDDATE_VALIDATION.name());
		}
		if (StringUtils.isBlank(freezeExtensionInput.getFacilityId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FACILITYID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.FACILITYID_REQUIRED.name());
		}
		response = freezeService.freezeExtensionV2(freezeExtensionInput,null);
		// For creating case in SF
		if (response.getMessages() != null && response.getMessages()[0].getMessageID() == 201) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else if (response.getMessages() != null && response.getMessages()[0].getMessageID() != 201) {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInput.getMosoId(), "FreezeExtension:::error." +freezeExtensionInput.getMosoId(), freezeExtensionInput.getThreadId(),
					correlationId, freezeExtensionInput, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		} else {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}
	}
	
	@ApiOperation(value = "Freeze Membership", notes = "Freeze Membership")
	@RequestMapping(method = RequestMethod.POST, value = "/v2/freeze-membership/")
	public @ResponseBody ResponseEntity<FreezeMemberResponse> freezeMembershipV2(
			@RequestBody FreezeMembershipInputV2 freezeMembershipInput) throws Exception {

		//Declaration
		FreezeMemberResponse response = new FreezeMemberResponse();
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;
		freezeMembershipInput.setCorrelationId(correlationId);
		
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeMembershipInput.getMosoId(), "FreezeMembership:::" +freezeMembershipInput.getMosoId(), freezeMembershipInput.getThreadId(),
				correlationId, freezeMembershipInput, "Logging initial HG request" ,HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		// Validations
		if (StringUtils.isBlank(freezeMembershipInput.getMosoId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.name());
		}
		if (StringUtils.isBlank(freezeMembershipInput.getFreezeReasonId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.getCode(),
					FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.name());
		}
		if (freezeMembershipInput.getStartDate() == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.START_DATE_EMPTY.getCode(),
					FreezeServiceExceptionEnum.START_DATE_EMPTY.name());
		}
		if (freezeMembershipInput.getEndDate() == null && freezeMembershipInput.getDurationMonths() <= 0) {

			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION,
					FreezeServiceExceptionEnum.END_DATE_AND_MONTH_DURATION_BLANK.getCode(),
					FreezeServiceExceptionEnum.END_DATE_AND_MONTH_DURATION_BLANK.name());

		}
		if (StringUtils.isBlank(freezeMembershipInput.getSource())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.SOURCE_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.SOURCE_REQUIRED.name());
		}
		
		//Check for existing freeze 
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMosoId());
		if (memberAgreementDetail == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode(),
					FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.name());
	
		}
		
		/* freeze extension is allowed only on active/Pending freeze agreements */
		if (!StringUtils.isBlank(memberAgreementDetail.getFreezeStatus()) && (memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("Active")
				|| memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("PENDING START"))) {
			
			Date existingFreezeEndDate=memberAgreementDetail.getFreezeEndTime();
			Date newFreezeStartDate=freezeMembershipInput.getStartDate();
			
			// Add one day to existing end date and then compare new start date 
			Calendar cal = Calendar.getInstance();
			cal.setTime(existingFreezeEndDate);
			cal.add(Calendar.DATE, 1);
			Date newExistingFreezeEndDate=cal.getTime();
			
			logger.info("Existing freeze end={}",existingFreezeEndDate);
			logger.info("New freeze end={}",newExistingFreezeEndDate);
			logger.info("New freeze start date={}",newFreezeStartDate);
			
			//logger.info("newFreezeStartDate.before(newExistingFreezeEndDate) ={}",getZeroTimeDate(newFreezeStartDate).before(getZeroTimeDate(newExistingFreezeEndDate) ));
			//logger.info("newFreezeStartDate.equals(newExistingFreezeEndDate)={}",newFreezeStartDate.equals(newExistingFreezeEndDate));
			
			//If new freeze start date is = or before existing freeze end date then create freeze extension 
			if(getZeroTimeDate(newFreezeStartDate).before(getZeroTimeDate(newExistingFreezeEndDate)) ||getZeroTimeDate(newFreezeStartDate).equals(getZeroTimeDate(newExistingFreezeEndDate))){
				String  eclubFacilityID= memberAgreementDetail.getHomeFacilityId();
				String accountingCode = facilityConversion.facilityConversion(eclubFacilityID);
				FreezeExtensionInputV2 freezeExtensionInputV2=new FreezeExtensionInputV2();
				freezeExtensionInputV2.setFacilityId(accountingCode);
				freezeExtensionInputV2.setMosoId(freezeMembershipInput.getMosoId());
				freezeExtensionInputV2.setExtensionDurationMonths(freezeMembershipInput.getDurationMonths());
				Date freezeExtensionEndDate=cal.getTime();
				if (freezeMembershipInput.getEndDate() == null && freezeMembershipInput.getDurationMonths() > 0) {
					Date startDate = freezeMembershipInput.getStartDate();
					cal = Calendar.getInstance();
					cal.setTime(startDate);
					cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
					freezeExtensionEndDate=cal.getTime();
					//freezeMembershipInput.setEndDate(cal.getTime());
				}
				logger.info("New freeze extension end={}",freezeExtensionEndDate);
				freezeExtensionInputV2.setFreezeExtensionEndDate(freezeExtensionEndDate);
				freezeExtensionInputV2.setWaiveOffExtensionFee(freezeMembershipInput.isWaveFreezeFee());
				freezeExtensionInputV2.setFreezeReason(freezeMembershipInput.getFreezeReasonId());
				freezeExtensionInputV2.setThreadId(freezeMembershipInput.getThreadId());
				freezeExtensionInputV2.setCorrelationId(freezeMembershipInput.getCorrelationId());
			
				FreezeExtensionOutput out = freezeService.freezeExtensionV2(freezeExtensionInputV2,memberAgreementDetail);
				if (out.getMessages() != null && out.getMessages()[0].getMessageID() == 201) {
					response.setMessages(out.getMessages());
					return restResponse.populateSuccessResponse(response, HttpStatus.OK);
				} else if (out.getMessages() != null && out.getMessages()[0].getMessageID() != 201) {
					response.setMessages(out.getMessages());
					eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInputV2.getMosoId(), "FreezeExtension:::error." +freezeExtensionInputV2.getMosoId(), freezeExtensionInputV2.getThreadId(),
							correlationId, freezeExtensionInputV2, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
					
					return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
				} else {
					getFreezeMemberRespose(out,response);
					return restResponse.populateSuccessResponse(response, HttpStatus.OK);
				}
				
            }
		}


		response = freezeService.freezeMembershipV2(freezeMembershipInput);
		if (response.getMessages() == null) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeMembershipInput.getMosoId(), "FreezeMembership:::error." +freezeMembershipInput.getMosoId(), freezeMembershipInput.getThreadId(),
					correlationId, freezeMembershipInput, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}

	}
	
	@ApiOperation(value = "Freeze Membership", notes = "Freeze Membership")
	@RequestMapping(method = RequestMethod.POST, value = "/v4/freeze-membership/")
	public @ResponseBody ResponseEntity<FreezeMemberResponse> freezeMembershipV4(@RequestBody FreezeMembershipInputV2 input) throws Exception {
		//Declaration
		logger.debug("Freeze membership for member: {} and source: {}", input.getMosoId(),input.getSource());
		FreezeMemberResponse response = new FreezeMemberResponse();
		long startTime = Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;
		input.setCorrelationId(correlationId);
		
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID + input.getMosoId(), "FreezeMembership:::" +input.getMosoId(), input.getThreadId(),
				correlationId, input, "Logging initial HG request" ,HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		// Validations
		if (StringUtils.isBlank(input.getMosoId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.name());
		}
		if (StringUtils.isBlank(input.getFreezeReasonId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.getCode(),
					FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.name());
		}
		if (input.getStartDate() == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.START_DATE_EMPTY.getCode(),
					FreezeServiceExceptionEnum.START_DATE_EMPTY.name());
		}
		if (input.getEndDate() == null && input.getDurationMonths() <= 0) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION,
					FreezeServiceExceptionEnum.END_DATE_AND_MONTH_DURATION_BLANK.getCode(),
					FreezeServiceExceptionEnum.END_DATE_AND_MONTH_DURATION_BLANK.name());
		}
		if (StringUtils.isBlank(input.getSource())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.SOURCE_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.SOURCE_REQUIRED.name());
		}
		if(Objects.isNull(input.getFreezeFees()) && input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_MEDICAL) || input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_PREGNANCY)) {
			input.setFreezeFees(BigDecimal.ZERO);
		}
		if(Objects.isNull(input.getFreezeFees()) && input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_FEE_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.FREEZE_FEE_REQUIRED.name());
		}		
		//Check for existing freeze 
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(input.getMosoId());
		if (memberAgreementDetail == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode(),
					FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.name());
		}
		
		String memberId = input.getMosoId();
		int countryCode = 0;
		String key = "";		
		String emailAddress = "";
		BigDecimal freezeFees = Objects.isNull(input.getFreezeFees()) ? BigDecimal.ZERO : input.getFreezeFees();
		
		//Service to get member  
		Map<String, String> urlVariables = new HashMap<>();
		urlVariables.put("categories", "membership,profile");
		Member member = redisApiService.getMemberV2SpecificCategories(memberId, urlVariables);
		
		if(Objects.isNull(member) || Objects.isNull(member.getMembership()) || StringUtils.isEmpty(member.getMembership().getCountryCode())) {
			logger.error("Missing redis globey membership,profile data for member: {}", memberId);
			return restResponse.logAndPopulateFailureResponseInDynamo("FreezeMembership#"+memberId, input, response, "",
					correlationId, ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.getCode(),
					FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.name(), key, startTime);
		}
		
		String country = member.getMembership().getCountryCode();
		countryCode = CountryCode.valueOf(country).intValue();
		emailAddress = getEmailId(input, member);
		if (!StringUtils.isBlank(memberAgreementDetail.getFreezeStatus()) && !StringUtils.isBlank(input.getSource()) && input.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) &&
				(memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("Active") || memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("PENDING START"))) {
			FreezeExtensionOutput freezeExtensionOutput = v4FreezeExtension(memberAgreementDetail, input,emailAddress);
			if(Objects.nonNull(freezeExtensionOutput)) {
				if (freezeExtensionOutput.getMessages() != null && freezeExtensionOutput.getMessages()[0].getMessageID() == 201) {
					response.setMessages(freezeExtensionOutput.getMessages());
					return restResponse.populateSuccessResponse(response, HttpStatus.OK);
				}else {
					getFreezeMemberResposeV4(freezeExtensionOutput, response);
					return restResponse.populateSuccessResponse(response, HttpStatus.OK);
				}
			}
		}
		// Apply Freeze suspension in MOSO		
		response = freezeService.freezeMembershipV4(input, input.isWaveFreezeFee(), countryCode, emailAddress, freezeFees,memberAgreementDetail);
		if (response.getMessages() == null) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID + input.getMosoId(), "FreezeMembership:::error." +input.getMosoId(), input.getThreadId(),
					correlationId, input, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}
	}
	
	
	@ApiOperation(value = "Freeze Membership", notes = "Freeze Membership")
	@RequestMapping(method = RequestMethod.POST, value = "/v5/freeze-membership/")
	public @ResponseBody ResponseEntity<FreezeMemberResponse> freezeMembershipV5(@RequestBody FreezeMembershipInputV4 input) throws Exception {
		//Declaration
		logger.debug("Freeze membership for member: {} and source: {}", input.getMosoId(),input.getSource());
		FreezeMemberResponse response = new FreezeMemberResponse();
		long startTime = Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;
		input.setCorrelationId(correlationId);
	
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID + input.getMosoId(), "FreezeMembership:::" +input.getMosoId(), input.getThreadId(),
				correlationId, input, "Logging initial HG request" ,HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		// Validations
		if (StringUtils.isBlank(input.getMosoId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.name());
		}
		if (StringUtils.isBlank(input.getFreezeReasonId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.getCode(),
					FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.name());
		}
		if (input.getStartDate() == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.START_DATE_EMPTY.getCode(),
					FreezeServiceExceptionEnum.START_DATE_EMPTY.name());
		}
		if (input.getEndDate() == null && (input.getDurationMonths() <= 0 && input.getDurationDays() <=0)) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION,
					FreezeServiceExceptionEnum.EITHER_DURATION_DAYS_OR_DURATION_MONTHS_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.EITHER_DURATION_DAYS_OR_DURATION_MONTHS_REQUIRED.name());
		}
		if (StringUtils.isBlank(input.getSource())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.SOURCE_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.SOURCE_REQUIRED.name());
		}
		if(Objects.isNull(input.getFreezeFees()) && input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_MEDICAL) || input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_PREGNANCY)) {
			input.setFreezeFees(BigDecimal.ZERO);
		}
		if(Objects.isNull(input.getFreezeFees()) && input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_FEE_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.FREEZE_FEE_REQUIRED.name());
		}		
		//Check for existing freeze 
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(input.getMosoId());
		if (memberAgreementDetail == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode(),
					FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.name());
		}
		
		String memberId = input.getMosoId();
		int countryCode = 0;
		String key = "";		
		String emailAddress = "";
		BigDecimal freezeFees = Objects.isNull(input.getFreezeFees()) ? BigDecimal.ZERO : input.getFreezeFees();
		
		//Service to get member  
		Map<String, String> urlVariables = new HashMap<>();
		urlVariables.put("categories", "membership,profile");
		Member member = redisApiService.getMemberV2SpecificCategories(memberId, urlVariables);
		
		if(Objects.isNull(member) || Objects.isNull(member.getMembership()) || StringUtils.isEmpty(member.getMembership().getCountryCode())) {
			logger.error("Missing redis globey membership,profile data for member: {}", memberId);
			return restResponse.logAndPopulateFailureResponseInDynamo("FreezeMembership#"+memberId, input, response, "",
					correlationId, ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.getCode(),
					FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.name(), key, startTime);
		}
		
		String country = member.getMembership().getCountryCode();
		countryCode = CountryCode.valueOf(country).intValue();
		emailAddress = getEmailIdV5(input, member);
		if (!StringUtils.isBlank(memberAgreementDetail.getFreezeStatus()) && !StringUtils.isBlank(input.getSource()) && input.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) &&
				(memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("Active") || memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("PENDING START"))) {
			FreezeExtensionOutput freezeExtensionOutput = v5FreezeExtension(memberAgreementDetail, input, emailAddress);
			if(Objects.nonNull(freezeExtensionOutput)) {
				if (freezeExtensionOutput.getMessages() != null && freezeExtensionOutput.getMessages()[0].getMessageID() == 201) {
					response.setMessages(freezeExtensionOutput.getMessages());
					return restResponse.populateSuccessResponse(response, HttpStatus.OK);
				}else {
					getFreezeMemberResposeV4(freezeExtensionOutput, response);
					return restResponse.populateSuccessResponse(response, HttpStatus.OK);
				}
			}
		}
		// Apply Freeze suspension in MOSO		
		response = freezeService.freezeMembershipV5(input, input.isWaveFreezeFee(), countryCode, emailAddress, freezeFees,memberAgreementDetail);
		if (response.getMessages() == null) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID + input.getMosoId(), "FreezeMembership:::error." +input.getMosoId(), input.getThreadId(),
					correlationId, input, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}
	}
	
	private FreezeExtensionOutput v5FreezeExtension(MemberAgreementDetail memberAgreementDetail, FreezeMembershipInputV4 input, String emailAddress) {
		/* freeze extension is allowed only on active/Pending freeze agreements */
		Date existingFreezeEndDate = memberAgreementDetail.getFreezeEndTime();
		Date newFreezeStartDate = input.getStartDate();

		// Add one day to existing end date and then compare new start date 
		Calendar cal = Calendar.getInstance();
		cal.setTime(existingFreezeEndDate);
		cal.add(Calendar.DATE, 1);
		Date newExistingFreezeEndDate = cal.getTime();

		logger.info("Existing freeze end={}", existingFreezeEndDate);
		logger.info("New freeze end={}", newExistingFreezeEndDate);
		logger.info("New freeze start date={}", newFreezeStartDate);

		//If new freeze start date is = or before existing freeze end date then create freeze extension 
		if(getZeroTimeDate(newFreezeStartDate).before(getZeroTimeDate(newExistingFreezeEndDate)) || 
				getZeroTimeDate(newFreezeStartDate).equals(getZeroTimeDate(newExistingFreezeEndDate))) {
			String  eclubFacilityID = memberAgreementDetail.getHomeFacilityId();
			String accountingCode = facilityConversion.facilityConversion(eclubFacilityID);
			FreezeExtensionInputV3 freezeExtensionInputV3 = new FreezeExtensionInputV3();
			freezeExtensionInputV3.setFacilityId(accountingCode);
			freezeExtensionInputV3.setMosoId(input.getMosoId());
			freezeExtensionInputV3.setExtensionDurationMonths(input.getDurationMonths());
			Date freezeExtensionEndDate=cal.getTime();
			if (input.getEndDate() == null) {
				Date startDate = input.getStartDate();
				cal = Calendar.getInstance();
				cal.setTime(startDate);
				if (input.getDurationDays() > 0 ) {
					cal.add(Calendar.DAY_OF_MONTH, input.getDurationDays());
				}else if (input.getDurationMonths() > 0) {
					cal.add(Calendar.MONTH, input.getDurationMonths());
				}
				freezeExtensionEndDate=cal.getTime();
				input.setEndDate(cal.getTime());
			}			
			logger.info("New freeze extension end={}",freezeExtensionEndDate);
			freezeExtensionInputV3.setFreezeExtensionEndDate(freezeExtensionEndDate);
			freezeExtensionInputV3.setWaiveOffExtensionFee(input.isWaveFreezeFee());
			freezeExtensionInputV3.setFreezeReason(input.getFreezeReasonId());
			freezeExtensionInputV3.setThreadId(input.getThreadId());
			freezeExtensionInputV3.setCorrelationId(input.getCorrelationId());
			freezeExtensionInputV3.setCaseOrigin(input.getCaseOrigin());
			FreezeExtensionOutput freezeExtensionOutput = freezeService.freezeExtensionV5(freezeExtensionInputV3, memberAgreementDetail, input, emailAddress);
			return freezeExtensionOutput;
		}
		return null;
	}

	private FreezeExtensionOutput v4FreezeExtension(MemberAgreementDetail memberAgreementDetail, FreezeMembershipInputV2 input, String emailAddress) {
		/* freeze extension is allowed only on active/Pending freeze agreements */
		Date existingFreezeEndDate = memberAgreementDetail.getFreezeEndTime();
		Date newFreezeStartDate = input.getStartDate();

		// Add one day to existing end date and then compare new start date 
		Calendar cal = Calendar.getInstance();
		cal.setTime(existingFreezeEndDate);
		cal.add(Calendar.DATE, 1);
		Date newExistingFreezeEndDate = cal.getTime();

		logger.info("Existing freeze end={}", existingFreezeEndDate);
		logger.info("New freeze end={}", newExistingFreezeEndDate);
		logger.info("New freeze start date={}", newFreezeStartDate);

		//If new freeze start date is = or before existing freeze end date then create freeze extension 
		if(getZeroTimeDate(newFreezeStartDate).before(getZeroTimeDate(newExistingFreezeEndDate)) || 
				getZeroTimeDate(newFreezeStartDate).equals(getZeroTimeDate(newExistingFreezeEndDate))) {
			String  eclubFacilityID = memberAgreementDetail.getHomeFacilityId();
			String accountingCode = facilityConversion.facilityConversion(eclubFacilityID);
			FreezeExtensionInputV2 freezeExtensionInputV2 = new FreezeExtensionInputV2();
			freezeExtensionInputV2.setFacilityId(accountingCode);
			freezeExtensionInputV2.setMosoId(input.getMosoId());
			freezeExtensionInputV2.setExtensionDurationMonths(input.getDurationMonths());
			Date freezeExtensionEndDate=cal.getTime();
			if (input.getEndDate() == null && input.getDurationMonths() > 0) {
				Date startDate = input.getStartDate();
				cal = Calendar.getInstance();
				cal.setTime(startDate);
				cal.add(Calendar.MONTH, input.getDurationMonths());
				freezeExtensionEndDate=cal.getTime();
				input.setEndDate(cal.getTime());
			}			
			logger.info("New freeze extension end={}",freezeExtensionEndDate);
			freezeExtensionInputV2.setFreezeExtensionEndDate(freezeExtensionEndDate);
			freezeExtensionInputV2.setWaiveOffExtensionFee(input.isWaveFreezeFee());
			freezeExtensionInputV2.setFreezeReason(input.getFreezeReasonId());
			freezeExtensionInputV2.setThreadId(input.getThreadId());
			freezeExtensionInputV2.setCorrelationId(input.getCorrelationId());
			FreezeExtensionOutput freezeExtensionOutput = freezeService.freezeExtensionV4(freezeExtensionInputV2, memberAgreementDetail, input, emailAddress);
			return freezeExtensionOutput;
		}
		return null;
	}

	@ApiOperation(value = "Get Freeze Reason", notes = "Get Freeze Reason")
	@RequestMapping(method = RequestMethod.GET, value = "/v2/freeze-info/{mosoId}/")
	public @ResponseBody ResponseEntity<RetrieveFreezeReasonOutput> retrieveFreezeReasonV2(
			@PathVariable(name = "mosoId") String mosoId,
			@RequestParam(required = true, value = "freezeReason") String freezeReason,
			@RequestParam(required = true, value = "duration") int duration) throws Exception {

		// Declaration
		RetrieveFreezeReasonOutput response = new RetrieveFreezeReasonOutput();
		long startTime=Calendar.getInstance().getTimeInMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;
		
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "Retrieve-Freeze-Reason:::" +mosoId, correlationId,
				correlationId, "FreezeReason : "+freezeReason+" Duration :"+duration, "Logging request" ,HttpStatus.CREATED, System.currentTimeMillis() - startTime);
		
		logger.debug("Calling Service");
		// Validations
		if (StringUtils.isBlank(mosoId)) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.name());
		}
		if (StringUtils.isBlank(freezeReason)) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.getCode(),
					FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.name());
		}
		if (duration <= 0) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_DURATION_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.FREEZE_DURATION_REQUIRED.name());
		}
		response = freezeService.retrieveFreezeReason(mosoId, freezeReason, duration);
		if (response.getMessages() == null) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "Retrieve-Freeze-Reason:::" +mosoId, correlationId,
					correlationId, "FreezeReason : "+freezeReason+" Duration :"+duration, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		}
	}
	
	@ApiOperation(value = "Freeze Agreement", notes = "Freeze Agreement")
	@RequestMapping(method = RequestMethod.POST, value = "/v2/agreements/freeze")
	public ResponseEntity<FreezeMemberResponse> freezeAgreementsV3(@RequestBody 
			FreezeMembershipInputV3 freezeMemberShipInput) {
		
		logger.info("Inside Freeze Extension V3");
		long startTime=Calendar.getInstance().getTimeInMillis();
		FreezeMemberResponse response = new FreezeMemberResponse();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;
		freezeMemberShipInput.setCorrelationId(correlationId);
		
		
		if (StringUtils.isBlank(freezeMemberShipInput.getMosoId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.MOSO_ID_REQUIRED.name());
		}
		
		if (StringUtils.isBlank(freezeMemberShipInput.getFacilityId())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FACILITYID_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.FACILITYID_REQUIRED.name());
		}
		
		if(freezeMemberShipInput.getRequestedDate() == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.REQUESTED_DATE_IS_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.REQUESTED_DATE_IS_REQUIRED.name());
		}
		
		if(freezeMemberShipInput.getClubOpenDate() == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.CLUB_OPEN_DATE_IS_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.CLUB_OPEN_DATE_IS_REQUIRED.name());
		}
		
		if (StringUtils.isBlank(freezeMemberShipInput.getFreezeReason())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.getCode(),
					FreezeServiceExceptionEnum.FREEZE_REASON_BLANK.name());
		}
		if (StringUtils.isBlank(freezeMemberShipInput.getSource())) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.SOURCE_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.SOURCE_REQUIRED.name());
		}
		
		///////New check//////////
		//Check for existing freeze 
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(freezeMemberShipInput.getMosoId());
		if (memberAgreementDetail == null) {
			return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode(),
					FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.name());
	
		}
		/* freeze extension is allowed only on active/Pending freeze agreements */
		
		if (!StringUtils.isBlank(memberAgreementDetail.getFreezeStatus()) &&( memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("Active")
				|| memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("PENDING START"))) {
			
			Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
			Date freezeExtensionEndDate = freezeMemberShipInput.getFreezeEndDate() == null ? freezeMemberShipInput.getClubOpenDate():freezeMemberShipInput.getFreezeEndDate();
			logger.info("Freeze End Date ={}",freezeExtensionEndDate);
			logger.info("Calculating End Date");
			if(freezeMemberShipInput.getFreezeEndDate() == null) {
				logger.info("Calculating End Date");
				cal.setTime(freezeExtensionEndDate);
				cal.add(Calendar.MONTH, 2);
				cal.set(Calendar.DAY_OF_MONTH, 1);
			}else {
				cal.setTime(freezeExtensionEndDate);
			}
			freezeExtensionEndDate = cal.getTime();
			freezeMemberShipInput.setFreezeEndDate(freezeExtensionEndDate);
			
			
			Date existingFreezeEndDate=memberAgreementDetail.getFreezeEndTime();
			Date newFreezeStartDate=freezeMemberShipInput.getClubOpenDate();
			
			// Add one day to existing end date and then compare new start date 
			cal = Calendar.getInstance();
			cal.setTime(existingFreezeEndDate);
			cal.add(Calendar.DATE, 1);
			Date newExistingFreezeEndDate=cal.getTime();
			
			logger.info("Existing freeze end={}",existingFreezeEndDate);
			logger.info("New freeze end={}",newExistingFreezeEndDate);
			logger.info("New freeze start date={}",newFreezeStartDate);
			
			//logger.info("newFreezeStartDate.before(newExistingFreezeEndDate) ={}",getZeroTimeDate(newFreezeStartDate).before(getZeroTimeDate(newExistingFreezeEndDate) ));
			//logger.info("newFreezeStartDate.equals(newExistingFreezeEndDate)={}",newFreezeStartDate.equals(newExistingFreezeEndDate));
			
			//If new freeze start date is = or before existing freeze end date then create freeze extension 
			if(getZeroTimeDate(newFreezeStartDate).before(getZeroTimeDate(newExistingFreezeEndDate)) ||getZeroTimeDate(newFreezeStartDate).equals(getZeroTimeDate(newExistingFreezeEndDate))){
				String  eclubFacilityID= memberAgreementDetail.getHomeFacilityId();
				String accountingCode = facilityConversion.facilityConversion(eclubFacilityID);
				FreezeExtensionInputV2 freezeExtensionInputV2=new FreezeExtensionInputV2();
				freezeExtensionInputV2.setFacilityId(accountingCode);
				freezeExtensionInputV2.setMosoId(freezeMemberShipInput.getMosoId());
				//freezeExtensionInputV2.setExtensionDurationMonths(freezeMembershipInput.getDurationMonths());
				
				logger.info("New freeze extension end={}",freezeExtensionEndDate);
				freezeExtensionInputV2.setFreezeExtensionEndDate(freezeExtensionEndDate);
				freezeExtensionInputV2.setWaiveOffExtensionFee(freezeMemberShipInput.isWaveFreezeFee());
				freezeExtensionInputV2.setFreezeReason(freezeMemberShipInput.getFreezeReason());
				freezeExtensionInputV2.setThreadId(freezeMemberShipInput.getThreadId());
				freezeExtensionInputV2.setCorrelationId(freezeMemberShipInput.getCorrelationId());
			
				FreezeExtensionOutput out = freezeService.freezeExtensionV2(freezeExtensionInputV2,memberAgreementDetail);
				if (out.getMessages() != null && out.getMessages()[0].getMessageID() == 201) {
					response.setMessages(out.getMessages());
					return restResponse.populateSuccessResponse(response, HttpStatus.OK);
				} else if (out.getMessages() != null && out.getMessages()[0].getMessageID() != 201) {
					response.setMessages(out.getMessages());
					eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInputV2.getMosoId(), "FreezeExtension:::error." +freezeExtensionInputV2.getMosoId(), freezeExtensionInputV2.getThreadId(),
							correlationId, freezeExtensionInputV2, response.getMessages()[0].getFriendlyMessage() ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
					
					return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
				} else {
					getFreezeMemberRespose(out,response);
					return restResponse.populateSuccessResponse(response, HttpStatus.OK);
				}
				
            }
		}


		
		response = freezeService.freezeMembershipV3(freezeMemberShipInput);
		// For creating case in SF
		if (response.getMessages() != null && response.getMessages()[0].getMessageID() == 201) {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		} else if (response.getMessages() != null && response.getMessages()[0].getMessageID() != 201) {
			return restResponse.populateSuccessResponse(response, HttpStatus.BAD_REQUEST);
		} else {
			return restResponse.populateSuccessResponse(response, HttpStatus.OK);
		}
	}
	
	@DeleteMapping(value = "/v1/{salesForceId}", produces = {"application/json"})
	@ApiOperation(value = "Unfreeze's member agreement", notes = "Unfreeze's member agreement")
	public ResponseEntity<UnfreezeMembershipResponse> unfreezeMember(@PathVariable(required = true) String salesForceId, 
			@RequestParam(required = true) String ipAddress) {
		logger.info("DELETE Request /freeze/v1{salesForceId}?ipAddress for salesForceId {}", salesForceId);
		long startTime = System.currentTimeMillis();
		String correlationId = httpServletRequest.getHeader("Correlation-ID");
		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;
		
		// @RequestParam required true not validating empty string(MD-12184)
		if(StringUtils.isEmpty(ipAddress)) {
			return restResponse.populateFailureResponse(new UnfreezeMembershipResponse(), HttpStatus.BAD_REQUEST,
					ExceptionMessageEnum.VALIDATION, FreezeServiceExceptionEnum.IP_ADDRESS_REQUIRED.getCode(),
					FreezeServiceExceptionEnum.IP_ADDRESS_REQUIRED.name());
		}
		
		String memberId = sdfcService.getMemberId(salesForceId);
		
		UnfreezeMembershipResponse response = unfreezeService.unFreezeMember(memberId, ipAddress);
		
		if(Objects.nonNull(response.getMessages())) {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+memberId, "UnfreezeMembership:::error." +memberId, "",
					correlationId, memberId + " " + ipAddress, response ,
					HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
		}
			
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+memberId, "UnfreezeMembership:::success." +memberId, "",
				correlationId, memberId + " " + ipAddress, response ,
				HttpStatus.OK, System.currentTimeMillis() - startTime);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * 
	 * @param request
	 * @return
	 */
    @PutMapping(value="/v1/clubapp/unfreeze/{mosoId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Submit Unfreeze case in Salesforce", notes = "Submit Unfreeze case in Salesforce")
    public ResponseEntity<Object> unFreezeInSalesforce(@PathVariable(required = true) String mosoId) {
        logger.info("ClubApp Unfreeze HG Service /freeze/v1/clubapp/unfreeze/{}", mosoId);
        String correlationId = httpServletRequest.getHeader("Correlation-ID");
		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;
        long startTime = System.currentTimeMillis();
        
        ApiResponse<Object> response = unfreezeService.unFreezeCaseInSalesforce(mosoId);
        
        if(Objects.nonNull(response.getMessages())) {
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "UnfreezeCase:::error." +mosoId, "",correlationId, mosoId, response ,
					HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);
			response.getMessages()[0].setFriendlyMessage("Can't request unfreeze for "+mosoId);
			return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
		}
			
		eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "UnfreezeCase:::success." +mosoId, "",correlationId, mosoId, response ,
				HttpStatus.OK, System.currentTimeMillis() - startTime);
		return new ResponseEntity<>(response, HttpStatus.OK);      
    }
    /**
     * 
     * @param freezeEmailInput
     * @return
     * @throws ParseException 
     */
    @PostMapping(value="/v1/clubapp/freeze/email", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Send freeze link to member from clubapp", notes = "Send freeze link to member from clubapp")
    public ResponseEntity<Object>sendMemberFreezeEmail(@RequestBody FreezeEmailInput freezeEmailInput) {
    	logger.info("Send freeze link to member from clubapp {}", freezeEmailInput);
    	ApiResponse<Object> response = new ApiResponse<>();
    	try {
    		String correlationId = httpServletRequest.getHeader("Correlation-ID");
    		correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;      		    		
    		FreezeEmailIOutput output = freezeService.sendMemberFreezeEmail(freezeEmailInput,correlationId); 
    		response.setResult(output);
    		if(Objects.nonNull(response.getMessages())) {
    			return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    		}
    	}catch (DataFetchException e) {
    		throw new DataFetchException(e.getMessage(), e.getStatus());
    	}
    	return new ResponseEntity<>(response, HttpStatus.OK);      
    }  
    
    @PostMapping(value="/v1/clubapp/freeze/case", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Creating member freeze case on SalesForce", notes = "Creating member freeze case on SalesForce")
    public ResponseEntity<Object>createMemberFreezeCase(@RequestBody MemberFreezeCase memberFreezeCase) {
    	logger.info("Creating member freeze case on SalesForce {}", memberFreezeCase);
    	ApiResponse<Object> response = new ApiResponse<>();
    	MemberFreezeCaseOutput output = freezeService.createMemberFreezeCase(memberFreezeCase); 
    	response.setResult(output);
    	if(Objects.nonNull(response.getMessages())) {
    		return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    	}
    	return new ResponseEntity<>(response, HttpStatus.OK);      
    }  
    /**
     * Send freeze link to member from engage
     * @param freezeEmailInput
     * @return
     * @throws ParseException 
     */
    @PostMapping(value="/v1/concierge/freeze/email", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Send freeze link to member from engage", notes = "Send freeze link to member from engage")
    public ResponseEntity<EngageFreezeEmailIOutput> sendMemberFreezeEmailFromEngage(@RequestBody FreezeEmailInput freezeEmailInput) {
        logger.info("Send freeze link to member from engage {}", freezeEmailInput);
        EngageFreezeEmailIOutput response = new EngageFreezeEmailIOutput();
        try {
        	FreezeServiceExceptionEnum FreezeServiceException = freezeRequestValidator.memberFreezeEmailValidate(freezeEmailInput);
        	if (FreezeServiceException != null) {
        		return restResponse.populateFailureResponse(response, HttpStatus.BAD_REQUEST,
        				ExceptionMessageEnum.VALIDATION, FreezeServiceException.getCode(),
        				FreezeServiceException.name());
        	}
        	String correlationId = httpServletRequest.getHeader("Correlation-ID");
        	correlationId = StringUtils.isBlank(correlationId) ? UUID.randomUUID().toString() : correlationId;            	
        	response = freezeService.sendFreezeEmailLink(freezeEmailInput,correlationId); 
        	if(Objects.nonNull(response.getMessages())) {
        		return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        	}
        }catch (DataFetchException e) {
            throw new DataFetchException(e.getMessage(), e.getStatus());
        }
        return new ResponseEntity<>(response, HttpStatus.OK);      
    }  
    
	
	private FreezeMemberResponse getFreezeMemberRespose(FreezeExtensionOutput out, FreezeMemberResponse response) {
		if(out.getMessages()!=null )
		response.setMessages(out.getMessages());
		response.setFreezeExtensionEndDate(out.getFreezeExtensionEndDate());
		response.setFreezeExtensionStartDate(out.getFreezeExtensionStartDate());
		response.setFreezeFeeAmount(out.getFreezeFeeAmount());
		response.setFreezeFeeItem(out.getFreezeFeeItem());
		response.setFreezeFeeStartDate(out.getFreezeFeeStartDate());
		response.setFreezeStatus(out.getFreezeStatus());
		response.setMemberFirstName(out.getMemberFirstName());
		response.setMemberLastName(out.getMemberLastName());
		response.setSuspensionId(out.getSuspensionId());
		response.setSuspendedAgreementName(out.getSuspendedAgreementName());
		response.setSuspensionReasonName(out.getSuspensionReasonName());
		
		return response;
	}
	
	private FreezeMemberResponse getFreezeMemberResposeV4(FreezeExtensionOutput out, FreezeMemberResponse response) {
		if(out.getMessages() != null)
			response.setMessages(out.getMessages());
		response.setRequestReceivedSuccessfully(true);
		response.setFreezeExtensionEndDate(out.getFreezeExtensionEndDate());
		response.setFreezeExtensionStartDate(out.getFreezeExtensionStartDate());
		response.setFreezeFeeAmount(out.getFreezeFeeAmount());
		response.setFreezeFeeItem(out.getFreezeFeeItem());
		response.setFreezeFeeStartDate(out.getFreezeFeeStartDate());
		response.setFreezeStatus(out.getFreezeStatus());
		response.setMemberFirstName(out.getMemberFirstName());
		response.setMemberLastName(out.getMemberLastName());
		response.setSuspensionId(out.getSuspensionId());
		response.setSuspendedAgreementName(out.getSuspendedAgreementName());
		response.setSuspensionReasonName(out.getSuspensionReasonName());
		return response;
	}
	
	public static Date getZeroTimeDate(Date fecha) {
	    Date res = fecha;
	    Calendar calendar = Calendar.getInstance();

	    calendar.setTime( fecha );
	    calendar.set(Calendar.HOUR_OF_DAY, 0);
	    calendar.set(Calendar.MINUTE, 0);
	    calendar.set(Calendar.SECOND, 0);
	    calendar.set(Calendar.MILLISECOND, 0);

	    res = calendar.getTime();

	    return res;
	}
	
	private String getEmailId(FreezeMembershipInputV2 input, Member member) {
		String email = "";
		if(!StringUtils.isBlank(input.getEmailAddress()))
			email = input.getEmailAddress();
		else
			if(member.getProfile() != null && member.getProfile().getEmail() != null)
				email = member.getProfile().getEmail();
		return email;
	}
	
	private String getEmailIdV5(FreezeMembershipInputV4 input, Member member) {
		String email = "";
		if(!StringUtils.isBlank(input.getEmailAddress()))
			email = input.getEmailAddress();
		else
			if(member.getProfile() != null && member.getProfile().getEmail() != null)
				email = member.getProfile().getEmail();
		return email;
	}
}
