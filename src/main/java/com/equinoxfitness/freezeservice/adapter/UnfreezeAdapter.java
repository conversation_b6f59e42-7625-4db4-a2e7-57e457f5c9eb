package com.equinoxfitness.freezeservice.adapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.equinoxfitness.freezeservice.contract.moso.MosoApiExceptionResponse;
import com.equinoxfitness.freezeservice.contract.moso.UnFreezeRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.gson.Gson;

@Component
public class UnfreezeAdapter {

	@Value("${unfreeze.moso.frezeeEnd.url}")
	private String unFreezeMosoURI;

	private RestTemplate restTemplate = new RestTemplate();

	private static final Logger logger = LoggerFactory.getLogger(UnfreezeAdapter.class);

	public boolean unFreezeMembership(UnFreezeRequest request, String authToken, String cookieValue) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", cookieValue);
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		HttpEntity<UnFreezeRequest> unFreezeRequest = new HttpEntity<>(request, entity.getHeaders());
		String unFreezeURL = unFreezeMosoURI + "?authToken=" + authToken;

		try {
			ObjectMapper mapper = new ObjectMapper();
			mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
			String unfreezeMembershipReq = mapper.writeValueAsString(unFreezeRequest);
			logger.info("Unfreeze membership {}", unfreezeMembershipReq);
		} catch (JsonProcessingException e) {
			logger.info("Exception occured!!");
			e.printStackTrace();
		}

		try {
			restTemplate.exchange(unFreezeURL, HttpMethod.PUT, unFreezeRequest, Object.class);
			return true;
		} catch (HttpStatusCodeException e) {
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			logger.error("Failed to end freeze for freezeId {} {}", request.getFreezeId(), mosoError.getErrorMessage());
		} catch (RestClientException e) {
			logger.error("Failed to end freeze for freezeId {} bcz {}", request.getFreezeId(), e.getMessage());
		}
		return false;
	}

}