/**
 * 
 */
package com.equinoxfitness.freezeservice.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.equinoxfitness.freezeservice.dvo.CorpAgreementDetail;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;

/**
 * <AUTHOR>
 *
 */
public interface FreezeServiceDAO {

	public MemberAgreementDetail getMemberAgreementDetail(String memberId);

	public String getSuspensionReasonId(String suspensionName);
	
	public boolean storeEmailDetailsForMedicalPreganancyFreeze(String memberId,  String memberName, String memberEmail, 
			Date freezeStartDate, int freezeDuration, String freezeReason, String scheduledTimeToEmail);

	public CorpAgreementDetail getCorpAgreementDetail(String mosoId);

	public List<MemberAgreementDetail> getMemberAgreementDetailList(String mosoId);

	public BigDecimal getMosoMemberAccountBalance(String mosoId);

	public boolean storeEmailDetailsForMedicalPreganancyFreezeV4(String mosoId, String firstName, String email,
			Date startDate, int durationMonths, String freezeReasonId, String scheduledTimeToEmail, Date endDate, String countryCode, boolean pilotFreezeMedFee);

}
