package com.equinoxfitness.freezeservice.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Repository;

import com.equinoxfitness.freezeservice.contract.FundingSource;
import com.equinoxfitness.freezeservice.dao.CardOnFileDao;

@Repository
public class CardOnFileDaoImpl implements CardOnFileDao {
	private static final Logger logger = LoggerFactory.getLogger(CardOnFileDaoImpl.class);
	
	@Autowired
	@Qualifier("getCOFSimpleJdbcCall")
	private SimpleJdbcCall getCOFSimpleJdbcCall;
	
	@SuppressWarnings("unchecked")
	@Override
	public FundingSource getFundingSource(String memberId) {
		logger.debug("getFundingSource for memberId {}", memberId);
		List<FundingSource> fundingSources = null;

		try {
			SqlParameterSource in = new MapSqlParameterSource().addValue("memberId", memberId);
			Map<String, Object> simpleJdbcCallResult = getCOFSimpleJdbcCall.execute(in);
			
			fundingSources = (List<FundingSource>) simpleJdbcCallResult.get("fundingSource");
		} catch (EmptyResultDataAccessException e) {
			logger.error(e.getMessage());
		} catch (Exception e2) {
			logger.error(e2.getMessage());
		}
		
		return fundingSources == null || fundingSources.size() == 0 ? new FundingSource() : fundingSources.get(0);
	}
}
