/**
 * 
 */
package com.equinoxfitness.freezeservice.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.CorpAgreementDetail;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;

/**
 * <AUTHOR>
 *
 */
@Repository
public class FreezeServiceDAOImpl implements FreezeServiceDAO {
	private static final Logger logger = LoggerFactory.getLogger(FreezeServiceDAOImpl.class);

	@Autowired
	@Qualifier("mosoJdbcTemplate")
	JdbcTemplate mosoJdbcTemplate;
	
	@Autowired
	@Qualifier("webDatabaseJdbcTemplate")
	JdbcTemplate webDatabaseJdbcTemplate;

	@Value("${sql.email.freeze.medical.pregnancy.store.webdb}")
	private String emailMedicalPregnancyFreezeStoreQuery;

	@Override
	public MemberAgreementDetail getMemberAgreementDetail(String memberId) {

		logger.debug("Getting member agreementDetail from tenant");
		String query = "select top 1 cast(PR.ROLEID AS varchar) memberID, pprv.[First Name], pea.[EmailAddress],\r\n"
				+ "case when BU.DivisionId = 1 then '1' when BU.DivisionId = 2 then '5' when BU.DivisionId = 3 then '6' END as countryCode,\r\n"
				+ "case when MAIPS.paymentvaluetype = 2 and MAIPS.paymentvalue = 1 then 'Payroll Deducted' when tt.TenderTypeID='103' then 'Credit Card' when tt.TenderTypeID='104' then 'EFT' when tt.TenderTypeID='909' then 'EFT' when tt.TenderTypeID='916' then 'EFT' when tt.TenderTypeID is null then 'Direct Bill' end as Billingmethod,\r\n"
				+ "case PRS.[Status]when 1 then 'Active' when 2 then 'Inactive' when 3 then 'On Hold' when 4 then 'On Freeze' END as memberStatus,\r\n"
				+ "cast(BU.code AS varchar) homeFacilityId, case BU.[DivisionId] when 1 then 'US' when 2 then 'UK' when 3 then 'CA' END as countryCode,\r\n"
				+ "MA.AgreementId as agreementId, MA.MemberAgreementId as memberAgreementId,\r\n"
				+ "case MA.[Status] when 0 then 'Inactive'when 1 then 'NonFinalized' when 2 then 'Processing' when 4 then 'Pending' when 5 then 'Active' when 6 then 'Expired' when 7 then 'Cancelled' when 8 then 'Delinquent' when 9 then 'Depleted' when 10 then 'On Hold' when 11 then 'On Freeze' when 12 then 'Terminated' END as contractStatus,\r\n"
				+ "MA.StartDate as startDate, MA.EndDate as endDate, MA.AGREEMENTTERMID as agreementTermId, MA.ObligationDate as obligationDate,\r\n"
				+ "CA.Date as cancellationDate, CA.StateId as cancellationStateId, CASE s.[Status] WHEN 0 THEN 'Invalid' WHEN 1 THEN 'Pending Start' WHEN 2 THEN 'Active' WHEN 3 THEN 'Expired' WHEN 4 THEN 'Rescinded' WHEN 5 THEN 'Pending Authorization' WHEN 6 THEN 'Denied' END AS freezeStatus, cast(sr.Name AS varchar) freezeReason,\r\n"
				+ "s.BeginTime AS freezeStartTime, sed.EndTime AS freezeEndTime, a.Name AS membershipClass, case MA.[AgreementTermId] when 1 then 'Fixed' when 2 then 'perpetual' when 3 then 'installments' END AS agreementType, s.suspensionid as FreezeId, ma.clientaccountid as clientAccountId from PARTYROLE PR LEFT OUTER JOIN PARTYROLESTATUS PRS ON PRS.PARTYROLEID = PR.PARTYROLEID LEFT OUTER JOIN PartyEmailAddresses pea on pea.PartyRoleId=PR.PARTYROLEID RIGHT OUTER JOIN MEMBERAGREEMENT MA on Ma.PartyRoleId=PR.PartyRoleId LEFT OUTER JOIN BUSINESSUNIT BU ON BU.BUSINESSUNITID = MA.BUSINESSUNITID LEFT OUTER JOIN Agreement A on MA.AgreementId=A.AgreementId LEFT OUTER JOIN Cancellation CA on MA.MemberAgreementId=CA.EntityId LEFT OUTER JOIN Suspension s on  MA.MemberAgreementId=s.TargetEntityId LEFT OUTER JOIN dbo.SuspensionEndDate sed ON sed.SuspensionId = s.SuspensionId and sed.SuspensionEndDateId = (select max(SuspensionEndDateId) from SuspensionEndDate sed2 where sed2.suspensionid = s.SuspensionId) LEFT OUTER JOIN dbo.SuspensionReason AS sr ON sr.SuspensionReasonId = s.SuspensionReasonId INNER JOIN MemberAgreementItem mai with (nolock) on ma.MemberAgreementId = mai.MemberAgreementId and IsKeyItem = 1 left JOIN memberagreementitempaysource MAIPS with (nolock) on MAIPS.memberagreementitemid = mai.MemberAgreementItemId and MAIPS.sponsorowned =1 and MAIPS.active = 1 left JOIN memberagreementitempaysource maipm with (nolock) on maipm.memberagreementitemid = mai.MemberAgreementItemId and maipm.sponsorowned =0 and maipm.active = 1 left outer JOIN ClientAccountPaySource caps with (nolock) on caps.ClientAccountPaySourceId = MAIPS.ClientAccountPaySourceId left outer JOIN ClientAccountPaySource capsm with (nolock) on capsm.ClientAccountPaySourceId = maipm.ClientAccountPaySourceId left outer JOIN TenderType tt with (nolock) on tt.tendertypeid = capsm.tendertypeid INNER JOIN dbo.PartyPropertiesReportingView pprv ON pr.PartyID = pprv.PartyID where ma.FromExternal = 0 and ma.agreementid != 980\r\n"
				+ "and PR.ROLEID = ? and A.AgreementTypeId !=2 and MA.status not in ('6','7','8','9') order by s.SuspensionId desc";

		List<MemberAgreementDetail> memberAgreementDetail = new ArrayList<MemberAgreementDetail>();
		try {
			memberAgreementDetail = mosoJdbcTemplate.query(query, new Object[] { memberId },
					new BeanPropertyRowMapper(MemberAgreementDetail.class));
		} catch (Exception e) {
			logger.error("Error occured while obtaining member agreement detail " + e.getMessage());
		}
		if (!memberAgreementDetail.isEmpty()) {
			logger.debug("Member Status {}" , memberAgreementDetail.get(0).getMemberStatus());
			return memberAgreementDetail.get(0);
		} else
			return null;
	}

	@Override
	public String getSuspensionReasonId(String suspensionName) {

		logger.debug("Inside DAO impl");
		logger.debug("Suspension Name "+suspensionName);
		String query = "select suspensionreasonid from suspensionreason where name=?";
		String suspensionReasonId = "";
		List<String> suspensionReasonIds = new ArrayList<String>();
		try {
			suspensionReasonIds = mosoJdbcTemplate.queryForList(query, new Object[] {suspensionName}, String.class);
			logger.debug("Suspension Id "+suspensionReasonIds);
		} catch (Exception e) {
			logger.error("Exception occured while retrieving suspension reasonID from Tenant {} " , e.getMessage());
			;
		}
		if (!suspensionReasonIds.isEmpty()) {
			suspensionReasonId = suspensionReasonIds.get(0);
			logger.debug("ReasonID " + suspensionReasonId);
		}
		return suspensionReasonId;
	}

	@Override
	public boolean storeEmailDetailsForMedicalPreganancyFreeze(String memberId, String memberName, String memberEmail,
			Date freezeStartDate, int freezeDuration, String freezeReason, String scheduledTimeToEmail) {
		int rowAdded = 0;
	
		try {
			rowAdded = webDatabaseJdbcTemplate.update(emailMedicalPregnancyFreezeStoreQuery, new Object[] {memberId, memberName,
					memberEmail, freezeStartDate, freezeDuration, freezeReason, scheduledTimeToEmail, 0, "web"});
		} catch (Exception e) {
			logger.error("Exception occured while inserting Freeze Email for member: {}, error: {}", memberId, e.getMessage());
		}
		logger.debug("Number of rows added : {}", rowAdded);
		return rowAdded > 0 ? true : false;
	}

	@Override
	public CorpAgreementDetail getCorpAgreementDetail(String mosoId) {
		logger.debug("Getting Corp agreementDetail from tenant");
		String query = "select distinct ag.Name as agreementType,\r\n"				 
				+ "ma.corporateAgreementName, \r\n"  
				+ "ppr.[28][corporationName],PR.roleID, ma.memberAgreementID , sm.name as status\r\n"  
				+ ",case when exists (select 1 from MemberAgreementGroupRole magrp\r\n"  
				+ "inner join  MemberAgreementGroupRole magr on magr.MemberAgreementGroupId = magrp.MemberAgreementGroupId and magr.RoleType in (2,4)\r\n"  
				+ "inner join  MemberAgreementPromotion map on map.MemberAgreementId = magr.MemberAgreementId\r\n"  
				+ "inner join Promotion p on p.PromotionId = map.PromotionId\r\n"  
				+ "inner join PromotionEffectAddItem pai on pai.PromotionId = p.PromotionId\r\n"  
				+ "inner join item i on i.itemid = pai.ItemId\r\n" 
				+ "where magrp.RoleType in (1,3)\r\n"  
				+ "and magrp.MemberAgreementId = ma.MemberAgreementId\r\n" 
				+ "and i.name like 'subsidy%'\r\n"  
				+ ") then 'Y'\r\n"  
				+ "else 'N'\r\n"  
				+ "end isSubsidy  -- this is to check if any subisdy (paid by corp) is applied via promotion\r\n"  
				+ ",case ma.status when 5 then 'Y' else 'N' end as isAgreementActive\r\n"  
				+ "from \r\n"  
				+ "partyrole pr\r\n"  
				+ "join MemberAgreement ma on ma.partyRoleid = pr.partyroleid\r\n" 
				+ "join memberagreementgrouprole magr on magr.memberagreementid = ma.memberagreementid\r\n"  
				+ "join memberagreementgroup mag on mag.memberagreementgroupid=magr.memberagreementgroupid\r\n" 
				+ "join AgreementGroup ag on ag.AgreementGroupid=mag.AgreementGroupid\r\n"  	
				+ "join partycharacteristic pc on pc.partyid = pr.partyid\r\n"  
				+ "join PartyPropertiesReporting ppr on ppr.partyid=pr.partyid\r\n" 
				+ "join statusmap sm with(nolock) on sm.statusid = ma.status\r\n"  
				+ "where pr.PartyRoleTypeID = '1'\r\n"  
				+ "and pr.roleid = ? \r\n"  
				+ "and sm.statusmaptype = '5'\r\n" 
				+ "and ma.status = 5\r\n"				
				+ "order by ma.corporateAgreementName";

		List<CorpAgreementDetail> corpMemberAgreementDetail = new ArrayList<CorpAgreementDetail>();
		try {
			corpMemberAgreementDetail = mosoJdbcTemplate.query(query, new Object[] { mosoId },
					new BeanPropertyRowMapper(CorpAgreementDetail.class));
		} catch (Exception e) {
			logger.error("Error occured while obtaining member agreement detail " + e.getMessage());
		}
		if (!corpMemberAgreementDetail.isEmpty()) {
			logger.debug("Member Status {}" , corpMemberAgreementDetail.get(0).getStatus());
			return corpMemberAgreementDetail.get(0);
		} else
			return null;
	}

	@Override
	public List<MemberAgreementDetail> getMemberAgreementDetailList(String mosoId) {
		logger.debug("Getting member agreementDetail from tenant");
		String query = "select top 5 cast(PR.ROLEID AS varchar)  memberID, pprv.[First Name], \r\n"
				+ "case when BU.DivisionId = 1 then '1' when BU.DivisionId = 2 then '5' when BU.DivisionId = 3 then '6' END as countryCode,\r\n"
				+ "case when MAIPS.paymentvaluetype = 2 and MAIPS.paymentvalue = 1 then 'Payroll Deducted' when tt.TenderTypeID='103' then 'Credit Card' when tt.TenderTypeID='104' then 'EFT' when tt.TenderTypeID='909' then 'EFT' when tt.TenderTypeID='916' then 'EFT' when tt.TenderTypeID is null then 'Direct Bill' end as Billingmethod,\r\n"
				+ "case PRS.[Status]when 1 then 'Active'when 2 then 'Inactive'when 3 then 'On Hold'when 4 then 'On Freeze' END as memberStatus,\r\n"
				+ "cast(BU.code AS varchar) homeFacilityId,case BU.[DivisionId] when 1 then 'US' when 2 then 'UK' when 3 then 'CA' END as countryCode,\r\n"
				+ "MA.AgreementId as agreementId,MA.MemberAgreementId as memberAgreementId,\r\n"
				+ "case MA.[Status]when 0 then 'Inactive'when 1 then 'NonFinalized'when 2 then 'Processing'when 4 then 'Pending'when 5 then 'Active'when 6 then 'Expired'when 7 then 'Cancelled'when 8 then 'Delinquent'when 9 then 'Depleted' when 10 then 'On Hold'when 11 then 'On Freeze'when 12 then 'Terminated' END as contractStatus,\r\n"
				+ "MA.StartDate as startDate,MA.EndDate as endDate,MA.AGREEMENTTERMID as agreementTermId,MA.ObligationDate as obligationDate,\r\n"
				+ "CA.Date as cancellationDate,CASE s.[Status] WHEN 0 THEN 'Invalid'WHEN 1 THEN 'Pending Start'WHEN 2 THEN 'Active' WHEN 3 THEN 'Expired'WHEN 4 THEN 'Rescinded'WHEN 5 THEN 'Pending Authorization'WHEN 6 THEN 'Denied'END AS freezeStatus, cast(sr.Name AS varchar) freezeReason,\r\n"
				+ "s.BeginTime AS freezeStartTime,sed.EndTime AS freezeEndTime,a.Name AS membershipClass, case MA.[AgreementTermId] when 1 then 'Fixed'when 2 then 'perpetual'when 3 then 'installments'END AS agreementType, s.suspensionid as FreezeId ,ma.clientaccountid as clientAccountId from PARTYROLE PR LEFT OUTER JOIN PARTYROLESTATUS PRS ON PRS.PARTYROLEID = PR.PARTYROLEID RIGHT OUTER JOIN MEMBERAGREEMENT MA on Ma.PartyRoleId=PR.PartyRoleId LEFT OUTER JOIN BUSINESSUNIT BU ON BU.BUSINESSUNITID = MA.BUSINESSUNITID LEFT OUTER JOIN Agreement A on MA.AgreementId=A.AgreementId LEFT OUTER JOIN Cancellation CA on MA.MemberAgreementId=CA.EntityId LEFT OUTER JOIN Suspension s on  MA.MemberAgreementId=s.TargetEntityId LEFT OUTER JOIN dbo.SuspensionEndDate sed ON sed.SuspensionId = s.SuspensionId and sed.SuspensionEndDateId = (select max(SuspensionEndDateId) from SuspensionEndDate sed2 where sed2.suspensionid = s.SuspensionId) LEFT OUTER JOIN dbo.SuspensionReason AS sr ON sr.SuspensionReasonId = s.SuspensionReasonId INNER JOIN MemberAgreementItem mai with (nolock) on ma.MemberAgreementId = mai.MemberAgreementId and IsKeyItem = 1 left JOIN memberagreementitempaysource MAIPS with (nolock) on MAIPS.memberagreementitemid = mai.MemberAgreementItemId and MAIPS.sponsorowned =1 and MAIPS.active = 1 left JOIN memberagreementitempaysource maipm with (nolock) on maipm.memberagreementitemid = mai.MemberAgreementItemId and maipm.sponsorowned =0 and maipm.active = 1 left outer JOIN ClientAccountPaySource caps with (nolock) on caps.ClientAccountPaySourceId = MAIPS.ClientAccountPaySourceId left outer JOIN ClientAccountPaySource capsm with (nolock) on capsm.ClientAccountPaySourceId = maipm.ClientAccountPaySourceId left outer JOIN TenderType tt with (nolock) on tt.tendertypeid = capsm.tendertypeid INNER JOIN dbo.PartyPropertiesReportingView pprv ON pr.PartyID = pprv.PartyID where ma.FromExternal = 0 and ma.agreementid != 980\r\n"
				+ "and PR.ROLEID = ? and A.AgreementTypeId !=2 order by s.SuspensionId desc";

		List<MemberAgreementDetail> memberAgreementDetail = new ArrayList<MemberAgreementDetail>();
		try {
			memberAgreementDetail = mosoJdbcTemplate.query(query, new Object[] { mosoId },
					new BeanPropertyRowMapper(MemberAgreementDetail.class));
		} catch (Exception e) {
			logger.error("Error occured while obtaining member agreement detail " + e.getMessage());
		}
		if (!memberAgreementDetail.isEmpty()) {
			logger.debug("Member Status {}" , memberAgreementDetail);
			return memberAgreementDetail;
		} else
			return Collections.EMPTY_LIST;
	}

	@Override
	public BigDecimal getMosoMemberAccountBalance(String memberId) {
		logger.debug("getMosoMemberAccountBalance : memberId {}", memberId);
		String query = 
						"SELECT ISNULL(SUM(b.balance),0) AS balance FROM dbo.PartyRole pr WITH (NOLOCK)\r\n" + 
						"OUTER APPLY(SELECT tx.TxInvoiceID, txt.PaymentDueDate, SUM(-txt.Amount) AS balance \r\n" + 
						"FROM dbo.TxTransaction txt WITH (NOLOCK)\r\n" + 
						"INNER JOIN TxInvoice tx WITH (NOLOCK)ON txt.TxInvoiceID = tx.TxInvoiceID\r\n" + 
						"INNER JOIN dbo.ClientAccount ca WITH (NOLOCK) ON  ca.ClientAccountId = tx.ClientAccountId\r\n" + 
						"AND ca.IsExternal = 0\r\n" + 
						"WHERE txt.TxTypeId = 9\r\n" + 
						"AND txt.ClientAccountPaySourcePartyRoleID = pr.PartyRoleID\r\n" + 
						"GROUP BY tx.TxInvoiceID, txt.PaymentDueDate) b\r\n" + 
						"WHERE pr.RoleID = ? GROUP BY pr.RoleID" ;
		
		try {
			BigDecimal balance = mosoJdbcTemplate.queryForObject(query, new Object[] {memberId}, BigDecimal.class);
			return balance;
		} catch (EmptyResultDataAccessException e) {
			logger.error("Error received from TenantDB - {}", e.getMessage());
			return BigDecimal.ZERO;
		}
	}

	@Override
	public boolean storeEmailDetailsForMedicalPreganancyFreezeV4(String memberId, String memberName, String memberEmail, Date freezeStartDate, 
			int freezeDuration, String freezeReason, String scheduledTimeToEmail, Date freezeEndDate, String countryCode, boolean pilotFreezeMedFee) {
		int rowAdded = 0;
		int isPilotFreezeMedFee = pilotFreezeMedFee ? 1 : 0;
		
		try {
			rowAdded = webDatabaseJdbcTemplate.update(emailMedicalPregnancyFreezeStoreQuery, new Object[] {memberId, memberName,
					memberEmail, freezeStartDate, freezeEndDate, freezeDuration, freezeReason, scheduledTimeToEmail, 0, "web", countryCode, isPilotFreezeMedFee});
		} catch (Exception e) {
			logger.error("Exception occured while inserting Freeze Email for member: {}, error: {}", memberId, e.getMessage());
		}
		logger.debug("Number of rows added : {}", rowAdded);
		return rowAdded > 0 ? true : false;
	}
}
