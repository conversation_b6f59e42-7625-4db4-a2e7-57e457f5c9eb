/**
 * 
 */
package com.equinoxfitness.freezeservice.dvo;

import java.sql.Date;

/**
 * <AUTHOR>
 *
 */
public class MemberAgreementDetail {
	public String memberId;
	public String firstName;
	public String billingMethod;
	public String memberStatus;
	public String homeFacilityId;
	public String countryCode;
	public String agreementId;
	public String memberAgreementId;
	public String contractStatus;
	public Date startDate;
	public Date endDate;
	public Date obligationDate;
	public Date cancellationDate;
	public String cancellationStateId;
	public String freezeStatus;
	public String freezeReason;
	public Date freezeStartTime;
	public Date freezeEndTime;
	public String membershipClass;
	public String agreementType;
	public String freezeId;
	int clientAccountId;
	public int agreementTermId;
	public String emailAddress;
	/**
	 * @return the memberId
	 */
	public String getMemberId() {
		return memberId;
	}
	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getBillingMethod() {
		return billingMethod;
	}
	public void setBillingMethod(String billingMethod) {
		this.billingMethod = billingMethod;
	}
	/**
	 * @param memberId the memberId to set
	 */
	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}
	/**
	 * @return the memberStatus
	 */
	public String getMemberStatus() {
		return memberStatus;
	}
	/**
	 * @param memberStatus the memberStatus to set
	 */
	public void setMemberStatus(String memberStatus) {
		this.memberStatus = memberStatus;
	}
	/**
	 * @return the homeFacilityId
	 */
	public String getHomeFacilityId() {
		return homeFacilityId;
	}
	/**
	 * @param homeFacilityId the homeFacilityId to set
	 */
	public void setHomeFacilityId(String homeFacilityId) {
		this.homeFacilityId = homeFacilityId;
	}
	/**
	 * @return the countryCode
	 */
	public String getCountryCode() {
		return countryCode;
	}
	/**
	 * @param homeFacilityId the homeFacilityId to set
	 */
	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}
	/**
	 * @return the agreementId
	 */
	public String getAgreementId() {
		return agreementId;
	}
	/**
	 * @param agreementId the agreementId to set
	 */
	public void setAgreementId(String agreementId) {
		this.agreementId = agreementId;
	}
	/**
	 * @return the memberAgreementId
	 */
	public String getMemberAgreementId() {
		return memberAgreementId;
	}
	/**
	 * @param memberAgreementId the memberAgreementId to set
	 */
	public void setMemberAgreementId(String memberAgreementId) {
		this.memberAgreementId = memberAgreementId;
	}
	/**
	 * @return the contractStatus
	 */
	public String getContractStatus() {
		return contractStatus;
	}
	/**
	 * @param contractStatus the contractStatus to set
	 */
	public void setContractStatus(String contractStatus) {
		this.contractStatus = contractStatus;
	}
	/**
	 * @return the startDate
	 */
	public Date getStartDate() {
		return startDate;
	}
	/**
	 * @param startDate the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	/**
	 * @return the endDate
	 */
	public Date getEndDate() {
		return endDate;
	}
	/**
	 * @param endDate the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	/**
	 * @return the obligationDate
	 */
	public Date getObligationDate() {
		return obligationDate;
	}
	/**
	 * @param obligationDate the obligationDate to set
	 */
	public void setObligationDate(Date obligationDate) {
		this.obligationDate = obligationDate;
	}
	/**
	 * @return the cancellationDate
	 */
	public Date getCancellationDate() {
		return cancellationDate;
	}
	/**
	 * @param cancellationDate the cancellationDate to set
	 */
	public void setCancellationDate(Date cancellationDate) {
		this.cancellationDate = cancellationDate;
	}
	/**
	 * @return the freezeStatus
	 */
	public String getFreezeStatus() {
		return freezeStatus;
	}
	/**
	 * @param freezeStatus the freezeStatus to set
	 */
	public void setFreezeStatus(String freezeStatus) {
		this.freezeStatus = freezeStatus;
	}
	/**
	 * @return the freezeReason
	 */
	public String getFreezeReason() {
		return freezeReason;
	}
	/**
	 * @param freezeReason the freezeReason to set
	 */
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	/**
	 * @return the freezeStartTime
	 */
	public Date getFreezeStartTime() {
		return freezeStartTime;
	}
	/**
	 * @param freezeStartTime the freezeStartTime to set
	 */
	public void setFreezeStartTime(Date freezeStartTime) {
		this.freezeStartTime = freezeStartTime;
	}
	/**
	 * @return the freezeEndTime
	 */
	public Date getFreezeEndTime() {
		return freezeEndTime;
	}
	/**
	 * @param freezeEndTime the freezeEndTime to set
	 */
	public void setFreezeEndTime(Date freezeEndTime) {
		this.freezeEndTime = freezeEndTime;
	}
	/**
	 * @return the membershipClass
	 */
	public String getMembershipClass() {
		return membershipClass;
	}
	/**
	 * @param membershipClass the membershipClass to set
	 */
	public void setMembershipClass(String membershipClass) {
		this.membershipClass = membershipClass;
	}
	/**
	 * @return the agreementType
	 */
	public String getAgreementType() {
		return agreementType;
	}
	/**
	 * @param agreementType the agreementType to set
	 */
	public void setAgreementType(String agreementType) {
		this.agreementType = agreementType;
	}
	/**
	 * @return the freezeId
	 */
	public String getFreezeId() {
		return freezeId;
	}
	/**
	 * @param freezeId the freezeId to set
	 */
	public void setFreezeId(String freezeId) {
		this.freezeId = freezeId;
	}
	/**
	 * @return the clientAccountId
	 */
	public int getClientAccountId() {
		return clientAccountId;
	}
	/**
	 * @param clientAccountId the clientAccountId to set
	 */
	public void setClientAccountId(int clientAccountId) {
		this.clientAccountId = clientAccountId;
	}
	/**
	 * @return the agreementTermId
	 */
	public int getAgreementTermId() {
		return agreementTermId;
	}
	/**
	 * @param agreementTermId the agreementTermId to set
	 */
	public void setAgreementTermId(int agreementTermId) {
		this.agreementTermId = agreementTermId;
	}
	public String getEmailAddress() {
		return emailAddress;
	}
	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}
	public String getCancellationStateId() {
		return cancellationStateId;
	}
	public void setCancellationStateId(String cancellationStateId) {
		this.cancellationStateId = cancellationStateId;
	}
	
}
