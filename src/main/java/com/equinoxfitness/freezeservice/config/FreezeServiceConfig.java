package com.equinoxfitness.freezeservice.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FreezeServiceConfig {
	
	@Value("${preAuthorize.url}")
	private String preAuthorizeUrl;
	
	@Bean(name="preAuthorizeUrl")
	public String preAuthorizeUrl() {
		return preAuthorizeUrl;
	}
	
	@Value("${capture.url}")
	private String captureUrl;
	
	@Bean(name="captureUrl")
	public String captureUrl() {
		return captureUrl;
	}
	
	@Value("${void.url}")
	private String voidUrl;
	
	@Bean(name="voidUrl")
	public String voidUrl() {
		return voidUrl;
	}

}
