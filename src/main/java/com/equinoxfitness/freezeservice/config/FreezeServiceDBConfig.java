/**
 * 
 */
package com.equinoxfitness.freezeservice.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * <AUTHOR>
 *
 */
@Configuration
public class FreezeServiceDBConfig {

	@Primary
	@Bean(name = "mosoDataSource")
	@ConfigurationProperties(prefix = "spring.datasource.tenantEquinox")
	public DataSource mosoDatabaseDataSource() {
		return  DataSourceBuilder.create().build();
	}
	
	@Bean(name = "mosoJdbcTemplate")
	public JdbcTemplate mosoDatabaseJdbcTemplate(@Qualifier("mosoDataSource") DataSource moso) {
		return new JdbcTemplate(moso, false);
	}
	
	/* @Bean(name = "paperTrailDb")
	@ConfigurationProperties(prefix = "spring.datasource.papertrail")
	public DataSource paperTrailDataSource() {
		return DataSourceBuilder.create().build();
	}
	
	@Bean(name = "paperTrailJdbcTemplate")
	public JdbcTemplate paperTrailJdbcTemplate(@Qualifier("paperTrailDb") DataSource paperTrailDb) {
		return new JdbcTemplate(paperTrailDb, false);
	}
	
	@Bean(name = "trainingDatabase")
	@ConfigurationProperties(prefix = "spring.datasource.training")
	public DataSource trainingDataSource() {
		return  DataSourceBuilder.create().build();
	}
    @Bean(name = "trainingDatabaseJdbcTemplate")
    public JdbcTemplate trainingDatabaseJdbcTemplate(@Qualifier("trainingDatabase") DataSource dataSource) {
        return new JdbcTemplate(dataSource, false);
    } */
    
    @Bean(name = "webDatabaseSource")
	@ConfigurationProperties("spring.datasource.webdb")
	public DataSource webDatabaseSource() {
		DataSource datasource = DataSourceBuilder.create().build();
		return datasource;
	}

	@Bean(name = "webDatabaseJdbcTemplate")
	public JdbcTemplate webDatabaseNamedParameterJdbcTemplate(@Qualifier("webDatabaseSource") DataSource webDatabaseSource) {
		return new JdbcTemplate(webDatabaseSource, false);
	}
}
