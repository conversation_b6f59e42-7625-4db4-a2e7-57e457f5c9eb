/**
 * 
 */
package com.equinoxfitness.freezeservice.config;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Component;

import com.equinoxfitness.freezeservice.utils.FundingSourceMapper;

/**
 * <AUTHOR> krishna chimata
 *
 */
@Component
public class SimpleJdbcCallConfig {
	
	@Autowired
	@Qualifier("mosoJdbcTemplate")
	private JdbcTemplate mosoJdbcTemplate;
	
	@Bean(name = "getCOFSimpleJdbcCall")
	public SimpleJdbcCall getCOFSimpleJdbcCall() {
		return new SimpleJdbcCall(mosoJdbcTemplate)
				.withSchemaName("dbo")
				.withProcedureName("EQX_SP_CardOnFile")
				.returningResultSet("fundingSource", new FundingSourceMapper());
	}

	@PostConstruct
	private void init() {
		mosoJdbcTemplate.setResultsMapCaseInsensitive(true);
	}

}
