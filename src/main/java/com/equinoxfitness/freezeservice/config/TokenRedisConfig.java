package com.equinoxfitness.freezeservice.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.equinoxfitness.redisUtil.RedisConnectionFactory;
import com.equinoxfitness.redisUtil.RedisTemplate;

@Configuration
public class TokenRedisConfig {
	
	@Value("${redisGlobal.port}")
	private int port;
	
	@Value("${redisGlobal.host}")
	private String userHost;
	
	@Value("${redis.freeze.session.database}")
	private int redisFreezeDb;
	
	@Bean
	RedisConnectionFactory tokenNoFreezeUtil(){
	     return new com.equinoxfitness.redisUtil.RedisConnectionFactory(userHost, port, redisFreezeDb);
	}
	
	@Bean(name = "tokenNoFreezeTemplate")
	RedisTemplate tokenNoFreezeTemplate(){
	     return new RedisTemplate(tokenNoFreezeUtil());
	}


}
