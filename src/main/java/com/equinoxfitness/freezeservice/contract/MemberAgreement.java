/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import com.equinoxfitness.common.moso.contract.BaseOutput;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberAgreement extends BaseOutput {

    @JsonProperty("AgreementId")
    Integer agreementId;

    @JsonProperty("ObligationDate")
    String obligationDate;

	/**
	 * @return the agreementId
	 */
	public Integer getAgreementId() {
		return agreementId;
	}

	/**
	 * @param agreementId the agreementId to set
	 */
	public void setAgreementId(Integer agreementId) {
		this.agreementId = agreementId;
	}

	/**
	 * @return the obligationDate
	 */
	public String getObligationDate() {
		return obligationDate;
	}

	/**
	 * @param obligationDate the obligationDate to set
	 */
	public void setObligationDate(String obligationDate) {
		this.obligationDate = obligationDate;
	}

  

}
