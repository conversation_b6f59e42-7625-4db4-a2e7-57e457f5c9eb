package com.equinoxfitness.freezeservice.contract.moso.invoice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Transaction {
	@JsonProperty("Id")
	private Integer id;

	@JsonProperty("InvoiceId")
	private Integer invoiceId;

	@JsonProperty("GroupId")
	private Integer groupId;

	@JsonProperty("BundleId")
	private Integer bundleId;

	@JsonProperty("BundleGroupId")
	private Integer bundleGroupId;

	@JsonProperty("TargetDate")
	private String targetDate;

	@JsonProperty("TargetLocation")
	private Location targetLocation;

	@JsonProperty("TransactionType")
	private String transactionType; // ['Invalid' or 'Sale' or 'Tax' or 'Discount' or 'Payment' or 'CashBack' or 'Adjustment' or 'LoyaltyDiscount' or 'AgreementDiscount' or 'OnAccount' or 'DisplayOnly' or 'InvoiceSummary'],

	@JsonProperty("Quantity")
	private Integer quantity;

	@JsonProperty("Amount")
	private String amount;

	@JsonProperty("UnitPrice")
	private String unitPrice;

	@JsonProperty("Description")
	private String description;

	@JsonProperty("Comments")
	private String comments;

	@JsonProperty("TransactionItemId")
	private Integer transactionItemId; // Depending on the type of transaction, this could be an item id, a discount code id, or one of several others.

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(Integer invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public Integer getBundleId() {
		return bundleId;
	}

	public void setBundleId(Integer bundleId) {
		this.bundleId = bundleId;
	}

	public Integer getBundleGroupId() {
		return bundleGroupId;
	}

	public void setBundleGroupId(Integer bundleGroupId) {
		this.bundleGroupId = bundleGroupId;
	}

	public String getTargetDate() {
		return targetDate;
	}

	public void setTargetDate(String targetDate) {
		this.targetDate = targetDate;
	}

	public Location getTargetLocation() {
		return targetLocation;
	}

	public void setTargetLocation(Location targetLocation) {
		this.targetLocation = targetLocation;
	}

	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(String unitPrice) {
		this.unitPrice = unitPrice;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Integer getTransactionItemId() {
		return transactionItemId;
	}

	public void setTransactionItemId(Integer transactionItemId) {
		this.transactionItemId = transactionItemId;
	}
}
