package com.equinoxfitness.freezeservice.contract.moso.invoice;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentTender {
	@JsonProperty("TenderTypeId")
	private Integer tenderTypeId;

	@JsonProperty("Amount")
	private BigDecimal amount;

	@JsonProperty("AdditionalInfo")
	private String additionalInfo;

	@JsonProperty("CreditCardToken")
	private String creditCardToken;

	@JsonProperty("ClientAccountPaySourceId")
	private Integer clientAccountPaySourceId;

	@JsonProperty("ClientAccountId")
	private Integer clientAccountId;

	@JsonProperty("GiftCardNumber")
	private String giftCardNumber;

	@JsonProperty("CreditCardAuthResult")
	private CreditCardAuthResult creditCardAuthResult;

	public Integer getTenderTypeId() {
		return tenderTypeId;
	}

	public void setTenderTypeId(Integer tenderTypeId) {
		this.tenderTypeId = tenderTypeId;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getAdditionalInfo() {
		return additionalInfo;
	}

	public void setAdditionalInfo(String additionalInfo) {
		this.additionalInfo = additionalInfo;
	}

	public String getCreditCardToken() {
		return creditCardToken;
	}

	public void setCreditCardToken(String creditCardToken) {
		this.creditCardToken = creditCardToken;
	}

	public Integer getClientAccountPaySourceId() {
		return clientAccountPaySourceId;
	}

	public void setClientAccountPaySourceId(Integer clientAccountPaySourceId) {
		this.clientAccountPaySourceId = clientAccountPaySourceId;
	}

	public Integer getClientAccountId() {
		return clientAccountId;
	}

	public void setClientAccountId(Integer clientAccountId) {
		this.clientAccountId = clientAccountId;
	}

	public String getGiftCardNumber() {
		return giftCardNumber;
	}

	public void setGiftCardNumber(String giftCardNumber) {
		this.giftCardNumber = giftCardNumber;
	}

	public CreditCardAuthResult getCreditCardAuthResult() {
		return creditCardAuthResult;
	}

	public void setCreditCardAuthResult(CreditCardAuthResult creditCardAuthResult) {
		this.creditCardAuthResult = creditCardAuthResult;
	}
}
