/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class UpdateSuspensionRequest {
	
	@JsonProperty("FreezeId")
	String freezeId;
	//@JsonProperty("FreezeStartDate")
	/*String freezeStartDate;*/
	@JsonProperty("FreezeEndDate")
	String freezeEndDate;
	@JsonProperty("Comments")
	String comments;
	/*@JsonProperty("ExceptionReasonId")
	int exceptionReasonId;
	@JsonProperty("ExceptionValue")
	Number exceptionValue;
	@JsonProperty("ExceptionComment")
	String exceptionComment;
	@JsonProperty("FreezeEndDateSet")
	boolean freezeEndDateSet;*/
	/**
	 * @return the freezeId
	 */
	public String getFreezeId() {
		return freezeId;
	}
	/**
	 * @param freezeId the freezeId to set
	 */
	public void setFreezeId(String freezeId) {
		this.freezeId = freezeId;
	}
	/**
	 * @return the freezeStartDate
	 *//*
	public String getFreezeStartDate() {
		return freezeStartDate;
	}
	*//**
	 * @param freezeStartDate the freezeStartDate to set
	 *//*
	public void setFreezeStartDate(String freezeStartDate) {
		this.freezeStartDate = freezeStartDate;
	}*/
	/**
	 * @return the freezeEndDate
	 */
	public String getFreezeEndDate() {
		return freezeEndDate;
	}
	/**
	 * @param freezeEndDate the freezeEndDate to set
	 */
	public void setFreezeEndDate(String freezeEndDate) {
		this.freezeEndDate = freezeEndDate;
	}
	/**
	 * @return the comments
	 */
	public String getComments() {
		return comments;
	}
	/**
	 * @param comments the comments to set
	 */
	public void setComments(String comments) {
		this.comments = comments;
	}
	/**
	 * @return the exceptionReasonId
	 *//*
	public int getExceptionReasonId() {
		return exceptionReasonId;
	}
	*//**
	 * @param exceptionReasonId the exceptionReasonId to set
	 *//*
	public void setExceptionReasonId(int exceptionReasonId) {
		this.exceptionReasonId = exceptionReasonId;
	}
	*//**
	 * @return the exceptionValue
	 *//*
	public Number getExceptionValue() {
		return exceptionValue;
	}
	*//**
	 * @param exceptionValue the exceptionValue to set
	 *//*
	public void setExceptionValue(Number exceptionValue) {
		this.exceptionValue = exceptionValue;
	}
	*//**
	 * @return the exceptionComment
	 *//*
	public String getExceptionComment() {
		return exceptionComment;
	}
	*//**
	 * @param exceptionComment the exceptionComment to set
	 *//*
	public void setExceptionComment(String exceptionComment) {
		this.exceptionComment = exceptionComment;
	}
	*//**
	 * @return the freezeEndDateSet
	 *//*
	public boolean isFreezeEndDateSet() {
		return freezeEndDateSet;
	}
	*//**
	 * @param freezeEndDateSet the freezeEndDateSet to set
	 *//*
	public void setFreezeEndDateSet(boolean freezeEndDateSet) {
		this.freezeEndDateSet = freezeEndDateSet;
	}*/
	

}
