/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceDetails {

	@JsonProperty("BusinessUnitCode")
	String businessUnitCode;
	@JsonProperty("SourceId")
	Integer sourceId;
	@JsonProperty("SourceName")
	String sourceName;
	@JsonProperty("StartDateEx")
	String startDateEx;
	@JsonProperty("Price")
	Number price;
	/**
	 * @return the businessUnitCode
	 */
	public String getBusinessUnitCode() {
		return businessUnitCode;
	}
	/**
	 * @param businessUnitCode the businessUnitCode to set
	 */
	public void setBusinessUnitCode(String businessUnitCode) {
		this.businessUnitCode = businessUnitCode;
	}
	/**
	 * @return the sourceId
	 */
	public Integer getSourceId() {
		return sourceId;
	}
	/**
	 * @param sourceId the sourceId to set
	 */
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	/**
	 * @return the sourceName
	 */
	public String getSourceName() {
		return sourceName;
	}
	/**
	 * @param sourceName the sourceName to set
	 */
	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}
	/**
	 * @return the startDateEx
	 */
	public String getStartDateEx() {
		return startDateEx;
	}
	/**
	 * @param startDateEx the startDateEx to set
	 */
	public void setStartDateEx(String startDateEx) {
		this.startDateEx = startDateEx;
	}
	/**
	 * @return the price
	 */
	public Number getPrice() {
		return price;
	}
	/**
	 * @param price the price to set
	 */
	public void setPrice(Number price) {
		this.price = price;
	}
	
}
