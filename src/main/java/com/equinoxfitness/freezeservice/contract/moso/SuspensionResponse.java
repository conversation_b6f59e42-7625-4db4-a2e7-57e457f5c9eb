/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.equinoxfitness.common.moso.contract.BaseOutput;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class SuspensionResponse extends BaseOutput{
	
	@JsonProperty("SuspensionId")
	int suspensionId;
	@JsonProperty("SuspendedAgreementName")
	String suspendedAgreementName;
	@JsonProperty("MemberFirstName")
	String memberFirstName;
	@JsonProperty("MemberLastName")
	String memberLastName;
	@JsonProperty("SuspensionReasonName")
	String suspensionReasonName;
	@JsonProperty("FreezeStartDate")
	String freezeStartDate;
	@JsonProperty("FreezeEndDate")
	String freezeEndDate;
	@JsonProperty("FreezeFeeItem")
	String freezeFeeItem;
	@JsonProperty("FreezeFeeAmount")
	Number freezeFeeAmount;
	@JsonProperty("FreezeFeeStartDate")
	String freezeFeeStartDate;
	@JsonProperty("FreezeRecurringFeeItem")
	String freezeRecurringFeeItem;
	@JsonProperty("FreezeRecurringFeeAmount")
	Number freezeRecurringFeeAmount;
	@JsonProperty("FreezeRecurringFeeStartDate")
	String freezeRecurringFeeStartDate;
	@JsonProperty("FreezeStatus")
	String freezeStatus;
	/**
	 * @return the suspensionId
	 */
	public int getSuspensionId() {
		return suspensionId;
	}
	/**
	 * @param suspensionId the suspensionId to set
	 */
	public void setSuspensionId(int suspensionId) {
		this.suspensionId = suspensionId;
	}
	/**
	 * @return the suspendedAgreementName
	 */
	public String getSuspendedAgreementName() {
		return suspendedAgreementName;
	}
	/**
	 * @param suspendedAgreementName the suspendedAgreementName to set
	 */
	public void setSuspendedAgreementName(String suspendedAgreementName) {
		this.suspendedAgreementName = suspendedAgreementName;
	}
	/**
	 * @return the memberFirstName
	 */
	public String getMemberFirstName() {
		return memberFirstName;
	}
	/**
	 * @param memberFirstName the memberFirstName to set
	 */
	public void setMemberFirstName(String memberFirstName) {
		this.memberFirstName = memberFirstName;
	}
	/**
	 * @return the memberLastName
	 */
	public String getMemberLastName() {
		return memberLastName;
	}
	/**
	 * @param memberLastName the memberLastName to set
	 */
	public void setMemberLastName(String memberLastName) {
		this.memberLastName = memberLastName;
	}
	/**
	 * @return the suspensionReasonName
	 */
	public String getSuspensionReasonName() {
		return suspensionReasonName;
	}
	/**
	 * @param suspensionReasonName the suspensionReasonName to set
	 */
	public void setSuspensionReasonName(String suspensionReasonName) {
		this.suspensionReasonName = suspensionReasonName;
	}
	/**
	 * @return the freezeStartDate
	 */
	public String getFreezeStartDate() {
		return freezeStartDate;
	}
	/**
	 * @param freezeStartDate the freezeStartDate to set
	 */
	public void setFreezeStartDate(String freezeStartDate) {
		this.freezeStartDate = freezeStartDate;
	}
	/**
	 * @return the freezeEndDate
	 */
	public String getFreezeEndDate() {
		return freezeEndDate;
	}
	/**
	 * @param freezeEndDate the freezeEndDate to set
	 */
	public void setFreezeEndDate(String freezeEndDate) {
		this.freezeEndDate = freezeEndDate;
	}
	/**
	 * @return the freezeFeeItem
	 */
	public String getFreezeFeeItem() {
		return freezeFeeItem;
	}
	/**
	 * @param freezeFeeItem the freezeFeeItem to set
	 */
	public void setFreezeFeeItem(String freezeFeeItem) {
		this.freezeFeeItem = freezeFeeItem;
	}
	/**
	 * @return the freezeFeeAmount
	 */
	public Number getFreezeFeeAmount() {
		return freezeFeeAmount;
	}
	/**
	 * @param freezeFeeAmount the freezeFeeAmount to set
	 */
	public void setFreezeFeeAmount(Number freezeFeeAmount) {
		this.freezeFeeAmount = freezeFeeAmount;
	}
	/**
	 * @return the freezeFeeStartDate
	 */
	public String getFreezeFeeStartDate() {
		return freezeFeeStartDate;
	}
	/**
	 * @param freezeFeeStartDate the freezeFeeStartDate to set
	 */
	public void setFreezeFeeStartDate(String freezeFeeStartDate) {
		this.freezeFeeStartDate = freezeFeeStartDate;
	}
	/**
	 * @return the freezeRecurringFeeItem
	 */
	public String getFreezeRecurringFeeItem() {
		return freezeRecurringFeeItem;
	}
	/**
	 * @param freezeRecurringFeeItem the freezeRecurringFeeItem to set
	 */
	public void setFreezeRecurringFeeItem(String freezeRecurringFeeItem) {
		this.freezeRecurringFeeItem = freezeRecurringFeeItem;
	}
	/**
	 * @return the freezeRecurringFeeAmount
	 */
	public Number getFreezeRecurringFeeAmount() {
		return freezeRecurringFeeAmount;
	}
	/**
	 * @param freezeRecurringFeeAmount the freezeRecurringFeeAmount to set
	 */
	public void setFreezeRecurringFeeAmount(Number freezeRecurringFeeAmount) {
		this.freezeRecurringFeeAmount = freezeRecurringFeeAmount;
	}
	/**
	 * @return the freezeRecurringFeeStartDate
	 */
	public String getFreezeRecurringFeeStartDate() {
		return freezeRecurringFeeStartDate;
	}
	/**
	 * @param freezeRecurringFeeStartDate the freezeRecurringFeeStartDate to set
	 */
	public void setFreezeRecurringFeeStartDate(String freezeRecurringFeeStartDate) {
		this.freezeRecurringFeeStartDate = freezeRecurringFeeStartDate;
	}
	/**
	 * @return the freezeStatus
	 */
	public String getFreezeStatus() {
		return freezeStatus;
	}
	/**
	 * @param freezeStatus the freezeStatus to set
	 */
	public void setFreezeStatus(String freezeStatus) {
		this.freezeStatus = freezeStatus;
	}
	@Override
	public String toString() {
		return "SuspensionResponse [suspensionId=" + suspensionId + ", suspendedAgreementName=" + suspendedAgreementName
				+ ", memberFirstName=" + memberFirstName + ", memberLastName=" + memberLastName
				+ ", suspensionReasonName=" + suspensionReasonName + ", freezeStartDate=" + freezeStartDate
				+ ", freezeEndDate=" + freezeEndDate + ", freezeFeeItem=" + freezeFeeItem + ", freezeFeeAmount="
				+ freezeFeeAmount + ", freezeFeeStartDate=" + freezeFeeStartDate + ", freezeRecurringFeeItem="
				+ freezeRecurringFeeItem + ", freezeRecurringFeeAmount=" + freezeRecurringFeeAmount
				+ ", freezeRecurringFeeStartDate=" + freezeRecurringFeeStartDate + ", freezeStatus=" + freezeStatus
				+ "]";
	}
	
}
