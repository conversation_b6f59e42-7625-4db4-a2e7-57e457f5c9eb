/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AllowedStaffWorkRole {
	
	@JsonProperty("WorkRoleID")
	Integer workRoleID;
	@JsonProperty("WorkRoleName")
	String workRoleName;
	/**
	 * @return the workRoleID
	 */
	public Integer getWorkRoleID() {
		return workRoleID;
	}
	/**
	 * @param workRoleID the workRoleID to set
	 */
	public void setWorkRoleID(Integer workRoleID) {
		this.workRoleID = workRoleID;
	}
	/**
	 * @return the workRoleName
	 */
	public String getWorkRoleName() {
		return workRoleName;
	}
	/**
	 * @param workRoleName the workRoleName to set
	 */
	public void setWorkRoleName(String workRoleName) {
		this.workRoleName = workRoleName;
	}
	
}
