package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UnFreezeRequest {

	@JsonProperty("FreezeId")
	private String freezeId;
	@JsonProperty("FreezeEndDate")
	private String freezeEndDate;
	@JsonProperty("FreezeEndDateSet")
	private boolean freezeEndDateSet;

	public String getFreezeId() {
		return freezeId;
	}

	public void setFreezeId(String freezeId) {
		this.freezeId = freezeId;
	}

	public String getFreezeEndDate() {
		return freezeEndDate;
	}

	public void setFreezeEndDate(String freezeEndDate) {
		this.freezeEndDate = freezeEndDate;
	}

	public boolean isFreezeEndDateSet() {
		return freezeEndDateSet;
	}

	public void setFreezeEndDateSet(boolean freezeEndDateSet) {
		this.freezeEndDateSet = freezeEndDateSet;
	}

}
