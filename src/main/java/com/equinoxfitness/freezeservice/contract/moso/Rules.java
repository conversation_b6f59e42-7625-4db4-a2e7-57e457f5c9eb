/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Rules {

	@JsonProperty("MustStartonBillDate")
	Boolean mustStartonBillDate;
	@JsonProperty("AllowOpenEnded")
	Boolean allowOpenEnded;
	@JsonProperty("MinDays")
	Integer minDays;
	@JsonProperty("MaxDays")
	Integer maxDays;
	@JsonProperty("ApplyMemberDiscountsDurring")
	Boolean applyMemberDiscountsDurring;
	/**
	 * @return the mustStartonBillDate
	 */
	public Boolean getMustStartonBillDate() {
		return mustStartonBillDate;
	}
	/**
	 * @param mustStartonBillDate the mustStartonBillDate to set
	 */
	public void setMustStartonBillDate(Boolean mustStartonBillDate) {
		this.mustStartonBillDate = mustStartonBillDate;
	}
	/**
	 * @return the allowOpenEnded
	 */
	public Boolean getAllowOpenEnded() {
		return allowOpenEnded;
	}
	/**
	 * @param allowOpenEnded the allowOpenEnded to set
	 */
	public void setAllowOpenEnded(Boolean allowOpenEnded) {
		this.allowOpenEnded = allowOpenEnded;
	}
	/**
	 * @return the minDays
	 */
	public Integer getMinDays() {
		return minDays;
	}
	/**
	 * @param minDays the minDays to set
	 */
	public void setMinDays(Integer minDays) {
		this.minDays = minDays;
	}
	/**
	 * @return the maxDays
	 */
	public Integer getMaxDays() {
		return maxDays;
	}
	/**
	 * @param maxDays the maxDays to set
	 */
	public void setMaxDays(Integer maxDays) {
		this.maxDays = maxDays;
	}
	/**
	 * @return the applyMemberDiscountsDurring
	 */
	public Boolean getApplyMemberDiscountsDurring() {
		return applyMemberDiscountsDurring;
	}
	/**
	 * @param applyMemberDiscountsDurring the applyMemberDiscountsDurring to set
	 */
	public void setApplyMemberDiscountsDurring(Boolean applyMemberDiscountsDurring) {
		this.applyMemberDiscountsDurring = applyMemberDiscountsDurring;
	}
	
}
