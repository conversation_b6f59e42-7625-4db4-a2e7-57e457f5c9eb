package com.equinoxfitness.freezeservice.contract.moso.invoice;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class NewInvoiceItem {
	@JsonProperty("IsItem")
	private Boolean isItem; //Set this to true if the attached ItemCode represents an item. Set to false if it represents a bundle.,

	@JsonProperty("ItemCode")
	private String itemCode;

	@JsonProperty("Quantity")
	private Integer quantity;

	@JsonProperty("DiscountCodes")
	private List<DiscountCodeSlim> discountCodes;

	@JsonProperty("Price")
	private BigDecimal price;

	@JsonProperty("Comments")
	private String comments;

	@JsonProperty("SalesPersonId")
	private String salesPersonId;

	@JsonProperty("GiftCard")
	private GiftCardInfo giftCard;

	@JsonProperty("LockerDetails")
	private LockerDetails lockerDetails;

	public Boolean getIsItem() {
		return isItem;
	}

	public void setIsItem(Boolean isItem) {
		this.isItem = isItem;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public List<DiscountCodeSlim> getDiscountCodes() {
		return discountCodes;
	}

	public void setDiscountCodes(List<DiscountCodeSlim> discountCodes) {
		this.discountCodes = discountCodes;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getSalesPersonId() {
		return salesPersonId;
	}

	public void setSalesPersonId(String salesPersonId) {
		this.salesPersonId = salesPersonId;
	}

	public GiftCardInfo getGiftCard() {
		return giftCard;
	}

	public void setGiftCard(GiftCardInfo giftCard) {
		this.giftCard = giftCard;
	}

	public LockerDetails getLockerDetails() {
		return lockerDetails;
	}

	public void setLockerDetails(LockerDetails lockerDetails) {
		this.lockerDetails = lockerDetails;
	}
}
