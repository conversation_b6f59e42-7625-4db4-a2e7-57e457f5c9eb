package com.equinoxfitness.freezeservice.contract.moso.invoice;

import java.math.BigDecimal;
import java.util.List;
import com.equinoxfitness.common.moso.contract.BaseOutput;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Invoice extends BaseOutput{
	@JsonProperty("Id")
	private Integer id;
	
	@JsonProperty("TargetDate")
	private String targetDate;

	@JsonProperty("TargetLocation")
	private Location targetLocation;

	@JsonProperty("Comments")
	private String comments;

	@JsonProperty("PartyRoleId")
	private Integer partyRoleId;

	@JsonProperty("ClientAccountId")
	private Integer clientAccountId;

	@JsonProperty("TaxExemptId")
	private Integer taxExemptId;

	@JsonProperty("TaxExemptionName")
	private String taxExemptionName;

	@JsonProperty("Total")
	private String total;

	@JsonProperty("Balance")
	private BigDecimal balance;

	@JsonProperty("InvoiceStatus")
	private String invoiceStatus; //['Invalid' or 'Projected' or 'Pending' or 'BalanceZero' or 'BalanceDue' or 'Cancelled' or 'BalanceCredit'],

	@JsonProperty("PaymentDueDate")
	private String paymentDueDate;

	@JsonProperty("Transactions")
	private List<Transaction> transactions;

	private String message;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getTargetDate() {
		return targetDate;
	}

	public void setTargetDate(String targetDate) {
		this.targetDate = targetDate;
	}

	public Location getTargetLocation() {
		return targetLocation;
	}

	public void setTargetLocation(Location targetLocation) {
		this.targetLocation = targetLocation;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Integer getPartyRoleId() {
		return partyRoleId;
	}

	public void setPartyRoleId(Integer partyRoleId) {
		this.partyRoleId = partyRoleId;
	}

	public Integer getClientAccountId() {
		return clientAccountId;
	}

	public void setClientAccountId(Integer clientAccountId) {
		this.clientAccountId = clientAccountId;
	}

	public Integer getTaxExemptId() {
		return taxExemptId;
	}

	public void setTaxExemptId(Integer taxExemptId) {
		this.taxExemptId = taxExemptId;
	}

	public String getTaxExemptionName() {
		return taxExemptionName;
	}

	public void setTaxExemptionName(String taxExemptionName) {
		this.taxExemptionName = taxExemptionName;
	}

	public String getTotal() {
		return total;
	}

	public void setTotal(String total) {
		this.total = total;
	}

	public BigDecimal getBalance() {
		return balance;
	}

	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}

	public String getInvoiceStatus() {
		return invoiceStatus;
	}

	public void setInvoiceStatus(String invoiceStatus) {
		this.invoiceStatus = invoiceStatus;
	}

	public String getPaymentDueDate() {
		return paymentDueDate;
	}

	public void setPaymentDueDate(String paymentDueDate) {
		this.paymentDueDate = paymentDueDate;
	}

	public List<Transaction> getTransactions() {
		return transactions;
	}

	public void setTransactions(List<Transaction> transactions) {
		this.transactions = transactions;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
}
