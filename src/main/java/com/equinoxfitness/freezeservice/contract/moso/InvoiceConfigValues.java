/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.equinoxfitness.common.moso.contract.BaseOutput;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class InvoiceConfigValues extends BaseOutput{

	@JsonProperty("TaxRates")
	TaxRate [] taxRates;

	/**
	 * @return the taxRates
	 */
	public TaxRate[] getTaxRates() {
		return taxRates;
	}

	/**
	 * @param taxRates the taxRates to set
	 */
	public void setTaxRates(TaxRate[] taxRates) {
		this.taxRates = taxRates;
	}
	
}
