package com.equinoxfitness.freezeservice.contract.moso.invoice;

public class MosoMemberDataVo {

    private String countryCode;
    private String mosoMemberId;
    private String homeFacilityId;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getMosoMemberId() {
        return mosoMemberId;
    }

    public void setMosoMemberId(String mosoMemberId) {
        this.mosoMemberId = mosoMemberId;
    }

    public String getHomeFacilityId() {
        return homeFacilityId;
    }

    public void setHomeFacilityId(String homeFacilityId) {
        this.homeFacilityId = homeFacilityId;
    }

    @Override
    public String toString() {
        return "MosoMemberDataOutput{" +
                "memberId='" + mosoMemberId +
                "', homeFacilityId='" + homeFacilityId +
                "', countryCode=" + countryCode +
                "'}";
    }

}
