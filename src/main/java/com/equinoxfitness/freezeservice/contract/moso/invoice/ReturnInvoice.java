package com.equinoxfitness.freezeservice.contract.moso.invoice;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReturnInvoice {

	@JsonProperty("CancellationReasonID")
	private Integer cancellationReasonID;
	
	@JsonProperty("CancellationReasonComment")
	private String cancellationReasonComment;
	
	@JsonProperty("Payments")
	private List<PaymentTender> payments;

	public Integer getCancellationReasonID() {
		return cancellationReasonID;
	}

	public void setCancellationReasonID(Integer cancellationReasonID) {
		this.cancellationReasonID = cancellationReasonID;
	}

	public String getCancellationReasonComment() {
		return cancellationReasonComment;
	}

	public void setCancellationReasonComment(String cancellationReasonComment) {
		this.cancellationReasonComment = cancellationReasonComment;
	}

	public List<PaymentTender> getPayments() {
		return payments;
	}

	public void setPayments(List<PaymentTender> payments) {
		this.payments = payments;
	}
	
	
}
