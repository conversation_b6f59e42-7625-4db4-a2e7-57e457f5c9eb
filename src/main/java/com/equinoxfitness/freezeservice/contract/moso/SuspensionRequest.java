/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class SuspensionRequest {
	
	@JsonProperty("AgreementId")
	String agreementId;
	@JsonProperty("RoleId")
	String roleId;
	@JsonProperty("FreezeReasonId")
	int freezeReasonId;
	@JsonProperty("RequestedDate")
	String requestedDate;
	@JsonProperty("FreezeStartDate")
	String freezeStartDate;
	@JsonProperty("FreezeEndDate")
	String freezeEndDate;
	@JsonProperty("Comments")
	String comments;
	/**
	 * @return the agreementId
	 */
	public String getAgreementId() {
		return agreementId;
	}
	/**
	 * @param agreementId the agreementId to set
	 */
	public void setAgreementId(String agreementId) {
		this.agreementId = agreementId;
	}
	/**
	 * @return the roleId
	 */
	public String getRoleId() {
		return roleId;
	}
	/**
	 * @param roleId the roleId to set
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	/**
	 * @return the freezeReasonId
	 */
	public int getFreezeReasonId() {
		return freezeReasonId;
	}
	/**
	 * @param freezeReasonId the freezeReasonId to set
	 */
	public void setFreezeReasonId(int freezeReasonId) {
		this.freezeReasonId = freezeReasonId;
	}
	/**
	 * @return the requestedDate
	 */
	public String getRequestedDate() {
		return requestedDate;
	}
	/**
	 * @param requestedDate the requestedDate to set
	 */
	public void setRequestedDate(String requestedDate) {
		this.requestedDate = requestedDate;
	}
	/**
	 * @return the freezeStartDate
	 */
	public String getFreezeStartDate() {
		return freezeStartDate;
	}
	/**
	 * @param freezeStartDate the freezeStartDate to set
	 */
	public void setFreezeStartDate(String freezeStartDate) {
		this.freezeStartDate = freezeStartDate;
	}
	/**
	 * @return the freezeEndDate
	 */
	public String getFreezeEndDate() {
		return freezeEndDate;
	}
	/**
	 * @param freezeEndDate the freezeEndDate to set
	 */
	public void setFreezeEndDate(String freezeEndDate) {
		this.freezeEndDate = freezeEndDate;
	}
	/**
	 * @return the comments
	 */
	public String getComments() {
		return comments;
	}
	/**
	 * @param comments the comments to set
	 */
	public void setComments(String comments) {
		this.comments = comments;
	}
	
}
