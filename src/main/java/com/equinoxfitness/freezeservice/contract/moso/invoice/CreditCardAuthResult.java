package com.equinoxfitness.freezeservice.contract.moso.invoice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditCardAuthResult {
	@JsonProperty("ExpirationDate")
	private String expirationDate;

	@JsonProperty("PostalCode")
	private String postalCode;

	@JsonProperty("AccountName")
	private String accountName;

	@JsonProperty("ProcessorCode")
	private String processorCode;

	@JsonProperty("ReferenceNumber")
	private String referenceNumber;

	@JsonProperty("ResponseCode")
	private String responseCode;

	@JsonProperty("CreditCardToken")
	private String creditCardToken;

	@JsonProperty("TrackData")
	private String trackData;

	@JsonProperty("CreditCardMask")
	private String creditCardMask;

	@JsonProperty("ResponseMessage")
	private String responseMessage;

	@JsonProperty("AvsResponseCode")
	private String avsResponseCode;

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public String getPostalCode() {
		return postalCode;
	}

	public void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getProcessorCode() {
		return processorCode;
	}

	public void setProcessorCode(String processorCode) {
		this.processorCode = processorCode;
	}

	public String getReferenceNumber() {
		return referenceNumber;
	}

	public void setReferenceNumber(String referenceNumber) {
		this.referenceNumber = referenceNumber;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getCreditCardToken() {
		return creditCardToken;
	}

	public void setCreditCardToken(String creditCardToken) {
		this.creditCardToken = creditCardToken;
	}

	public String getTrackData() {
		return trackData;
	}

	public void setTrackData(String trackData) {
		this.trackData = trackData;
	}

	public String getCreditCardMask() {
		return creditCardMask;
	}

	public void setCreditCardMask(String creditCardMask) {
		this.creditCardMask = creditCardMask;
	}

	public String getResponseMessage() {
		return responseMessage;
	}

	public void setResponseMessage(String responseMessage) {
		this.responseMessage = responseMessage;
	}

	public String getAvsResponseCode() {
		return avsResponseCode;
	}

	public void setAvsResponseCode(String avsResponseCode) {
		this.avsResponseCode = avsResponseCode;
	}
}
