package com.equinoxfitness.freezeservice.contract.moso;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;


public class SuspensionResponseArrayDeserializer extends JsonDeserializer<SuspensionListResponse> {

    @Override
    public SuspensionListResponse deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException {
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        SuspensionResponse[] suspensionResponses = mapper.readValue(p, SuspensionResponse[].class);
        
        SuspensionListResponse wrapper = new SuspensionListResponse();
        wrapper.setSuspensionResponses(suspensionResponses);
        return wrapper;
    }
}
