package com.equinoxfitness.freezeservice.contract.moso.invoice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class LockerDetails {
	@JsonProperty("LockerArea")
	private String lockerArea;
	
	@JsonProperty("LockerBank")
	private String lockerBank;

	@JsonProperty("LockerSize")
	private String lockerSize;

	@JsonProperty("LockerCategory")
	private String lockerCategory;

	@JsonProperty("LockerNumber")
	private Integer lockerNumber;

	public String getLockerArea() {
		return lockerArea;
	}

	public void setLockerArea(String lockerArea) {
		this.lockerArea = lockerArea;
	}

	public String getLockerBank() {
		return lockerBank;
	}

	public void setLockerBank(String lockerBank) {
		this.lockerBank = lockerBank;
	}

	public String getLockerSize() {
		return lockerSize;
	}

	public void setLockerSize(String lockerSize) {
		this.lockerSize = lockerSize;
	}

	public String getLockerCategory() {
		return lockerCategory;
	}

	public void setLockerCategory(String lockerCategory) {
		this.lockerCategory = lockerCategory;
	}

	public Integer getLockerNumber() {
		return lockerNumber;
	}

	public void setLockerNumber(Integer lockerNumber) {
		this.lockerNumber = lockerNumber;
	}
}
