package com.equinoxfitness.freezeservice.contract.moso;

import com.equinoxfitness.common.moso.contract.BaseOutput;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

@JsonDeserialize(using = SuspensionResponseArrayDeserializer.class)
public class SuspensionListResponse extends BaseOutput{

    private SuspensionResponse[] suspensionResponses;

    public SuspensionResponse[] getSuspensionResponses() {
        return suspensionResponses;
    }

    public void setSuspensionResponses(SuspensionResponse[] suspensionResponses) {
        this.suspensionResponses = suspensionResponses;
    }
}