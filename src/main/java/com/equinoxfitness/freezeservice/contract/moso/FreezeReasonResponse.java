/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.equinoxfitness.common.moso.contract.BaseOutput;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class FreezeReasonResponse extends BaseOutput{
	
	@JsonProperty("SuspensionReasonID")
	Integer suspensionReasonID ;
	@JsonProperty("SuspensionReasonName")
	String suspensionReasonName;
	@JsonProperty("SuspensionType")
	String suspensionType;
	@JsonProperty("ObligationEffect")
	String obligationEffect;
	@JsonProperty("SuspensionReasonDescription")
	String suspensionReasonDescription;
	@JsonProperty("Rules")
	Rules rules;
	@JsonProperty("ForAgreements")
	ForAgreement [] forAgreements;
	@JsonProperty("AllowedStaffWorkRoles")
	AllowedStaffWorkRole [] allowedStaffWorkRoles;
	@JsonProperty("Effects")
	Effects effects;
	@JsonProperty("GlCode")
	GlCodeInfo glCode;
	/**
	 * @return the suspensionReasonID
	 */
	public Integer getSuspensionReasonID() {
		return suspensionReasonID;
	}
	/**
	 * @param suspensionReasonID the suspensionReasonID to set
	 */
	public void setSuspensionReasonID(Integer suspensionReasonID) {
		this.suspensionReasonID = suspensionReasonID;
	}
	/**
	 * @return the suspensionReasonName
	 */
	public String getSuspensionReasonName() {
		return suspensionReasonName;
	}
	/**
	 * @param suspensionReasonName the suspensionReasonName to set
	 */
	public void setSuspensionReasonName(String suspensionReasonName) {
		this.suspensionReasonName = suspensionReasonName;
	}
	/**
	 * @return the suspensionType
	 */
	public String getSuspensionType() {
		return suspensionType;
	}
	/**
	 * @param suspensionType the suspensionType to set
	 */
	public void setSuspensionType(String suspensionType) {
		this.suspensionType = suspensionType;
	}
	/**
	 * @return the obligationEffect
	 */
	public String getObligationEffect() {
		return obligationEffect;
	}
	/**
	 * @param obligationEffect the obligationEffect to set
	 */
	public void setObligationEffect(String obligationEffect) {
		this.obligationEffect = obligationEffect;
	}
	/**
	 * @return the suspensionReasonDescription
	 */
	public String getSuspensionReasonDescription() {
		return suspensionReasonDescription;
	}
	/**
	 * @param suspensionReasonDescription the suspensionReasonDescription to set
	 */
	public void setSuspensionReasonDescription(String suspensionReasonDescription) {
		this.suspensionReasonDescription = suspensionReasonDescription;
	}
	/**
	 * @return the rules
	 */
	public Rules getRules() {
		return rules;
	}
	/**
	 * @param rules the rules to set
	 */
	public void setRules(Rules rules) {
		this.rules = rules;
	}
	/**
	 * @return the forAgreements
	 */
	public ForAgreement[] getForAgreements() {
		return forAgreements;
	}
	/**
	 * @param forAgreements the forAgreements to set
	 */
	public void setForAgreements(ForAgreement[] forAgreements) {
		this.forAgreements = forAgreements;
	}
	/**
	 * @return the allowedStaffWorkRoles
	 */
	public AllowedStaffWorkRole[] getAllowedStaffWorkRoles() {
		return allowedStaffWorkRoles;
	}
	/**
	 * @param allowedStaffWorkRoles the allowedStaffWorkRoles to set
	 */
	public void setAllowedStaffWorkRoles(AllowedStaffWorkRole[] allowedStaffWorkRoles) {
		this.allowedStaffWorkRoles = allowedStaffWorkRoles;
	}
	/**
	 * @return the effects
	 */
	public Effects getEffects() {
		return effects;
	}
	/**
	 * @param effects the effects to set
	 */
	public void setEffects(Effects effects) {
		this.effects = effects;
	}
	/**
	 * @return the glCode
	 */
	public GlCodeInfo getGlCode() {
		return glCode;
	}
	/**
	 * @param glCode the glCode to set
	 */
	public void setGlCode(GlCodeInfo glCode) {
		this.glCode = glCode;
	}
	
}
