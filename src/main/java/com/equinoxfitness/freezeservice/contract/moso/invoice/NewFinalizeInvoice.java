package com.equinoxfitness.freezeservice.contract.moso.invoice;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class NewFinalizeInvoice{
	@JsonProperty("Payments")
	private List<PaymentTender> payments;

	@JsonProperty("BusinessUnitCode")
	private String businessUnitCode;
	
	@JsonProperty("TargetDate")
	private String targetDate;

	@JsonProperty("MemberId")
	private String memberId;

	@JsonProperty("AccountId")
	private Integer accountId;

	@JsonProperty("Items")
	private List<NewInvoiceItem> items;

	@JsonProperty("CurrencyCode")
	private String currencyCode;

	@JsonProperty("Comments")
	private String comments;

	@JsonProperty("TaxExemptId")
	private Integer taxExemptId;

	@JsonProperty("EmployeeAuthorizationPin")
	private String employeeAuthorizationPin;

	public List<PaymentTender> getPayments() {
		return payments;
	}

	public void setPayments(List<PaymentTender> payments) {
		this.payments = payments;
	}

	public String getBusinessUnitCode() {
		return businessUnitCode;
	}

	public void setBusinessUnitCode(String businessUnitCode) {
		this.businessUnitCode = businessUnitCode;
	}

	public String getTargetDate() {
		return targetDate;
	}

	public void setTargetDate(String targetDate) {
		this.targetDate = targetDate;
	}

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public Integer getAccountId() {
		return accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	public List<NewInvoiceItem> getItems() {
		return items;
	}

	public void setItems(List<NewInvoiceItem> items) {
		this.items = items;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Integer getTaxExemptId() {
		return taxExemptId;
	}

	public void setTaxExemptId(Integer taxExemptId) {
		this.taxExemptId = taxExemptId;
	}

	public String getEmployeeAuthorizationPin() {
		return employeeAuthorizationPin;
	}

	public void setEmployeeAuthorizationPin(String employeeAuthorizationPin) {
		this.employeeAuthorizationPin = employeeAuthorizationPin;
	}
}
