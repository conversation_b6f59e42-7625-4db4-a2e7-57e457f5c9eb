/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class TaxRate {

	@JsonProperty("Id")
	Integer id;
	@JsonProperty("Rate")
	Number rate;
	@JsonProperty("TaxGroup")
	TaxGroup taxGroup;

	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * @return the rate
	 */
	public Number getRate() {
		return rate;
	}

	/**
	 * @param rate
	 *            the rate to set
	 */
	public void setRate(Number rate) {
		this.rate = rate;
	}

	/**
	 * @return the taxGroup
	 */
	public TaxGroup getTaxGroup() {
		return taxGroup;
	}

	/**
	 * @param taxGroup
	 *            the taxGroup to set
	 */
	public void setTaxGroup(TaxGroup taxGroup) {
		this.taxGroup = taxGroup;
	}

}
