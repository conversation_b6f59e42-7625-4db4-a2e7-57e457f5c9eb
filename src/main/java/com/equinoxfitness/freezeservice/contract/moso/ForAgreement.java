/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ForAgreement {

	@JsonProperty("AgreementID")
	Integer agreementID;
	@JsonProperty("AgreementName")
	String AgreementName;
	/**
	 * @return the agreementID
	 */
	public Integer getAgreementID() {
		return agreementID;
	}
	/**
	 * @param agreementID the agreementID to set
	 */
	public void setAgreementID(Integer agreementID) {
		this.agreementID = agreementID;
	}
	/**
	 * @return the agreementName
	 */
	public String getAgreementName() {
		return AgreementName;
	}
	/**
	 * @param agreementName the agreementName to set
	 */
	public void setAgreementName(String agreementName) {
		AgreementName = agreementName;
	}
	
}
