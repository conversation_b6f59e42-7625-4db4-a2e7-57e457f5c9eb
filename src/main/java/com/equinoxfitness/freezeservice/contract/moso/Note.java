package com.equinoxfitness.freezeservice.contract.moso;

import com.equinoxfitness.common.moso.contract.BaseOutput;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Note extends BaseOutput{
	@JsonProperty("Id")
	private String id;
	@JsonProperty("Content")
	private String content;
	@JsonProperty("NoteTypeId")
	private Integer noteTypeId;
	@JsonProperty("ContactTypeId")
	private Integer ContactTypeId;
	@JsonProperty("IsCheckinAlert")
	private boolean isCheckinAlert;
	private String errorMessage;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public Integer getNoteTypeId() {
		return noteTypeId;
	}
	public void setNoteTypeId(Integer noteTypeId) {
		this.noteTypeId = noteTypeId;
	}
	public Integer getContactTypeId() {
		return ContactTypeId;
	}
	public void setContactTypeId(Integer contactTypeId) {
		ContactTypeId = contactTypeId;
	}
	public boolean isCheckinAlert() {
		return isCheckinAlert;
	}
	public void setCheckinAlert(boolean isCheckinAlert) {
		this.isCheckinAlert = isCheckinAlert;
	}
	public String getErrorMessage() {
		return errorMessage;
	}
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	
	  
	
}
