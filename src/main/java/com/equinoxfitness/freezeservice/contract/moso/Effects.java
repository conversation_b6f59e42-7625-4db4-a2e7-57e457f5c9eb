/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Effects {

	@JsonProperty("StopsBilling")
	Boolean stopsBilling;
	@JsonProperty("ChargesOneTimeFee")
	Boolean chargesOneTimeFee;
	@JsonProperty("OneTimeFeeItem")
	String oneTimeFeeItem;
	@JsonProperty("ChargesRecurringFee")
	Boolean chargesRecurringFee;
	@JsonProperty("FeeType")
	String feeType;
	@JsonProperty("RecurringFeePercent")
	Number recurringFeePercent;
	@JsonProperty("RecurringFeeItem")
	String recurringFeeItem;
	@JsonProperty("PreventsActivityUse")
	Boolean preventsActivityUse;
	@JsonProperty("ExtendsActivityExpirationDate")
	Boolean extendsActivityExpirationDate;
	@JsonProperty("ExtendsOblDateOnRecurring")
	Boolean extendsOblDateOnRecurring;
	/**
	 * @return the stopsBilling
	 */
	public Boolean getStopsBilling() {
		return stopsBilling;
	}
	/**
	 * @param stopsBilling the stopsBilling to set
	 */
	public void setStopsBilling(Boolean stopsBilling) {
		this.stopsBilling = stopsBilling;
	}
	/**
	 * @return the chargesOneTimeFee
	 */
	public Boolean getChargesOneTimeFee() {
		return chargesOneTimeFee;
	}
	/**
	 * @param chargesOneTimeFee the chargesOneTimeFee to set
	 */
	public void setChargesOneTimeFee(Boolean chargesOneTimeFee) {
		this.chargesOneTimeFee = chargesOneTimeFee;
	}
	/**
	 * @return the oneTimeFeeItem
	 */
	public String getOneTimeFeeItem() {
		return oneTimeFeeItem;
	}
	/**
	 * @param oneTimeFeeItem the oneTimeFeeItem to set
	 */
	public void setOneTimeFeeItem(String oneTimeFeeItem) {
		this.oneTimeFeeItem = oneTimeFeeItem;
	}
	/**
	 * @return the chargesRecurringFee
	 */
	public Boolean getChargesRecurringFee() {
		return chargesRecurringFee;
	}
	/**
	 * @param chargesRecurringFee the chargesRecurringFee to set
	 */
	public void setChargesRecurringFee(Boolean chargesRecurringFee) {
		this.chargesRecurringFee = chargesRecurringFee;
	}
	/**
	 * @return the feeType
	 */
	public String getFeeType() {
		return feeType;
	}
	/**
	 * @param feeType the feeType to set
	 */
	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}
	/**
	 * @return the recurringFeePercent
	 */
	public Number getRecurringFeePercent() {
		return recurringFeePercent;
	}
	/**
	 * @param recurringFeePercent the recurringFeePercent to set
	 */
	public void setRecurringFeePercent(Number recurringFeePercent) {
		this.recurringFeePercent = recurringFeePercent;
	}
	/**
	 * @return the recurringFeeItem
	 */
	public String getRecurringFeeItem() {
		return recurringFeeItem;
	}
	/**
	 * @param recurringFeeItem the recurringFeeItem to set
	 */
	public void setRecurringFeeItem(String recurringFeeItem) {
		this.recurringFeeItem = recurringFeeItem;
	}
	/**
	 * @return the preventsActivityUse
	 */
	public Boolean getPreventsActivityUse() {
		return preventsActivityUse;
	}
	/**
	 * @param preventsActivityUse the preventsActivityUse to set
	 */
	public void setPreventsActivityUse(Boolean preventsActivityUse) {
		this.preventsActivityUse = preventsActivityUse;
	}
	/**
	 * @return the extendsActivityExpirationDate
	 */
	public Boolean getExtendsActivityExpirationDate() {
		return extendsActivityExpirationDate;
	}
	/**
	 * @param extendsActivityExpirationDate the extendsActivityExpirationDate to set
	 */
	public void setExtendsActivityExpirationDate(Boolean extendsActivityExpirationDate) {
		this.extendsActivityExpirationDate = extendsActivityExpirationDate;
	}
	/**
	 * @return the extendsOblDateOnRecurring
	 */
	public Boolean getExtendsOblDateOnRecurring() {
		return extendsOblDateOnRecurring;
	}
	/**
	 * @param extendsOblDateOnRecurring the extendsOblDateOnRecurring to set
	 */
	public void setExtendsOblDateOnRecurring(Boolean extendsOblDateOnRecurring) {
		this.extendsOblDateOnRecurring = extendsOblDateOnRecurring;
	}
	
}
