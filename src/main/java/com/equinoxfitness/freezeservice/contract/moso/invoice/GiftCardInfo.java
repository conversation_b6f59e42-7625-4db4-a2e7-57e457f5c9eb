package com.equinoxfitness.freezeservice.contract.moso.invoice;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GiftCardInfo {
	@JsonProperty("Amount")
	private BigDecimal amount;

	@JsonProperty("ExpirationDate")
	private String expirationDate;

	@JsonProperty("GiftCardNumber")
	private String giftCardNumber;

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public String getGiftCardNumber() {
		return giftCardNumber;
	}

	public void setGiftCardNumber(String giftCardNumber) {
		this.giftCardNumber = giftCardNumber;
	}
}
