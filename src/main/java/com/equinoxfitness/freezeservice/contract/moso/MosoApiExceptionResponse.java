/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.moso;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class MosoApiExceptionResponse implements Serializable {
private static final long serialVersionUID = 1L;
    
    private String Message;
    private String Data;
    
    public String getMessage() {
        return Message;
    }
    public void setMessage(String message) {
        Message = message;
    }
    public String getData() {
        return Data;
    }
    public void setData(String data) {
        Data = data;
    }
    
    public String getErrorMessage(){
        return "Message-"+getMessage()+", Data-"+getData();
    }

}
