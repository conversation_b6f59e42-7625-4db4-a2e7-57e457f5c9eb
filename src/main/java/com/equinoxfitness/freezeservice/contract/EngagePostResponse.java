package com.equinoxfitness.freezeservice.contract;

import java.io.Serializable;

public class EngagePostResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private String id;
	private boolean success;
	private Object errors;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public Object getErrors() {
		return errors;
	}
	public void setErrors(Object errors) {
		this.errors = errors;
	}
	
	@Override
	public String toString() {
		return "EngagePostResponse [id=" + id + ", success=" + success + ", errors=" + errors + "]";
	}
	
	
}
