package com.equinoxfitness.freezeservice.contract;

import java.math.BigDecimal;
import java.util.List;

public class MemberFreezeCase {
	
	private String clubId;
	private String description;
	private String mosoId;	
	private String source;
	private String freezeReason;
	private int durationMonths;
	private Boolean freezeFeeWaived;
	private BigDecimal freezeFees;
	private int durationDays;
	private String emailAddress;
	private String memberFirstName;
	private String startDate;
	private String countryCode;
	private boolean pilotFreezeMedFee;
	private List<FreezeDocuments> freezeDocuments;

	public int getDurationDays() {
		return durationDays;
	}
	public void setDurationDays(int durationDays) {
		this.durationDays = durationDays;
	}
	
	public String getClubId() {
		return clubId;
	}
	public String getDescription() {
		return description;
	}
	public String getMosoId() {
		return mosoId;
	}
	public String getSource() {
		return source;
	}
	public void setClubId(String clubId) {
		this.clubId = clubId;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public void setMosoId(String mosoId) {
		this.mosoId = mosoId;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getFreezeReason() {
		return freezeReason;
	}
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	public int getDurationMonths() {
		return durationMonths;
	}
	public void setDurationMonths(int durationMonths) {
		this.durationMonths = durationMonths;
	}
	public Boolean getFreezeFeeWaived() {
		return freezeFeeWaived;
	}
	public void setFreezeFeeWaived(Boolean freezeFeeWaived) {
		this.freezeFeeWaived = freezeFeeWaived;
	}
	public BigDecimal getFreezeFees() {
		return freezeFees;
	}
	public void setFreezeFees(BigDecimal freezeFees) {
		this.freezeFees = freezeFees;
	}
	public String getEmailAddress() {
		return emailAddress;
	}
	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	public String getMemberFirstName() {
		return memberFirstName;
	}

	public void setMemberFirstName(String memberFirstName) {
		this.memberFirstName = memberFirstName;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public boolean isPilotFreezeMedFee() {
		return pilotFreezeMedFee;
	}

	public void setPilotFreezeMedFee(boolean pilotFreezeMedFee) {
		this.pilotFreezeMedFee = pilotFreezeMedFee;
	}

	public List<FreezeDocuments> getFreezeDocuments() {
		return freezeDocuments;
	}
	public void setFreezeDocuments(List<FreezeDocuments> freezeDocuments) {
		this.freezeDocuments = freezeDocuments;
	}
}
