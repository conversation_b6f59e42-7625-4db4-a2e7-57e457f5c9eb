package com.equinoxfitness.freezeservice.contract;

import java.math.BigDecimal;
import java.util.Date;

public class FreezeMembershipInputV3 extends BaseRequest {

	private String facilityId;
	private String mosoId;
	private String freezeReason;
	private Date requestedDate;
	private Date clubOpenDate;
	private Date freezeEndDate;
	private String comments;
	private String source;
	private String correlationId;
	private boolean waveFreezeFee;
	private BigDecimal freezeFees=BigDecimal.ZERO;

	public String getFacilityId() {
		return facilityId;
	}

	public void setFacilityId(String facilityId) {
		this.facilityId = facilityId;
	}

	public String getMosoId() {
		return mosoId;
	}

	public void setMosoId(String mosoId) {
		this.mosoId = mosoId;
	}

	public String getFreezeReason() {
		return freezeReason;
	}

	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}

	public Date getRequestedDate() {
		return requestedDate;
	}

	public void setRequestedDate(Date requestedDate) {
		this.requestedDate = requestedDate;
	}

	public Date getClubOpenDate() {
		return clubOpenDate;
	}

	public void setClubOpenDate(Date clubOpenDate) {
		this.clubOpenDate = clubOpenDate;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getCorrelationId() {
		return correlationId;
	}

	public void setCorrelationId(String correlationId) {
		this.correlationId = correlationId;
	}

	public Date getFreezeEndDate() {
		return freezeEndDate;
	}

	public void setFreezeEndDate(Date freezeEndDate) {
		this.freezeEndDate = freezeEndDate;
	}

	public boolean isWaveFreezeFee() {
		return waveFreezeFee;
	}

	public void setWaveFreezeFee(boolean waveFreezeFee) {
		this.waveFreezeFee = waveFreezeFee;
	}

	public BigDecimal getFreezeFees() {
		return freezeFees;
	}

	public void setFreezeFees(BigDecimal freezeFees) {
		this.freezeFees = freezeFees;
	}

}
