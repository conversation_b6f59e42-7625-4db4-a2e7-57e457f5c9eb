/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import com.equinoxfitness.commons.output.BaseResponse;

/**
 * <AUTHOR>
 *
 */
public class EncryptOutput extends BaseResponse{

	private String creditCardToken;

	/**
	 * @return the creditCardToken
	 */
	public String getCreditCardToken() {
		return creditCardToken;
	}

	/**
	 * @param creditCardToken the creditCardToken to set
	 */
	public void setCreditCardToken(String creditCardToken) {
		this.creditCardToken = creditCardToken;
	}
	
}
