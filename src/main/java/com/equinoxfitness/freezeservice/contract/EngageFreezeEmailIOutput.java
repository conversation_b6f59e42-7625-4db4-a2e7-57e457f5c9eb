package com.equinoxfitness.freezeservice.contract;

import com.equinoxfitness.commons.output.BaseResponse;

public class EngageFreezeEmailIOutput extends BaseResponse {

	private static final long serialVersionUID = 7643321228755035657L;

	private boolean success;
	private String message;
	private String token;
	public boolean isSuccess() {
		return success;
	}
	public String getMessage() {
		return message;
	}
	public String getToken() {
		return token;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public void setToken(String token) {
		this.token = token;
	}

	@Override
	public String toString() {
		return "EngageFreezeEmailIOutput [success=" + success + ", message=" + message + ", token=" + token + "]";
	}	

}
