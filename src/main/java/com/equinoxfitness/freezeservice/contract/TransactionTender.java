/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class TransactionTender {
	
	private CreditCardTransaction creditCardTransaction;
	private String additionalInfo;
	private BigDecimal tenderAmount;
	private int tenderTypeId;
	/**
	 * @return the creditCardTransaction
	 */
	public CreditCardTransaction getCreditCardTransaction() {
		return creditCardTransaction;
	}
	/**
	 * @param creditCardTransaction the creditCardTransaction to set
	 */
	public void setCreditCardTransaction(CreditCardTransaction creditCardTransaction) {
		this.creditCardTransaction = creditCardTransaction;
	}
	/**
	 * @return the additionalInfo
	 */
	public String getAdditionalInfo() {
		return additionalInfo;
	}
	/**
	 * @param additionalInfo the additionalInfo to set
	 */
	public void setAdditionalInfo(String additionalInfo) {
		this.additionalInfo = additionalInfo;
	}
	/**
	 * @return the tenderAmount
	 */
	public BigDecimal getTenderAmount() {
		return tenderAmount;
	}
	/**
	 * @param tenderAmount the tenderAmount to set
	 */
	public void setTenderAmount(BigDecimal tenderAmount) {
		this.tenderAmount = tenderAmount;
	}
	/**
	 * @return the tenderTypeID
	 */
	public int getTenderTypeID() {
		return tenderTypeId;
	}
	/**
	 * @param tenderTypeID the tenderTypeID to set
	 */
	public void setTenderTypeID(int tenderTypeID) {
		this.tenderTypeId = tenderTypeID;
	}

}
