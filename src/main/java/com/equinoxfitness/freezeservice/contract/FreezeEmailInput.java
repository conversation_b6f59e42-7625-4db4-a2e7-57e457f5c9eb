package com.equinoxfitness.freezeservice.contract;

import java.io.Serializable;
import java.util.Date;

public class FreezeEmailInput implements Serializable {

    private static final long serialVersionUID = 1L;

    private String memberName;
    private String memberId;
    private String freezeReason;
    private String email;
    private String phone;
    private String facilityId;
    private String country;
    private boolean inObligation;
    private Date startDate;
    private boolean eligibleForFreeze;
    private boolean joinedAfterContractChange;
    private String memberSince;
    private String source;
    private boolean skipEmail;
    private String ownerId;
    private String caseOrigin; //JIRA# WEB-4183, NMNM-3151. Introduced this field as the existing 'source' field can't be used because equinox.com has custom logic.
    
    public String getMemberId() {
        return memberId;
    }

    public String getFreezeReason() {
        return freezeReason;
    }

    public String getEmail() {
        return email;
    }

    public String getFacilityId() {
        return facilityId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public void setFreezeReason(String freezeReason) {
        this.freezeReason = freezeReason;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setFacilityId(String facilityId) {
        this.facilityId = facilityId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public boolean isInObligation() {
        return inObligation;
    }

    public void setInObligation(boolean inObligation) {
        this.inObligation = inObligation;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public boolean isEligibleForFreeze() {
        return eligibleForFreeze;
    }

    public boolean isJoinedAfterContractChange() {
        return joinedAfterContractChange;
    }

    public String getMemberSince() {
        return memberSince;
    }

    public void setEligibleForFreeze(boolean eligibleForFreeze) {
        this.eligibleForFreeze = eligibleForFreeze;
    }

    public void setJoinedAfterContractChange(boolean joinedAfterContractChange) {
        this.joinedAfterContractChange = joinedAfterContractChange;
    }

    public void setMemberSince(String memberSince) {
        this.memberSince = memberSince;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public boolean isSkipEmail() {
        return skipEmail;
    }

    public void setSkipEmail(boolean skipEmail) {
        this.skipEmail = skipEmail;
    }

	public String getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(String ownerId) {
		this.ownerId = ownerId;
	}

	public String getCaseOrigin() {
		return caseOrigin;
	}

	public void setCaseOrigin(String caseOrigin) {
		this.caseOrigin = caseOrigin;
	}
}