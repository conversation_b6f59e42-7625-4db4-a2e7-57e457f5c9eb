package com.equinoxfitness.freezeservice.contract;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class Feedback {	

	@JsonProperty("Description")
	private String description;
	@JsonProperty("Do_Not_Contact__c")
	private Boolean doNotContact;
	@JsonProperty("Club__r")
	private Club__r clubC;
	@JsonProperty("Contact")
	private FeedbackContact contactC;
	@JsonProperty("Feedback_Source__c")
	private String feedbackSource;
	@JsonProperty("Origin")
	private String origin;
	@JsonProperty("Type")
	private String feedbackType;
	@JsonProperty("Categories__c")
	private String category;
	@JsonProperty("Sub_Categories__c")
	private String subCategory;
	@JsonProperty("Subject")
	private String subject;
	@JsonProperty("Follow_Up_Required__c")
	private Boolean followUpRequired;
	@JsonProperty("Freeze_Fee_Waived__c")
	private boolean freezeFeeWaived;
	@JsonProperty("Freeze_Fee__c")
	private String freezeFees;
	@JsonProperty("Duration__c")
	private Integer freezeDurationMonths;
	@JsonProperty("Freeze_Duration_Days__c")
	private Integer freezeDurationDays;
	@JsonProperty("Freeze_Reason__c")
	private String freezeReason;
	@JsonProperty("Status")
	private String status;
	@JsonProperty("RecordTypeId")
	private String recordTypeId;
	@JsonProperty("Freeze_Start_Date__c")
	private String freezeStartDate;
	@JsonProperty("OwnerId")
	private String ownerId;
	@JsonProperty("Freeze_requested_date__c")
	private String freezeRequestedDate;
	
	public Integer getFreezeDurationDays() {
		return freezeDurationDays;
	}
	public void setFreezeDurationDays(Integer freezeDurationDays) {
		this.freezeDurationDays = freezeDurationDays;
	}
	
	public String getDescription() {
		return description;
	}
	public Boolean getDoNotContact() {
		return doNotContact;
	}
	public Club__r getClubC() {
		return clubC;
	}
	public FeedbackContact getContactC() {
		return contactC;
	}
	public String getFeedbackSource() {
		return feedbackSource;
	}
	public String getOrigin() {
		return origin;
	}
	public String getFeedbackType() {
		return feedbackType;
	}
	public String getCategory() {
		return category;
	}
	public String getSubCategory() {
		return subCategory;
	}
	public String getSubject() {
		return subject;
	}
	public Boolean getFollowUpRequired() {
		return followUpRequired;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public void setDoNotContact(Boolean doNotContact) {
		this.doNotContact = doNotContact;
	}
	public void setClubC(Club__r clubC) {
		this.clubC = clubC;
	}
	public void setContactC(FeedbackContact contactC) {
		this.contactC = contactC;
	}
	public void setFeedbackSource(String feedbackSource) {
		this.feedbackSource = feedbackSource;
	}
	public void setOrigin(String origin) {
		this.origin = origin;
	}
	public void setFeedbackType(String feedbackType) {
		this.feedbackType = feedbackType;
	}
	public void setCategory(String category) {
		this.category = category;
	}
	public void setSubCategory(String subCategory) {
		this.subCategory = subCategory;
	}
	public void setSubject(String subject) {
		this.subject = subject;
	}
	public void setFollowUpRequired(Boolean followUpRequired) {
		this.followUpRequired = followUpRequired;
	}
	public boolean isFreezeFeeWaived() {
		return freezeFeeWaived;
	}
	public void setFreezeFeeWaived(boolean freezeFeeWaived) {
		this.freezeFeeWaived = freezeFeeWaived;
	}
	public String getFreezeFees() {
		return freezeFees;
	}
	public void setFreezeFees(String freezeFees) {
		this.freezeFees = freezeFees;
	}
	public Integer getFreezeDurationMonths() {
		return freezeDurationMonths;
	}
	public void setFreezeDurationMonths(Integer freezeDurationMonths) {
		this.freezeDurationMonths = freezeDurationMonths;
	}
	public String getFreezeReason() {
		return freezeReason;
	}
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getRecordTypeId() {
		return recordTypeId;
	}
	public void setRecordTypeId(String recordTypeId) {
		this.recordTypeId = recordTypeId;
	}
	public String getFreezeStartDate() {
		return freezeStartDate;
	}
	public void setFreezeStartDate(String freezeStartDate) {
		this.freezeStartDate = freezeStartDate;
	}
	public String getOwnerId() {
		return ownerId;
	}
	public void setOwnerId(String ownerId) {
		this.ownerId = ownerId;
	}
	public String getFreezeRequestedDate() {
		return freezeRequestedDate;
	}
	public void setFreezeRequestedDate(String freezeRequestedDate) {
		this.freezeRequestedDate = freezeRequestedDate;
	}
	@Override
	public String toString() {
		return "Feedback [description=" + description + ", doNotContact=" + doNotContact + ", clubC=" + clubC
				+ ", contactC=" + contactC + ", feedbackSource=" + feedbackSource + ", origin=" + origin
				+ ", feedbackType=" + feedbackType + ", category=" + category + ", subCategory=" + subCategory
				+ ", subject=" + subject + ", followUpRequired=" + followUpRequired + ", freezeFeeWaived="
				+ freezeFeeWaived + ", freezeFees=" + freezeFees + ", freezeDurationMonths=" + freezeDurationMonths + ", freezeDurationDays=" + freezeDurationDays
				+ ", freezeReason=" + freezeReason + ", status=" + status + ", recordTypeId=" + recordTypeId
				+ ", freezeStartDate=" + freezeStartDate + ", ownerId=" + ownerId + ", freezeRequestedDate=" + freezeRequestedDate + "]";
	}
}
