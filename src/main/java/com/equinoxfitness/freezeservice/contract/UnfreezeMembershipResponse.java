package com.equinoxfitness.freezeservice.contract;

import com.equinoxfitness.commons.output.BaseResponse;

public class UnfreezeMembershipResponse extends BaseResponse {

	private static final long serialVersionUID = 1L;
	private boolean success;
	private String memberAgreementId;
	private String case_id;

	public String getMemberAgreementId() {
		return memberAgreementId;
	}

	public void setMemberAgreementId(String memberAgreementId) {
		this.memberAgreementId = memberAgreementId;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public String getCase_id() {
		return case_id;
	}

	public void setCase_id(String case_id) {
		this.case_id = case_id;
	}	

}
