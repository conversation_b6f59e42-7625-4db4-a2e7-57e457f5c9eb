package com.equinoxfitness.freezeservice.contract;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *
 */
public class BaseRequest {
	
	@ApiModelProperty(value = "Unique id of the thread")
	protected String threadId;
	
	@ApiModelProperty(value = "Invoke this opeartion as sync or async", allowableValues="true, false")
	protected boolean invokeAsync;
	
	@ApiModelProperty(value = "Unique identifier of the requester")
	protected String requestedBy;

	/**
	 * @return the threadId
	 */
	public String getThreadId() {
		return threadId;
	}

	/**
	 * @param threadId the threadId to set
	 */
	public void setThreadId(String threadId) {
		this.threadId = threadId;
	}

	/**
	 * @return the invokeAsync
	 */
	public boolean isInvokeAsync() {
		return invokeAsync;
	}

	/**
	 * @param invokeAsync the invokeAsync to set
	 */
	public void setInvokeAsync(boolean invokeAsync) {
		this.invokeAsync = invokeAsync;
	}

	/**
	 * @return the requestedBy
	 */
	public String getRequestedBy() {
		return requestedBy;
	}

	/**
	 * @param requestedBy the requestedBy to set
	 */
	public void setRequestedBy(String requestedBy) {
		this.requestedBy = requestedBy;
	}
}
