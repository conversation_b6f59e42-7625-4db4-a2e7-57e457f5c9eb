package com.equinoxfitness.freezeservice.contract;

import java.util.List;

public class TokenData {
	private String memberId;
	private String freezeReason;
	private String email;
	private String phone;
	private String facilityId;
	private String memberName;
	private String country;
	private boolean inObligation;
	private String startDate;
	private boolean eligibleForFreeze;
	private boolean joinedAfterContractChange;
	private String memberSince;
	private String source;
	private boolean canFreeze;
	private int freezeDurationInMonths;
	private String freezeDurationInDays;
	private String freezeDurationInDaysAllOptions;
	private boolean freezeDurationPilot;
	private boolean pilotFreezeDaysPerYear;
	private Double monthlyFeeRate;
	private boolean cancelFlow;
	private String caseOrigin; //JIRA# WEB-4183, NMNM-3151. Introduced this field as the existing 'source' field can't be used because equinox.com has custom logic.
	private boolean universityMember;
	private boolean pilotFreezeMedFee;
	
	public boolean isFreezeDurationPilot() {
		return freezeDurationPilot;
	}

	public void setFreezeDurationPilot(boolean freezeDurationPilot) {
		this.freezeDurationPilot = freezeDurationPilot;
	}

	public String getFreezeDurationInDays() {
		return freezeDurationInDays;
	}

	public void setFreezeDurationInDays(String freezeDurationInDays) {
		this.freezeDurationInDays = freezeDurationInDays;
	}

	public String getMemberId() {
		return memberId;
	}
	public String getFreezeReason() {
		return freezeReason;
	}
	public String getEmail() {
		return email;
	}
	public String getPhone() {
		return phone;
	}
	public String getFacilityId() {
		return facilityId;
	}
	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public void setFacilityId(String facilityId) {
		this.facilityId = facilityId;
	}
	public String getMemberName() {
		return memberName;
	}
	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}	
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}	
	public boolean isInObligation() {
		return inObligation;
	}
	public void setInObligation(boolean inObligation) {
		this.inObligation = inObligation;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}	
	public boolean isEligibleForFreeze() {
		return eligibleForFreeze;
	}
	public boolean isJoinedAfterContractChange() {
		return joinedAfterContractChange;
	}
	public String getMemberSince() {
		return memberSince;
	}
	public void setEligibleForFreeze(boolean eligibleForFreeze) {
		this.eligibleForFreeze = eligibleForFreeze;
	}
	public void setJoinedAfterContractChange(boolean joinedAfterContractChange) {
		this.joinedAfterContractChange = joinedAfterContractChange;
	}
	public void setMemberSince(String memberSince) {
		this.memberSince = memberSince;
	}		
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public boolean isCanFreeze() {
		return canFreeze;
	}
	public void setCanFreeze(boolean canFreeze) {
		this.canFreeze = canFreeze;
	}
	public int getFreezeDurationInMonths() {
		return freezeDurationInMonths;
	}
	public void setFreezeDurationInMonths(int freezeDurationInMonths) {
		this.freezeDurationInMonths = freezeDurationInMonths;
	}

	public boolean isPilotFreezeDaysPerYear() {
		return pilotFreezeDaysPerYear;
	}

	public void setPilotFreezeDaysPerYear(boolean pilotFreezeDaysPerYear) {
		this.pilotFreezeDaysPerYear = pilotFreezeDaysPerYear;
	}

	public Double getMonthlyFeeRate() {
		return monthlyFeeRate;
	}

	public void setMonthlyFeeRate(Double monthlyFeeRate) {
		this.monthlyFeeRate = monthlyFeeRate;
	}

	public boolean isCancelFlow() {
		return cancelFlow;
	}

	public void setCancelFlow(boolean cancelFlow) {
		this.cancelFlow = cancelFlow;
	}

	public String getFreezeDurationInDaysAllOptions() {
		return freezeDurationInDaysAllOptions;
	}

	public void setFreezeDurationInDaysAllOptions(String freezeDurationInDaysAllOptions) {
		this.freezeDurationInDaysAllOptions = freezeDurationInDaysAllOptions;
	}

	public String getCaseOrigin() {
		return caseOrigin;
	}

	public void setCaseOrigin(String caseOrigin) {
		this.caseOrigin = caseOrigin;
	}

	public boolean isUniversityMember() {
		return universityMember;
	}

	public void setUniversityMember(boolean universityMember) {
		this.universityMember = universityMember;
	}

	public boolean isPilotFreezeMedFee() {
		return pilotFreezeMedFee;
	}

	public void setPilotFreezeMedFee(boolean pilotFreezeMedFee) {
		this.pilotFreezeMedFee = pilotFreezeMedFee;
	}

	@Override
	public String toString() {
		return "TokenData [memberId=" + memberId + ", freezeReason=" + freezeReason + ", email=" + email + ", phone="
				+ phone + ", facilityId=" + facilityId + ", memberName=" + memberName + ", country=" + country
				+ ", inObligation=" + inObligation + ", startDate=" + startDate + ", eligibleForFreeze="
				+ eligibleForFreeze + ", joinedAfterContractChange=" + joinedAfterContractChange + ", memberSince="
				+ memberSince + ", source=" + source + ", canFreeze=" + canFreeze + ", freezeDurationInMonths="
				+ freezeDurationInMonths + ", freezeDurationInDays=" + freezeDurationInDays
				+ ", freezeDurationInDaysAllOptions=" + freezeDurationInDaysAllOptions + ", freezeDurationPilot="
				+ freezeDurationPilot + ", pilotFreezeDaysPerYear=" + pilotFreezeDaysPerYear + ", monthlyFeeRate="
				+ monthlyFeeRate + ", cancelFlow=" + cancelFlow + ", caseOrigin=" + caseOrigin + " , universityMember="
				+ universityMember + " , pilotFreezeMedFee=" + pilotFreezeMedFee + "]";
	}
}
