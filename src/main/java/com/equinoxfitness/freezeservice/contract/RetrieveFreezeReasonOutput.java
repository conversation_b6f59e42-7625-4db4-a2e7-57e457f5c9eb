/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import com.equinoxfitness.commons.output.BaseResponse;

/**
 * <AUTHOR>
 *
 */
public class RetrieveFreezeReasonOutput extends BaseResponse{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String suspensionReasonName;
	private double oneTimeFee;
	/**
	 * @return the suspensionReasonName
	 */
	public String getSuspensionReasonName() {
		return suspensionReasonName;
	}
	/**
	 * @param suspensionReasonName the suspensionReasonName to set
	 */
	public void setSuspensionReasonName(String suspensionReasonName) {
		this.suspensionReasonName = suspensionReasonName;
	}
	/**
	 * @return the oneTimeFee
	 */
	public double getOneTimeFee() {
		return oneTimeFee;
	}
	/**
	 * @param oneTimeFee the oneTimeFee to set
	 */
	public void setOneTimeFee(double oneTimeFee) {
		this.oneTimeFee = oneTimeFee;
	}
	

}
