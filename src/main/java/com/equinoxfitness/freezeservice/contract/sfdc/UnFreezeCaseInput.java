package com.equinoxfitness.freezeservice.contract.sfdc;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UnFreezeCaseInput {

	@JsonProperty("Id")
	private String id;

	@JsonProperty("ContactId")
	private String contactId;

	@JsonProperty("RecordTypeId")
	private String recordTypeId;

	@JsonProperty("Status")
	private String status;

	@JsonProperty("Cancellation_type__c")
	private String cancellationType;

	@JsonProperty("Origin")
	private String origin;

	@JsonProperty("Subject")
	private String subject;

	@JsonProperty("Description")
	private String description;

	@JsonProperty("Categories__c")
	private String categories;

	@JsonProperty("Club__c")
	private String clubId;

	@JsonProperty("Date_Of_Incident__c")
	private String dateOfIncident;

	@JsonProperty("Sub_Categories__c")
	private String subCategories;

	@JsonProperty("MemberId__c")
	private String memberId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getContactId() {
		return contactId;
	}

	public void setContactId(String contactId) {
		this.contactId = contactId;
	}

	public String getRecordTypeId() {
		return recordTypeId;
	}

	public void setRecordTypeId(String recordTypeId) {
		this.recordTypeId = recordTypeId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getCancellationType() {
		return cancellationType;
	}

	public void setCancellationType(String cancellationType) {
		this.cancellationType = cancellationType;
	}

	public String getOrigin() {
		return origin;
	}

	public void setOrigin(String origin) {
		this.origin = origin;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getCategories() {
		return categories;
	}

	public void setCategories(String categories) {
		this.categories = categories;
	}

	public String getClubId() {
		return clubId;
	}

	public void setClubId(String clubId) {
		this.clubId = clubId;
	}

	public String getDateOfIncident() {
		return dateOfIncident;
	}

	public void setDateOfIncident(String dateOfIncident) {
		this.dateOfIncident = dateOfIncident;
	}

	public String getSubCategories() {
		return subCategories;
	}

	public void setSubCategories(String subCategories) {
		this.subCategories = subCategories;
	}

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

}
