package com.equinoxfitness.freezeservice.contract.sfdc;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class NoteOutput {
	
	@JsonProperty("id")
	private String id;
	@JsonProperty("success")
	private Boolean success;
	@JsonProperty("errors")
	private List<Object> errors = null;

	@JsonProperty("id")
	public String getId() {
	return id;
	}

	@JsonProperty("id")
	public void setId(String id) {
	this.id = id;
	}

	@JsonProperty("success")
	public Boolean getSuccess() {
	return success;
	}

	@JsonProperty("success")
	public void setSuccess(Boolean success) {
	this.success = success;
	}

	@JsonProperty("errors")
	public List<Object> getErrors() {
	return errors;
	}

	@JsonProperty("errors")
	public void setErrors(List<Object> errors) {
	this.errors = errors;
	}

}
