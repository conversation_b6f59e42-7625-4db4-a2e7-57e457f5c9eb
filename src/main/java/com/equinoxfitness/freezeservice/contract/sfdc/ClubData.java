/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.sfdc;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class ClubData {
	 @JsonProperty("totalSize")
	    private Integer totalSize;
	    @JsonProperty("done")
	    private Boolean done;
	    @JsonProperty("records")
	    private List<ClubRecord> records = null;
		/**
		 * @return the totalSize
		 */
		public Integer getTotalSize() {
			return totalSize;
		}
		/**
		 * @param totalSize the totalSize to set
		 */
		public void setTotalSize(Integer totalSize) {
			this.totalSize = totalSize;
		}
		/**
		 * @return the done
		 */
		public Boolean getDone() {
			return done;
		}
		/**
		 * @param done the done to set
		 */
		public void setDone(Boolean done) {
			this.done = done;
		}
		/**
		 * @return the records
		 */
		public List<ClubRecord> getRecords() {
			return records;
		}
		/**
		 * @param records the records to set
		 */
		public void setRecords(List<ClubRecord> records) {
			this.records = records;
		}
	    

}
