/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.sfdc;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
@JsonInclude(Include.NON_NULL)
public class Case {
	@JsonProperty("Id")
	String id;
	@JsonProperty("ContactId")
	String contactId;
	@JsonProperty("RecordTypeId")
	String recordTypeId;
	@JsonProperty("Status")
	String status;
	@JsonProperty("Reason")
	String reason;
	@JsonProperty("Origin")
	String origin;
	@JsonProperty("Subject")
	String subject;
	@JsonProperty("Description")
	String description;
	@JsonProperty("Categories__c")
	String categories__c;
	@JsonProperty("Club__c")
	String club;
	@JsonProperty("Do_Not_Contact__c")
	boolean doNotContact;
	@JsonProperty("Feedback_Source__c")
	String feedbackSource;
	@JsonProperty("Regional_Manager_Email__c")
	String regionlaManagerEmail;
	@JsonProperty("Sub_Categories__c")
	String subCategories;
	@JsonProperty("MemberId__c")
	String memberId;
	@JsonProperty("Duration__c")
	String duration;
	@JsonProperty("Freeze_Duration_Days__c")
	String freezeDurationDays;
	@JsonProperty("Freeze_End_Date__c")
	Date freezeEndDate;
	@JsonProperty("Freeze_fee__c")
	double freezeFee;
	@JsonProperty("Freeze_Reason__c")
	String freezeReason;
	@JsonProperty("Freeze_Start_Date__c")
	Date freezeStartDate;
	@JsonProperty("Is_Exception__c")
	boolean isException;
	@JsonProperty("Cancellation_type__c")
	private String cancellationType;
	@JsonProperty("Date_Of_Incident__c")
	private Date dateOfIncident;
	@JsonProperty("Contact")
	private Contact contact;
	@JsonProperty("Alternate_Reason__c")
	String alternateReason;

	public String getContactId() {
		return contactId;
	}
	public void setContactId(String contactId) {
		this.contactId = contactId;
	}
	/**
	 * @return the recordTypeId
	 */
	public String getRecordTypeId() {
		return recordTypeId;
	}
	/**
	 * @param recordTypeId the recordTypeId to set
	 */
	public void setRecordTypeId(String recordTypeId) {
		this.recordTypeId = recordTypeId;
	}
	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * @param status the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * @return the reason
	 */
	public String getReason() {
		return reason;
	}
	/**
	 * @param reason the reason to set
	 */
	public void setReason(String reason) {
		this.reason = reason;
	}
	/**
	 * @return the origin
	 */
	public String getOrigin() {
		return origin;
	}
	/**
	 * @param origin the origin to set
	 */
	public void setOrigin(String origin) {
		this.origin = origin;
	}
	/**
	 * @return the subject
	 */
	public String getSubject() {
		return subject;
	}
	/**
	 * @param subject the subject to set
	 */
	public void setSubject(String subject) {
		this.subject = subject;
	}
	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}
	/**
	 * @param description the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}
	/**
	 * @return the categories__c
	 */
	public String getCategories__c() {
		return categories__c;
	}
	/**
	 * @param categories__c the categories__c to set
	 */
	public void setCategories__c(String categories__c) {
		this.categories__c = categories__c;
	}
	/**
	 * @return the club
	 */
	public String getClub() {
		return club;
	}
	/**
	 * @param club the club to set
	 */
	public void setClub(String club) {
		this.club = club;
	}
	/**
	 * @return the doNotContact
	 */
	public boolean isDoNotContact() {
		return doNotContact;
	}
	/**
	 * @param doNotContact the doNotContact to set
	 */
	public void setDoNotContact(boolean doNotContact) {
		this.doNotContact = doNotContact;
	}
	/**
	 * @return the feedbackSource
	 */
	public String getFeedbackSource() {
		return feedbackSource;
	}
	/**
	 * @param feedbackSource the feedbackSource to set
	 */
	public void setFeedbackSource(String feedbackSource) {
		this.feedbackSource = feedbackSource;
	}
	/**
	 * @return the regionlaManagerEmail
	 */
	public String getRegionlaManagerEmail() {
		return regionlaManagerEmail;
	}
	/**
	 * @param regionlaManagerEmail the regionlaManagerEmail to set
	 */
	public void setRegionlaManagerEmail(String regionlaManagerEmail) {
		this.regionlaManagerEmail = regionlaManagerEmail;
	}
	/**
	 * @return the subCategories
	 */
	public String getSubCategories() {
		return subCategories;
	}
	/**
	 * @param subCategories the subCategories to set
	 */
	public void setSubCategories(String subCategories) {
		this.subCategories = subCategories;
	}
	/**
	 * @return the memberId
	 */
	public String getMemberId() {
		return memberId;
	}
	/**
	 * @param memberId the memberId to set
	 */
	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}
	/**
	 * @return the duration
	 */
	public String getDuration() {
		return duration;
	}
	/**
	 * @param duration the duration to set
	 */
	public void setDuration(String duration) {
		this.duration = duration;
	}
	/**
	 * @return the freezeEndDate
	 */
	public Date getFreezeEndDate() {
		return freezeEndDate;
	}
	/**
	 * @param freezeEndDate the freezeEndDate to set
	 */
	public void setFreezeEndDate(Date freezeEndDate) {
		this.freezeEndDate = freezeEndDate;
	}
	/**
	 * @return the freezeFee
	 */
	public double getFreezeFee() {
		return freezeFee;
	}
	/**
	 * @param freezeFee the freezeFee to set
	 */
	public void setFreezeFee(double freezeFee) {
		this.freezeFee = freezeFee;
	}
	/**
	 * @return the freezeReason
	 */
	public String getFreezeReason() {
		return freezeReason;
	}
	/**
	 * @param freezeReason the freezeReason to set
	 */
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	/**
	 * @return the freezeStartDate
	 */
	public Date getFreezeStartDate() {
		return freezeStartDate;
	}
	/**
	 * @param freezeStartDate the freezeStartDate to set
	 */
	public void setFreezeStartDate(Date freezeStartDate) {
		this.freezeStartDate = freezeStartDate;
	}
	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}
	public String getCancellationType() {
		return cancellationType;
	}
	public void setCancellationType(String cancellationType) {
		this.cancellationType = cancellationType;
	}
	/**
	 * @return the isException
	 */
	public boolean getIsException() {
		return isException;
	}
	/**
	 * @param isException the isException to set
	 */
	public void setIsException(boolean isException) {
		this.isException = isException;
	}
	public Date getDateOfIncident() {
		return dateOfIncident;
	}
	public void setDateOfIncident(Date dateOfIncident) {
		this.dateOfIncident = dateOfIncident;
	}
	public Contact getContact() {
		return contact;
	}
	public void setContact(Contact contact) {
		this.contact = contact;
	}

	public String getFreezeDurationDays() {
		return freezeDurationDays;
	}

	public void setFreezeDurationDays(String freezeDurationDays) {
		this.freezeDurationDays = freezeDurationDays;
	}

	public String getAlternateReason() {
		return alternateReason;
	}

	public void setAlternateReason(String alternateReason) {
		this.alternateReason = alternateReason;
	}
}
