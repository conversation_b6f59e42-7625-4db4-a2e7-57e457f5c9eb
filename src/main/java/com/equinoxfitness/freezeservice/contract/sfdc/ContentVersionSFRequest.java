package com.equinoxfitness.freezeservice.contract.sfdc;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ContentVersionSFRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5927693526701199731L;

	@JsonProperty("Title")
	private String title;
	
	@JsonProperty("PathOnClient")
	private String pathOnClient;
	
	@JsonProperty("VersionData")
	private String versionData;
	
	@JsonProperty("FirstPublishLocationId")
	private String firstPublishLocationId;
	
	@JsonProperty("Company_Name__c")
	private String companyName;
	
	@JsonProperty("Company_Phone__c")
	private String companyPhone;
	
	@JsonProperty("Visitor_Name__c")
	private String visitorName;
	
	@JsonProperty("Visitor_Phone__c")
	private String visitorPhone;
	
//	@JsonProperty("Waiver_Club__r")
//	private Club waiverClub2;
	
	@JsonProperty("Waiver_Club__c")
	private String waiverClub;
	
//	@JsonProperty("Club__r")
//	private Club club;
	
	@JsonProperty("Time_In__c")
	private String timeIn;
	
	@JsonProperty("Reason_for_Entry__c")
	private String reasonForEntry;
	
	@JsonProperty("Case__c")
	private String caseId;
	
	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @param title the title to set
	 */
	public void setTitle(String title) {
		this.title = title;
	}

	/**
	 * @return the pathOnClient
	 */
	public String getPathOnClient() {
		return pathOnClient;
	}

	/**
	 * @param pathOnClient the pathOnClient to set
	 */
	public void setPathOnClient(String pathOnClient) {
		this.pathOnClient = pathOnClient;
	}

	/**
	 * @return the versionData
	 */
	public String getVersionData() {
		return versionData;
	}

	/**
	 * @param versionData the versionData to set
	 */
	public void setVersionData(String versionData) {
		this.versionData = versionData;
	}

	/**
	 * @return the firstPublishLocationId
	 */
	public String getFirstPublishLocationId() {
		return firstPublishLocationId;
	}

	/**
	 * @param firstPublishLocationId the firstPublishLocationId to set
	 */
	public void setFirstPublishLocationId(String firstPublishLocationId) {
		this.firstPublishLocationId = firstPublishLocationId;
	}

	/**
	 * @return the companyName
	 */
	public String getCompanyName() {
		return companyName;
	}

	/**
	 * @param companyName the companyName to set
	 */
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	/**
	 * @return the companyPhone
	 */
	public String getCompanyPhone() {
		return companyPhone;
	}

	/**
	 * @param companyPhone the companyPhone to set
	 */
	public void setCompanyPhone(String companyPhone) {
		this.companyPhone = companyPhone;
	}

	/**
	 * @return the visitorName
	 */
	public String getVisitorName() {
		return visitorName;
	}

	/**
	 * @param visitorName the visitorName to set
	 */
	public void setVisitorName(String visitorName) {
		this.visitorName = visitorName;
	}

	/**
	 * @return the visitorPhone
	 */
	public String getVisitorPhone() {
		return visitorPhone;
	}

	/**
	 * @param visitorPhone the visitorPhone to set
	 */
	public void setVisitorPhone(String visitorPhone) {
		this.visitorPhone = visitorPhone;
	}

	/**
	 * @return the waiverClub
	 */
	public String getWaiverClub() {
		return waiverClub;
	}

	/**
	 * @param waiverClub the waiverClub to set
	 */
	public void setWaiverClub(String waiverClub) {
		this.waiverClub = waiverClub;
	}

	/**
	 * @return the timeIn
	 */
	public String getTimeIn() {
		return timeIn;
	}

	/**
	 * @param timeIn the timeIn to set
	 */
	public void setTimeIn(String timeIn) {
		this.timeIn = timeIn;
	}

	/**
	 * @return the reasonForEntry
	 */
	public String getReasonForEntry() {
		return reasonForEntry;
	}

	/**
	 * @param reasonForEntry the reasonForEntry to set
	 */
	public void setReasonForEntry(String reasonForEntry) {
		this.reasonForEntry = reasonForEntry;
	}

	public String getCaseId() {
		return caseId;
	}

	public void setCaseId(String caseId) {
		this.caseId = caseId;
	}

	@Override
	public String toString() {
		return "ContentVersionSFRequest [title=" + title + ", pathOnClient=" + pathOnClient + ", versionData="
				+ versionData + ", firstPublishLocationId=" + firstPublishLocationId + ", companyName=" + companyName
				+ ", companyPhone=" + companyPhone + ", visitorName=" + visitorName + ", visitorPhone=" + visitorPhone
				+ ", waiverClub=" + waiverClub + ", timeIn=" + timeIn + ", reasonForEntry=" + reasonForEntry
				+ ", caseId=" + caseId + "]";
	}	
}
