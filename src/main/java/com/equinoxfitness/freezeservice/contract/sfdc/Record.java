
package com.equinoxfitness.freezeservice.contract.sfdc;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Record {

	@JsonProperty("attributes")
	private Attributes attributes;

	@JsonProperty("Id")
	private String id;

	@JsonProperty("Moso_Id__c")
	private String mosoId;
	
	@JsonProperty("FirstName")
    private String firstName;
	
	@JsonProperty("Corp_Group__r")
	private CorpGroup corpGroup;

	public String getMosoId() {
		return mosoId;
	}

	public void setMosoId(String mosoId) {
		this.mosoId = mosoId;
	}

	@JsonProperty("attributes")
	public Attributes getAttributes() {
		return attributes;
	}

	@JsonProperty("attributes")
	public void setAttributes(Attributes attributes) {
		this.attributes = attributes;
	}

	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}
	
	@JsonProperty("FirstName")
    public String getFirstName() {
        return firstName;
    }

    @JsonProperty("FirstName")
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    
    
    public void setCorpGroup(CorpGroup corpGroup) {
		this.corpGroup = corpGroup;
	}
    
    public CorpGroup getCorpGroup() {
		return corpGroup;
	}

}
