/**
 * 
 */
package com.equinoxfitness.freezeservice.contract.sfdc;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class Contact__r {
	
	@JsonProperty("attributes")
    private Attributes attributes;
	@JsonProperty("Id")
	private String id;

	/**
	 * @return the attributes
	 */
	public Attributes getAttributes() {
		return attributes;
	}
	/**
	 * @param attributes the attributes to set
	 */
	public void setAttributes(Attributes attributes) {
		this.attributes = attributes;
	}
	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}
}
