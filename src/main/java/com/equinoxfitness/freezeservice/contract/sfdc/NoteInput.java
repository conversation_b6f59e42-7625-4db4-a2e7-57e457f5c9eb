package com.equinoxfitness.freezeservice.contract.sfdc;

import com.fasterxml.jackson.annotation.JsonProperty;

public class NoteInput {
	
	@JsonProperty("Id")
	private Object id;
	@JsonProperty("ParentId")
	private String parentId;
	@JsonProperty("Title")
	private String title;
	@JsonProperty("Body")
	private String body;

	@JsonProperty("Id")
	public Object getId() {
	return id;
	}

	@JsonProperty("Id")
	public void setId(Object id) {
	this.id = id;
	}

	@JsonProperty("ParentId")
	public String getParentId() {
	return parentId;
	}

	@JsonProperty("ParentId")
	public void setParentId(String parentId) {
	this.parentId = parentId;
	}

	@JsonProperty("Title")
	public String getTitle() {
	return title;
	}

	@JsonProperty("Title")
	public void setTitle(String title) {
	this.title = title;
	}

	@JsonProperty("Body")
	public String getBody() {
	return body;
	}

	@JsonProperty("Body")
	public void setBody(String body) {
	this.body = body;
	}

}
