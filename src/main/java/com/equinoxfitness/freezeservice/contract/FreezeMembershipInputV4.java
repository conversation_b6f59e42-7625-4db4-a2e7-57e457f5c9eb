package com.equinoxfitness.freezeservice.contract;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class FreezeMembershipInputV4 extends BaseRequest {
	
	private String mosoId;
	private String freezeReasonId;
	private Date requestedDate;
	private Date startDate;
	private Date endDate;
	private String description;
	private boolean useCOF;
	private BigDecimal freezeFees = BigDecimal.ZERO;
	private boolean skipValidations;
	private int durationMonths=0;
	private String source;
	private boolean waveFreezeFee;
	private String facilityId;
	private TransactionTender[] transactionTenders;
	private String correlationId;
	private String memberSince;
	private boolean holdBillingInObFlag;
	private boolean inObligationFlag;
	private boolean joinedAfterContractChange;
	private boolean eligibleForFreeze;
	private String emailAddress; 
	private List<FreezeDocuments> freezeDocuments;
	private boolean isPIF;
	private int durationDays=0;
	private boolean cancelFlow;
	private String caseOrigin; //JIRA# WEB-4183, NMNM-3151. Introduced this field as the existing 'source' field can't be used because equinox.com has custom logic.
	private boolean universityMember;
	private boolean pilotFreezeMedFee;

	public int getDurationDays() {
		return durationDays;
	}
	public void setDurationDays(int durationDays) {
		this.durationDays = durationDays;
	}
	public String getMosoId() {
		return mosoId;
	}
	public void setMosoId(String mosoId) {
		this.mosoId = mosoId;
	}
	public String getFreezeReasonId() {
		return freezeReasonId;
	}
	public void setFreezeReasonId(String freezeReasonId) {
		this.freezeReasonId = freezeReasonId;
	}
	public Date getRequestedDate() {
		return requestedDate;
	}
	public void setRequestedDate(Date requestedDate) {
		this.requestedDate = requestedDate;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public boolean isUseCOF() {
		return useCOF;
	}
	public void setUseCOF(boolean useCOF) {
		this.useCOF = useCOF;
	}
	public BigDecimal getFreezeFees() {
		return freezeFees;
	}
	public void setFreezeFees(BigDecimal freezeFees) {
		this.freezeFees = freezeFees;
	}
	public boolean isSkipValidations() {
		return skipValidations;
	}
	public void setSkipValidations(boolean skipValidations) {
		this.skipValidations = skipValidations;
	}
	public int getDurationMonths() {
		return durationMonths;
	}
	public void setDurationMonths(int durationMonths) {
		this.durationMonths = durationMonths;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public boolean isWaveFreezeFee() {
		return waveFreezeFee;
	}
	public void setWaveFreezeFee(boolean waveFreezeFee) {
		this.waveFreezeFee = waveFreezeFee;
	}
	public String getFacilityId() {
		return facilityId;
	}
	public void setFacilityId(String facilityId) {
		this.facilityId = facilityId;
	}
	public TransactionTender[] getTransactionTenders() {
		return transactionTenders;
	}
	public void setTransactionTenders(TransactionTender[] transactionTenders) {
		this.transactionTenders = transactionTenders;
	}
	public String getCorrelationId() {
		return correlationId;
	}
	public void setCorrelationId(String correlationId) {
		this.correlationId = correlationId;
	}
	public String getMemberSince() {
		return memberSince;
	}
	public void setMemberSince(String memberSince) {
		this.memberSince = memberSince;
	}
	public boolean isHoldBillingInObFlag() {
		return holdBillingInObFlag;
	}
	public void setHoldBillingInObFlag(boolean holdBillingInObFlag) {
		this.holdBillingInObFlag = holdBillingInObFlag;
	}
	public boolean isInObligationFlag() {
		return inObligationFlag;
	}
	public void setInObligationFlag(boolean inObligationFlag) {
		this.inObligationFlag = inObligationFlag;
	}
	public boolean isJoinedAfterContractChange() {
		return joinedAfterContractChange;
	}
	public void setJoinedAfterContractChange(boolean joinedAfterContractChange) {
		this.joinedAfterContractChange = joinedAfterContractChange;
	}
	public boolean isEligibleForFreeze() {
		return eligibleForFreeze;
	}
	public void setEligibleForFreeze(boolean eligibleForFreeze) {
		this.eligibleForFreeze = eligibleForFreeze;
	}
	public String getEmailAddress() {
		return emailAddress;
	}
	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}
	public List<FreezeDocuments> getFreezeDocuments() {
		return freezeDocuments;
	}
	public void setFreezeDocuments(List<FreezeDocuments> freezeDocuments) {
		this.freezeDocuments = freezeDocuments;
	}
	public boolean isPIF() {
		return isPIF;
	}
	public void setPIF(boolean isPIF) {
		this.isPIF = isPIF;
	}

	public boolean isCancelFlow() {
		return cancelFlow;
	}

	public void setCancelFlow(boolean cancelFlow) {
		this.cancelFlow = cancelFlow;
	}
	public String getCaseOrigin() {
		return caseOrigin;
	}
	public void setCaseOrigin(String caseOrigin) {
		this.caseOrigin = caseOrigin;
	}

	public boolean isUniversityMember() {
		return universityMember;
	}

	public void setUniversityMember(boolean universityMember) {
		this.universityMember = universityMember;
	}

	public boolean isPilotFreezeMedFee() {
		return pilotFreezeMedFee;
	}

	public void setPilotFreezeMedFee(boolean pilotFreezeMedFee) {
		this.pilotFreezeMedFee = pilotFreezeMedFee;
	}
}
