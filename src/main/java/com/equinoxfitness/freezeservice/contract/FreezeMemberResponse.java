/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import com.equinoxfitness.commons.output.BaseResponse;

/**
 * <AUTHOR>
 *
 */
public class FreezeMemberResponse extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private boolean requestReceivedSuccessfully;
	private EligibilityLackReason failureReasons;
	
	private int suspensionId;
	private String suspendedAgreementName;
	private String memberFirstName;
	private String memberLastName;
	private String suspensionReasonName;
	private String freezeExtensionStartDate;
	private String freezeExtensionEndDate;
	private String freezeFeeItem;
	private String freezeFeeAmount;
	private String freezeFeeStartDate;
	private String freezeStatus;
	/**
	 * @return the requestReceivedSuccessfully
	 */
	public boolean isRequestReceivedSuccessfully() {
		return requestReceivedSuccessfully;
	}
	/**
	 * @param requestReceivedSuccessfully the requestReceivedSuccessfully to set
	 */
	public void setRequestReceivedSuccessfully(boolean requestReceivedSuccessfully) {
		this.requestReceivedSuccessfully = requestReceivedSuccessfully;
	}
	/**
	 * @return the failureReasons
	 */
	public EligibilityLackReason getFailureReasons() {
		return failureReasons;
	}
	/**
	 * @param failureReasons the failureReasons to set
	 */
	public void setFailureReasons(EligibilityLackReason failureReasons) {
		this.failureReasons = failureReasons;
	}
	/**
	 * @return the suspensionId
	 */
	public int getSuspensionId() {
		return suspensionId;
	}
	/**
	 * @param suspensionId the suspensionId to set
	 */
	public void setSuspensionId(int suspensionId) {
		this.suspensionId = suspensionId;
	}
	/**
	 * @return the suspendedAgreementName
	 */
	public String getSuspendedAgreementName() {
		return suspendedAgreementName;
	}
	/**
	 * @param suspendedAgreementName the suspendedAgreementName to set
	 */
	public void setSuspendedAgreementName(String suspendedAgreementName) {
		this.suspendedAgreementName = suspendedAgreementName;
	}
	/**
	 * @return the memberFirstName
	 */
	public String getMemberFirstName() {
		return memberFirstName;
	}
	/**
	 * @param memberFirstName the memberFirstName to set
	 */
	public void setMemberFirstName(String memberFirstName) {
		this.memberFirstName = memberFirstName;
	}
	/**
	 * @return the memberLastName
	 */
	public String getMemberLastName() {
		return memberLastName;
	}
	/**
	 * @param memberLastName the memberLastName to set
	 */
	public void setMemberLastName(String memberLastName) {
		this.memberLastName = memberLastName;
	}
	/**
	 * @return the suspensionReasonName
	 */
	public String getSuspensionReasonName() {
		return suspensionReasonName;
	}
	/**
	 * @param suspensionReasonName the suspensionReasonName to set
	 */
	public void setSuspensionReasonName(String suspensionReasonName) {
		this.suspensionReasonName = suspensionReasonName;
	}
	/**
	 * @return the freezeExtensionStartDate
	 */
	public String getFreezeExtensionStartDate() {
		return freezeExtensionStartDate;
	}
	/**
	 * @param freezeExtensionStartDate the freezeExtensionStartDate to set
	 */
	public void setFreezeExtensionStartDate(String freezeExtensionStartDate) {
		this.freezeExtensionStartDate = freezeExtensionStartDate;
	}
	/**
	 * @return the freezeExtensionEndDate
	 */
	public String getFreezeExtensionEndDate() {
		return freezeExtensionEndDate;
	}
	/**
	 * @param freezeExtensionEndDate the freezeExtensionEndDate to set
	 */
	public void setFreezeExtensionEndDate(String freezeExtensionEndDate) {
		this.freezeExtensionEndDate = freezeExtensionEndDate;
	}
	/**
	 * @return the freezeFeeItem
	 */
	public String getFreezeFeeItem() {
		return freezeFeeItem;
	}
	/**
	 * @param freezeFeeItem the freezeFeeItem to set
	 */
	public void setFreezeFeeItem(String freezeFeeItem) {
		this.freezeFeeItem = freezeFeeItem;
	}
	/**
	 * @return the freezeFeeAmount
	 */
	public String getFreezeFeeAmount() {
		return freezeFeeAmount;
	}
	/**
	 * @param freezeFeeAmount the freezeFeeAmount to set
	 */
	public void setFreezeFeeAmount(String freezeFeeAmount) {
		this.freezeFeeAmount = freezeFeeAmount;
	}
	/**
	 * @return the freezeFeeStartDate
	 */
	public String getFreezeFeeStartDate() {
		return freezeFeeStartDate;
	}
	/**
	 * @param freezeFeeStartDate the freezeFeeStartDate to set
	 */
	public void setFreezeFeeStartDate(String freezeFeeStartDate) {
		this.freezeFeeStartDate = freezeFeeStartDate;
	}
	/**
	 * @return the freezeStatus
	 */
	public String getFreezeStatus() {
		return freezeStatus;
	}
	/**
	 * @param freezeStatus the freezeStatus to set
	 */
	public void setFreezeStatus(String freezeStatus) {
		this.freezeStatus = freezeStatus;
	}
	
	
}
