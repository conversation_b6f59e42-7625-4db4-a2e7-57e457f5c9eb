package com.equinoxfitness.freezeservice.contract;

public class TokenRequest {	
	
	private String tokenType;	
	private String requestor;	
	private TokenData tokenData;	 
	private String IssuedDate;
	
	public String getTokenType() {
		return tokenType;
	}
	public String getRequestor() {
		return requestor;
	}
	public TokenData getTokenData() {
		return tokenData;
	}
	public String getIssuedDate() {
		return IssuedDate;
	}
	public void setTokenType(String tokenType) {
		this.tokenType = tokenType;
	}
	public void setRequestor(String requestor) {
		this.requestor = requestor;
	}
	public void setTokenData(TokenData tokenData) {
		this.tokenData = tokenData;
	}
	public void setIssuedDate(String issuedDate) {
		IssuedDate = issuedDate;
	}
	@Override
	public String toString() {
		return "TokenRequest [tokenType=" + tokenType + ", requestor=" + requestor + ", tokenData=" + tokenData
				+ ", IssuedDate=" + IssuedDate + "]";
	}	
}
