/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class FreezeExtensionInputV2 extends BaseRequest{
	private String mosoId;
	private String facilityId;
	private String freezeReason;
	private Date freezeExtensionEndDate;
	private int extensionDurationMonths;
	private boolean waiveOffExtensionFee;
	private String notesType;
	private String notes;
	private String contactType;
	private String correlationId;
	
	
	public String getMosoId() {
		return mosoId;
	}
	public void setMosoId(String mosoId) {
		this.mosoId = mosoId;
	}
	/**
	 * @return the facilityId
	 */
	public String getFacilityId() {
		return facilityId;
	}
	/**
	 * @param facilityId the facilityId to set
	 */
	public void setFacilityId(String facilityId) {
		this.facilityId = facilityId;
	}
	/**
	 * @return the freezeReason
	 */
	public String getFreezeReason() {
		return freezeReason;
	}
	/**
	 * @param freezeReason the freezeReason to set
	 */
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	/**
	 * @return the freezeExtensionEndDate
	 */
	public Date getFreezeExtensionEndDate() {
		return freezeExtensionEndDate;
	}
	/**
	 * @param freezeExtensionEndDate the freezeExtensionEndDate to set
	 */
	public void setFreezeExtensionEndDate(Date freezeExtensionEndDate) {
		this.freezeExtensionEndDate = freezeExtensionEndDate;
	}
	/**
	 * @return the extensionDurationMonths
	 */
	public int getExtensionDurationMonths() {
		return extensionDurationMonths;
	}
	/**
	 * @param extensionDurationMonths the extensionDurationMonths to set
	 */
	public void setExtensionDurationMonths(int extensionDurationMonths) {
		this.extensionDurationMonths = extensionDurationMonths;
	}
	/**
	 * @return the waiveOffExtensionFee
	 */
	public boolean isWaiveOffExtensionFee() {
		return waiveOffExtensionFee;
	}
	/**
	 * @param waiveOffExtensionFee the waiveOffExtensionFee to set
	 */
	public void setWaiveOffExtensionFee(boolean waiveOffExtensionFee) {
		this.waiveOffExtensionFee = waiveOffExtensionFee;
	}
	/**
	 * @return the notesType
	 */
	public String getNotesType() {
		return notesType;
	}
	/**
	 * @param notesType the notesType to set
	 */
	public void setNotesType(String notesType) {
		this.notesType = notesType;
	}
	/**
	 * @return the notes
	 */
	public String getNotes() {
		return notes;
	}
	/**
	 * @param notes the notes to set
	 */
	public void setNotes(String notes) {
		this.notes = notes;
	}
	/**
	 * @return the contactType
	 */
	public String getContactType() {
		return contactType;
	}
	/**
	 * @param contactType the contactType to set
	 */
	public void setContactType(String contactType) {
		this.contactType = contactType;
	}
	public String getCorrelationId() {
		return correlationId;
	}
	public void setCorrelationId(String correlationId) {
		this.correlationId = correlationId;
	}
}
