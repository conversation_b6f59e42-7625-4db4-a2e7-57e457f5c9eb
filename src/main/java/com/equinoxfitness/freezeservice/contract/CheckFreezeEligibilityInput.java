/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class CheckFreezeEligibilityInput extends BaseRequest{
	
	private String memberId;
	private String freezeReason;
	private Date requestedDate;
	private Date startDate;
	private Date endDate;
	private int durationMonths;
	private int durationDays;
	/**
	 * @return the memberId
	 */
	public String getMemberId() {
		return memberId;
	}
	/**
	 * @param memberId the memberId to set
	 */
	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}
	/**
	 * @return the freezeReason
	 */
	public String getFreezeReason() {
		return freezeReason;
	}
	/**
	 * @param freezeReason the freezeReason to set
	 */
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	/**
	 * @return the requestedDate
	 */
	public Date getRequestedDate() {
		return requestedDate;
	}
	/**
	 * @param requestedDate the requestedDate to set
	 */
	public void setRequestedDate(Date requestedDate) {
		this.requestedDate = requestedDate;
	}
	/**
	 * @return the startDate
	 */
	public Date getStartDate() {
		return startDate;
	}
	/**
	 * @param startDate the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	/**
	 * @return the endDate
	 */
	public Date getEndDate() {
		return endDate;
	}
	/**
	 * @param endDate the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	/**
	 * @return the durationMonths
	 */
	public int getDurationMonths() {
		return durationMonths;
	}
	/**
	 * @param durationMonths the durationMonths to set
	 */
	public void setDurationMonths(int durationMonths) {
		this.durationMonths = durationMonths;
	}
	/**
	 * @return the durationDays
	 */
	public int getDurationDays() {
		return durationDays;
	}
	/**
	 * @param durationDays the durationDays to set
	 */
	public void setDurationDays(int durationDays) {
		this.durationDays = durationDays;
	}
}
