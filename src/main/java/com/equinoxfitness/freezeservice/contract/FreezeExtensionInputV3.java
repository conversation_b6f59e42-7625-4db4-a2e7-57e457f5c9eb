package com.equinoxfitness.freezeservice.contract;

import java.util.Date;

public class FreezeExtensionInputV3 extends BaseRequest{
	
	private String mosoId;
	private String facilityId;
	private String freezeReason;
	private Date freezeExtensionEndDate;
	private int extensionDurationMonths;
	private boolean waiveOffExtensionFee;
	private String notesType;
	private String notes;
	private String contactType;
	private String correlationId;
	private int extensionDurationDays;
	private String caseOrigin; //JIRA# WEB-4183, NMNM-3151. Introduced this field as the existing 'source' field can't be used because equinox.com has custom logic.
	
	public int getExtensionDurationDays() {
		return extensionDurationDays;
	}
	public void setExtensionDurationDays(int extensionDurationDays) {
		this.extensionDurationDays = extensionDurationDays;
	}
	public String getMosoId() {
		return mosoId;
	}
	public void setMosoId(String mosoId) {
		this.mosoId = mosoId;
	}
	public String getFacilityId() {
		return facilityId;
	}
	public void setFacilityId(String facilityId) {
		this.facilityId = facilityId;
	}
	public String getFreezeReason() {
		return freezeReason;
	}
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	public Date getFreezeExtensionEndDate() {
		return freezeExtensionEndDate;
	}
	public void setFreezeExtensionEndDate(Date freezeExtensionEndDate) {
		this.freezeExtensionEndDate = freezeExtensionEndDate;
	}
	public int getExtensionDurationMonths() {
		return extensionDurationMonths;
	}
	public void setExtensionDurationMonths(int extensionDurationMonths) {
		this.extensionDurationMonths = extensionDurationMonths;
	}
	public boolean isWaiveOffExtensionFee() {
		return waiveOffExtensionFee;
	}
	public void setWaiveOffExtensionFee(boolean waiveOffExtensionFee) {
		this.waiveOffExtensionFee = waiveOffExtensionFee;
	}
	public String getNotesType() {
		return notesType;
	}
	public void setNotesType(String notesType) {
		this.notesType = notesType;
	}
	public String getNotes() {
		return notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}
	public String getContactType() {
		return contactType;
	}
	public void setContactType(String contactType) {
		this.contactType = contactType;
	}
	public String getCorrelationId() {
		return correlationId;
	}
	public void setCorrelationId(String correlationId) {
		this.correlationId = correlationId;
	}
	public String getCaseOrigin() {
		return caseOrigin;
	}
	public void setCaseOrigin(String caseOrigin) {
		this.caseOrigin = caseOrigin;
	}
}
