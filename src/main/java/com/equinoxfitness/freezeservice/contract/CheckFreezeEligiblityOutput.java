/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import com.equinoxfitness.commons.output.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class CheckFreezeEligiblityOutput extends BaseResponse {

	private static final long serialVersionUID = 1L;
	private boolean eligibleForFreeze;
	private String status;
	private boolean inObligation;
	private boolean joinedAfterContractChange;
	private String memberSince;
	private boolean isPIF;
	private String email;
	private boolean eligibleForOneMonthFreeze;
	private List<String> freezeDurationInDays;
	private String durationType;
	private boolean pilotFreezeDaysPerYear = false;
	private double monthlyFeeRate;
	private List<String> freezeDurationInDaysAllOptions;

	public List<String> getFreezeDurationInDays() {
		return freezeDurationInDays;
	}

	public void setFreezeDurationInDays(List<String> freezeDurationInDays) {
		this.freezeDurationInDays = freezeDurationInDays;
	}

	public String getDurationType() {
		return durationType;
	}

	public void setDurationType(String durationType) {
		this.durationType = durationType;
	}

	public boolean isEligibleForFreeze() {
		return eligibleForFreeze;
	}
	public String getStatus() {
		return status;
	}
	public boolean isInObligation() {
		return inObligation;
	}
	public boolean isJoinedAfterContractChange() {
		return joinedAfterContractChange;
	}
	public String getMemberSince() {
		return memberSince;
	}
	public void setEligibleForFreeze(boolean eligibleForFreeze) {
		this.eligibleForFreeze = eligibleForFreeze;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public void setInObligation(boolean inObligation) {
		this.inObligation = inObligation;
	}
	public void setJoinedAfterContractChange(boolean joinedAfterContractChange) {
		this.joinedAfterContractChange = joinedAfterContractChange;
	}
	public void setMemberSince(String memberSince) {
		this.memberSince = memberSince;
	}	
	public Boolean getIsPIF() {
		return isPIF;
	}
	public void setIsPIF(Boolean isPIF) {
		this.isPIF = isPIF;
	}	
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public boolean isEligibleForOneMonthFreeze() {
		return eligibleForOneMonthFreeze;
	}
	public void setEligibleForOneMonthFreeze(boolean eligibleForOneMonthFreeze) {
		this.eligibleForOneMonthFreeze = eligibleForOneMonthFreeze;
	}
  
	public boolean isPilotFreezeDaysPerYear() {
		return pilotFreezeDaysPerYear;
	}
	public void setPilotFreezeDaysPerYear(boolean pilotFreezeDaysPerYear) {
		this.pilotFreezeDaysPerYear = pilotFreezeDaysPerYear;
	}

	public double getMonthlyFeeRate() {
		return monthlyFeeRate;
	}

	public void setMonthlyFeeRate(double monthlyFeeRate) {
		this.monthlyFeeRate = monthlyFeeRate;
	}

	public List<String> getFreezeDurationInDaysAllOptions() {
		return freezeDurationInDaysAllOptions;
	}

	public void setFreezeDurationInDaysAllOptions(List<String> freezeDurationInDaysAllOptions) {
		this.freezeDurationInDaysAllOptions = freezeDurationInDaysAllOptions;
	}

	@Override
	public String toString() {
		return "CheckFreezeEligiblityOutput [eligibleForFreeze=" + eligibleForFreeze + ", status=" + status
				+ ", inObligation=" + inObligation + ", joinedAfterContractChange=" + joinedAfterContractChange
				+ ", memberSince=" + memberSince + ", isPIF=" + isPIF + ", email=" + email
				+ ", eligibleForOneMonthFreeze=" + eligibleForOneMonthFreeze + ", freezeDurationInDays="
				+ freezeDurationInDays + ", durationType=" + durationType + ", pilotFreezeDaysPerYear="
				+ pilotFreezeDaysPerYear + ", monthlyFeeRate=" + monthlyFeeRate + ", freezeDurationInDaysAllOptions="
				+ freezeDurationInDaysAllOptions + "]";
	}
}
