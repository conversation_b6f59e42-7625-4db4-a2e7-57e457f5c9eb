/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

/**
 * <AUTHOR>
 *
 */
public class EligibilityLackReason {
	
	private boolean byPassable;
	private String reasonMessage;
	/**
	 * @return the byPassable
	 */
	public boolean isByPassable() {
		return byPassable;
	}
	/**
	 * @param byPassable the byPassable to set
	 */
	public void setByPassable(boolean byPassable) {
		this.byPassable = byPassable;
	}
	/**
	 * @return the reasonMessage
	 */
	public String getReasonMessage() {
		return reasonMessage;
	}
	/**
	 * @param reasonMessage the reasonMessage to set
	 */
	public void setReasonMessage(String reasonMessage) {
		this.reasonMessage = reasonMessage;
	}
}
