package com.equinoxfitness.freezeservice.contract;

import java.math.BigDecimal;

public class MedicalFreezeEmailInput {
    private String memberId;
    private String emailAddress;
    private String memberFirstName;
    private String startDate;
    private String endDate;
    private int durationMonths;
    private boolean pilotFreezeMedFee;
    private BigDecimal freezeFees;
    private String countryCode;

    public MedicalFreezeEmailInput() {
        // Default constructor
    }

    // Getters and Setters
    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getMemberFirstName() {
        return memberFirstName;
    }

    public void setMemberFirstName(String memberFirstName) {
        this.memberFirstName = memberFirstName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public int getDurationMonths() {
        return durationMonths;
    }

    public void setDurationMonths(int durationMonths) {
        this.durationMonths = durationMonths;
    }

    public boolean isPilotFreezeMedFee() {
        return pilotFreezeMedFee;
    }

    public void setPilotFreezeMedFee(boolean pilotFreezeMedFee) {
        this.pilotFreezeMedFee = pilotFreezeMedFee;
    }

    public BigDecimal getFreezeFees() {
        return freezeFees;
    }

    public void setFreezeFees(BigDecimal freezeFees) {
        this.freezeFees = freezeFees;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
}
