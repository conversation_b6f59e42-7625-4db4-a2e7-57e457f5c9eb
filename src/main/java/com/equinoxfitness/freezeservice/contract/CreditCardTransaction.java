/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class CreditCardTransaction {
	
	private BigDecimal authorizationAmount;
	private String authorizationCode;
	private String isCreditCardPresent;
	private CreditCard creditCard;
	/**
	 * @return the authorizationAmount
	 */
	public BigDecimal getAuthorizationAmount() {
		return authorizationAmount;
	}
	/**
	 * @param authorizationAmount the authorizationAmount to set
	 */
	public void setAuthorizationAmount(BigDecimal authorizationAmount) {
		this.authorizationAmount = authorizationAmount;
	}
	/**
	 * @return the authorizationCode
	 */
	public String getAuthorizationCode() {
		return authorizationCode;
	}
	/**
	 * @param authorizationCode the authorizationCode to set
	 */
	public void setAuthorizationCode(String authorizationCode) {
		this.authorizationCode = authorizationCode;
	}
	/**
	 * @return the isCreditCardPresent
	 */
	public String getIsCreditCardPresent() {
		return isCreditCardPresent;
	}
	/**
	 * @param isCreditCardPresent the isCreditCardPresent to set
	 */
	public void setIsCreditCardPresent(String isCreditCardPresent) {
		this.isCreditCardPresent = isCreditCardPresent;
	}
	/**
	 * @return the creditCard
	 */
	public CreditCard getCreditCard() {
		return creditCard;
	}
	/**
	 * @param creditCard the creditCard to set
	 */
	public void setCreditCard(CreditCard creditCard) {
		this.creditCard = creditCard;
	}

}
