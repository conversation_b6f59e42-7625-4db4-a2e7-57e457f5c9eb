/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class FreezeMembershipInputV2 extends BaseRequest {

	private String mosoId;
	private String freezeReasonId;
	private Date requestedDate;
	private Date startDate;
	private Date endDate;
	private String description;
	private boolean useCOF;
	private BigDecimal freezeFees = BigDecimal.ZERO;
	private boolean skipValidations;
	private int durationMonths=0;
	private String source;
	private boolean waveFreezeFee;
	private String facilityId;
	private TransactionTender[] transactionTenders;
	private String correlationId;
	private String memberSince;
	private boolean holdBillingInObFlag;
	private boolean inObligationFlag;
	private boolean joinedAfterContractChange;
	private boolean eligibleForFreeze;
	private String emailAddress; 
	private List<FreezeDocuments> freezeDocuments;
	private boolean isPIF;
	private String caseOrigin; //JIRA# WEB-4183, NMNM-3151. Introduced this field as the existing 'source' field can't be used because equinox.com has custom logic.
	private boolean universityMember;
	private boolean pilotFreezeMedFee;
	private boolean cancelFlow;

	public String getMosoId() {
		return mosoId;
	}
	public void setMosoId(String mosoId) {
		this.mosoId = mosoId;
	}
	/**
	 * @return the freezeReasonId
	 */
	public String getFreezeReasonId() {
		return freezeReasonId;
	}
	/**
	 * @param freezeReasonId the freezeReasonId to set
	 */
	public void setFreezeReasonId(String freezeReasonId) {
		this.freezeReasonId = freezeReasonId;
	}
	/**
	 * @return the requestedDate
	 */
	public Date getRequestedDate() {
		return requestedDate;
	}
	/**
	 * @param requestedDate the requestedDate to set
	 */
	public void setRequestedDate(Date requestedDate) {
		this.requestedDate = requestedDate;
	}
	/**
	 * @return the startDate
	 */
	public Date getStartDate() {
		return startDate;
	}
	/**
	 * @param startDate the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	/**
	 * @return the endDate
	 */
	public Date getEndDate() {
		return endDate;
	}
	/**
	 * @param endDate the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}
	/**
	 * @param description the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}
	/**
	 * @return the useCOF
	 */
	public boolean isUseCOF() {
		return useCOF;
	}
	/**
	 * @param useCOF the useCOF to set
	 */
	public void setUseCOF(boolean useCOF) {
		this.useCOF = useCOF;
	}
	/**
	 * @return the freezeFees
	 */
	public BigDecimal getFreezeFees() {
		return freezeFees;
	}
	/**
	 * @param freezeFees the freezeFees to set
	 */
	public void setFreezeFees(BigDecimal freezeFees) {
		this.freezeFees = freezeFees;
	}
	/**
	 * @return the skipValidations
	 */
	public boolean isSkipValidations() {
		return skipValidations;
	}
	/**
	 * @param skipValidations the skipValidations to set
	 */
	public void setSkipValidations(boolean skipValidations) {
		this.skipValidations = skipValidations;
	}
	/**
	 * @return the durationMonths
	 */
	public int getDurationMonths() {
		return durationMonths;
	}
	/**
	 * @param durationMonths the durationMonths to set
	 */
	public void setDurationMonths(int durationMonths) {
		this.durationMonths = durationMonths;
	}
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}
	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}
	/**
	 * @return the transactionTenders
	 */
	public TransactionTender[] getTransactionTenders() {
		return transactionTenders;
	}
	/**
	 * @param transactionTenders the transactionTenders to set
	 */
	public void setTransactionTenders(TransactionTender[] transactionTenders) {
		this.transactionTenders = transactionTenders;
	}
	/**
	 * @return the waveFreezeFee
	 */
	public boolean isWaveFreezeFee() {
		return waveFreezeFee;
	}
	/**
	 * @param waveFreezeFee the waveFreezeFee to set
	 */
	public void setWaveFreezeFee(boolean waveFreezeFee) {
		this.waveFreezeFee = waveFreezeFee;
	}
	/**
	 * @return the facilityId
	 */
	public String getFacilityId() {
		return facilityId;
	}
	/**
	 * @param facilityId the facilityId to set
	 */
	public void setFacilityId(String facilityId) {
		this.facilityId = facilityId;
	}
	
	public String getCorrelationId() {
		return correlationId;
	}
	
	public void setCorrelationId(String correlationId) {
		this.correlationId = correlationId;
	}
	
	public boolean isHoldBillingInObFlag() {
		return holdBillingInObFlag;
	}
	
	public void setHoldBillingInObFlag(boolean holdBillingInObFlag) {
		this.holdBillingInObFlag = holdBillingInObFlag;
	}
	
	public boolean isInObligationFlag() {
		return inObligationFlag;
	}
	
	public void setInObligationFlag(boolean inObligationFlag) {
		this.inObligationFlag = inObligationFlag;
	}
	
	public boolean isJoinedAfterContractChange() {
		return joinedAfterContractChange;
	}
	
	public void setJoinedAfterContractChange(boolean joinedAfterContractChange) {
		this.joinedAfterContractChange = joinedAfterContractChange;
	}
	
	public String getMemberSince() {
		return memberSince;
	}
	
	public void setMemberSince(String memberSince) {
		this.memberSince = memberSince;
	}
	
	public boolean isEligibleForFreeze() {
		return eligibleForFreeze;
	}
	
	public void setEligibleForFreeze(boolean eligibleForFreeze) {
		this.eligibleForFreeze = eligibleForFreeze;
	}
	
	public String getEmailAddress() {
		return emailAddress;
	}
	
	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}
	
	public List<FreezeDocuments> getFreezeDocuments() {
		return freezeDocuments;
	}
	
	public void setFreezeDocuments(List<FreezeDocuments> freezeDocuments) {
		this.freezeDocuments = freezeDocuments;
	}
	
	public boolean isPIF() {
		return isPIF;
	}
	
	public void setPIF(boolean isPIF) {
		this.isPIF = isPIF;
	}
	public String getCaseOrigin() {
		return caseOrigin;
	}
	public void setCaseOrigin(String caseOrigin) {
		this.caseOrigin = caseOrigin;
	}

	public boolean isUniversityMember() {
		return universityMember;
	}

	public void setUniversityMember(boolean universityMember) {
		this.universityMember = universityMember;
	}

	public boolean isPilotFreezeMedFee() {
		return pilotFreezeMedFee;
	}

	public void setPilotFreezeMedFee(boolean pilotFreezeMedFee) {
		this.pilotFreezeMedFee = pilotFreezeMedFee;
  }
  
	public boolean isCancelFlow() {
		return cancelFlow;
	}

	public void setCancelFlow(boolean cancelFlow) {
		this.cancelFlow = cancelFlow;
	}
}
