package com.equinoxfitness.freezeservice.contract;

public class FreezeEmailIOutput {
	
	private boolean success;
	private String message;
	private String token;
	
	public boolean isSuccess() {
		return success;
	}
	public String getMessage() {
		return message;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	@Override
	public String toString() {
		return "FreezeEmailIOutput [success=" + success + ", message=" + message + ", token=" + token + "]";
	}
}
