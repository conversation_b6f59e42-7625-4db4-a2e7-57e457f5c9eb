/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class CheckFreezeEligibilityInputV2 extends BaseRequest{
	
	private String mosoId;
	private String freezeReason;
	private Date requestedDate;
	private Date startDate;
	private Date endDate;
	private int durationMonths;
	private int durationDays;
	private String source;
	
	
	public String getMosoId() {
		return mosoId;
	}
	public void setMosoId(String mosoId) {
		this.mosoId = mosoId;
	}
	/**
	 * @return the freezeReason
	 */
	public String getFreezeReason() {
		return freezeReason;
	}
	/**
	 * @param freezeReason the freezeReason to set
	 */
	public void setFreezeReason(String freezeReason) {
		this.freezeReason = freezeReason;
	}
	/**
	 * @return the requestedDate
	 */
	public Date getRequestedDate() {
		return requestedDate;
	}
	/**
	 * @param requestedDate the requestedDate to set
	 */
	public void setRequestedDate(Date requestedDate) {
		this.requestedDate = requestedDate;
	}
	/**
	 * @return the startDate
	 */
	public Date getStartDate() {
		return startDate;
	}
	/**
	 * @param startDate the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	/**
	 * @return the endDate
	 */
	public Date getEndDate() {
		return endDate;
	}
	/**
	 * @param endDate the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	/**
	 * @return the durationMonths
	 */
	public int getDurationMonths() {
		return durationMonths;
	}
	/**
	 * @param durationMonths the durationMonths to set
	 */
	public void setDurationMonths(int durationMonths) {
		this.durationMonths = durationMonths;
	}
	/**
	 * @return the durationDays
	 */
	public int getDurationDays() {
		return durationDays;
	}
	/**
	 * @param durationDays the durationDays to set
	 */
	public void setDurationDays(int durationDays) {
		this.durationDays = durationDays;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}	
}
