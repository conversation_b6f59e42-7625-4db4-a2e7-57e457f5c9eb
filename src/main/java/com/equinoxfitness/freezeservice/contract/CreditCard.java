/**
 * 
 */
package com.equinoxfitness.freezeservice.contract;

/**
 * <AUTHOR>
 *
 */
public class CreditCard {
	
	private boolean isDebitCard;
	private String creditCardExpiryDate;
	private String creditCardNumber;
	private int creditCardType;
	private String lastFourCreditCardDigits;
	private String nameOnCard;
	private String cvvNumber;
	/**
	 * @return the isDebitCard
	 */
	public boolean isDebitCard() {
		return isDebitCard;
	}
	/**
	 * @param isDebitCard the isDebitCard to set
	 */
	public void setDebitCard(boolean isDebitCard) {
		this.isDebitCard = isDebitCard;
	}
	/**
	 * @return the creditCardExpiryDate
	 */
	public String getCreditCardExpiryDate() {
		return creditCardExpiryDate;
	}
	/**
	 * @param creditCardExpiryDate the creditCardExpiryDate to set
	 */
	public void setCreditCardExpiryDate(String creditCardExpiryDate) {
		this.creditCardExpiryDate = creditCardExpiryDate;
	}
	/**
	 * @return the creditCardNumber
	 */
	public String getCreditCardNumber() {
		return creditCardNumber;
	}
	/**
	 * @param creditCardNumber the creditCardNumber to set
	 */
	public void setCreditCardNumber(String creditCardNumber) {
		this.creditCardNumber = creditCardNumber;
	}
	/**
	 * @return the creditCardType
	 */
	public int getCreditCardType() {
		return creditCardType;
	}
	/**
	 * @param creditCardType the creditCardType to set
	 */
	public void setCreditCardType(int creditCardType) {
		this.creditCardType = creditCardType;
	}
	/**
	 * @return the lastFourCreditCardDigits
	 */
	public String getLastFourCreditCardDigits() {
		return lastFourCreditCardDigits;
	}
	/**
	 * @param lastFourCreditCardDigits the lastFourCreditCardDigits to set
	 */
	public void setLastFourCreditCardDigits(String lastFourCreditCardDigits) {
		this.lastFourCreditCardDigits = lastFourCreditCardDigits;
	}
	/**
	 * @return the nameOnCard
	 */
	public String getNameOnCard() {
		return nameOnCard;
	}
	/**
	 * @param nameOnCard the nameOnCard to set
	 */
	public void setNameOnCard(String nameOnCard) {
		this.nameOnCard = nameOnCard;
	}
	/**
	 * @return the cvvNumber
	 */
	public String getCvvNumber() {
		return cvvNumber;
	}
	/**
	 * @param cvvNumber the cvvNumber to set
	 */
	public void setCvvNumber(String cvvNumber) {
		this.cvvNumber = cvvNumber;
	}
	

}
