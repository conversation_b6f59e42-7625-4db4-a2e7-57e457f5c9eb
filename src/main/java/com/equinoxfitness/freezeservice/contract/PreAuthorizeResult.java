package com.equinoxfitness.freezeservice.contract;

import com.equinoxfitness.commons.output.BaseResponse;

public class PreAuthorizeResult extends BaseResponse{

	private static final long serialVersionUID = 1L;
	private String referenceNumber;
	private String processorCode;
	private String responseCode;
	private String processorMessage;
	private String transactionTag;
	private String token;
	private String tokentype;
	private String transactionStatus;
	private Integer ttl;
	
	public String getReferenceNumber() {
		return referenceNumber;
	}
	public void setReferenceNumber(String referenceNumber) {
		this.referenceNumber = referenceNumber;
	}
	public String getProcessorCode() {
		return processorCode;
	}
	public void setProcessorCode(String processorCode) {
		this.processorCode = processorCode;
	}
	public String getResponseCode() {
		return responseCode;
	}
	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}
	public String getProcessorMessage() {
		return processorMessage;
	}
	public void setProcessorMessage(String processorMessage) {
		this.processorMessage = processorMessage;
	}
	public String getTransactionTag() {
		return transactionTag;
	}
	public void setTransactionTag(String transactionTag) {
		this.transactionTag = transactionTag;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getTokentype() {
		return tokentype;
	}
	public void setTokentype(String tokentype) {
		this.tokentype = tokentype;
	}
	public String getTransactionStatus() {
		return transactionStatus;
	}
	public void setTransactionStatus(String transactionStatus) {
		this.transactionStatus = transactionStatus;
	}
	public Integer getTtl() {
		return ttl;
	}
	public void setTtl(Integer ttl) {
		this.ttl = ttl;
	}
}
