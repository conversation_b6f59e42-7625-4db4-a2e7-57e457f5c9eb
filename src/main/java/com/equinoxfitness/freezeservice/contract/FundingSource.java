package com.equinoxfitness.freezeservice.contract;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Calendar;

public class FundingSource implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String memberId;
	private String cardHolderName;
	private String creditType;
	private String displayMask;
	private Calendar expiryDate;
	private Integer paySourceId;
	private String creditCardToken;
	private String accountId;
	private Boolean isForRecurringCharge;
	private Boolean isForNonRecurringCharge;
	private String billingCountry;
	private String billingAddress1;
	private String billingAddress2;
	private String billingCity;
	private String billingState;
	private String billingPostalCode;
	private BigDecimal creditLimit;
	private BigDecimal maxTransactionAmount;
	
	public String getMemberId() {
		return memberId;
	}
	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}
	public String getCardHolderName() {
		return cardHolderName;
	}
	public void setCardHolderName(String cardHolderName) {
		this.cardHolderName = cardHolderName;
	}
	public String getCreditType() {
		return creditType;
	}
	public void setCreditType(String creditType) {
		this.creditType = creditType;
	}
	public String getDisplayMask() {
		return displayMask;
	}
	public void setDisplayMask(String displayMask) {
		this.displayMask = displayMask;
	}
	public Calendar getExpiryDate() {
		return expiryDate;
	}
	public void setExpiryDate(Calendar expiryDate) {
		this.expiryDate = expiryDate;
	}
	public Integer getPaySourceId() {
		return paySourceId;
	}
	public void setPaySourceId(Integer paySourceId) {
		this.paySourceId = paySourceId;
	}
	public String getCreditCardToken() {
		return creditCardToken;
	}
	public void setCreditCardToken(String creditCardToken) {
		this.creditCardToken = creditCardToken;
	}
	public String getAccountId() {
		return accountId;
	}
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	public Boolean getIsForRecurringCharge() {
		return isForRecurringCharge;
	}
	public void setIsForRecurringCharge(Boolean isForRecurringCharge) {
		this.isForRecurringCharge = isForRecurringCharge;
	}
	public Boolean getIsForNonRecurringCharge() {
		return isForNonRecurringCharge;
	}
	public void setIsForNonRecurringCharge(Boolean isForNonRecurringCharge) {
		this.isForNonRecurringCharge = isForNonRecurringCharge;
	}
	public String getBillingCountry() {
		return billingCountry;
	}
	public void setBillingCountry(String billingCountry) {
		this.billingCountry = billingCountry;
	}
	public String getBillingAddress1() {
		return billingAddress1;
	}
	public void setBillingAddress1(String billingAddress1) {
		this.billingAddress1 = billingAddress1;
	}
	public String getBillingAddress2() {
		return billingAddress2;
	}
	public void setBillingAddress2(String billingAddress2) {
		this.billingAddress2 = billingAddress2;
	}
	public String getBillingCity() {
		return billingCity;
	}
	public void setBillingCity(String billingCity) {
		this.billingCity = billingCity;
	}
	public String getBillingState() {
		return billingState;
	}
	public void setBillingState(String billingState) {
		this.billingState = billingState;
	}
	public String getBillingPostalCode() {
		return billingPostalCode;
	}
	public void setBillingPostalCode(String billingPostalCode) {
		this.billingPostalCode = billingPostalCode;
	}
	public BigDecimal getCreditLimit() {
		return creditLimit;
	}
	public void setCreditLimit(BigDecimal creditLimit) {
		this.creditLimit = creditLimit;
	}
	public BigDecimal getMaxTransactionAmount() {
		return maxTransactionAmount;
	}
	public void setMaxTransactionAmount(BigDecimal maxTransactionAmount) {
		this.maxTransactionAmount = maxTransactionAmount;
	}	
}