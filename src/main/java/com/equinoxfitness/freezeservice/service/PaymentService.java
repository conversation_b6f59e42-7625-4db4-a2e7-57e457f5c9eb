package com.equinoxfitness.freezeservice.service;

import com.equinoxfitness.freezeservice.contract.CaptureRequest;
import com.equinoxfitness.freezeservice.contract.CaptureResult;
import com.equinoxfitness.freezeservice.contract.EncryptInput;
import com.equinoxfitness.freezeservice.contract.EncryptOutput;
import com.equinoxfitness.freezeservice.contract.PreAuthorizeRequest;
import com.equinoxfitness.freezeservice.contract.PreAuthorizeResult;
import com.equinoxfitness.freezeservice.contract.VoidRequest;
import com.equinoxfitness.freezeservice.contract.VoidResult;

public interface PaymentService {

	public PreAuthorizeResult preAuthorize(PreAuthorizeRequest preAuthorizeRequest);

	public CaptureResult capture(CaptureRequest captureRequest, String referenceNumber);

	public VoidResult voidCharge(VoidRequest voidRequest, String referenceNumber);
	
	public EncryptOutput encrypt(EncryptInput input);

}
