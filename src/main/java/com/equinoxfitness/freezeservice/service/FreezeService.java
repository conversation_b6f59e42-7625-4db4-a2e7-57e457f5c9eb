/**
 * 
 */
package com.equinoxfitness.freezeservice.service;

import java.math.BigDecimal;
import java.text.ParseException;

import com.equinoxfitness.commons.output.GetFacilityResponse;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput;
import com.equinoxfitness.freezeservice.contract.EngageFreezeEmailIOutput;
import com.equinoxfitness.freezeservice.contract.FreezeEmailIOutput;
import com.equinoxfitness.freezeservice.contract.FreezeEmailInput;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInput;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV3;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput;
import com.equinoxfitness.freezeservice.contract.FreezeMemberResponse;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV3;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV4;
import com.equinoxfitness.freezeservice.contract.MemberFreezeCase;
import com.equinoxfitness.freezeservice.contract.MemberFreezeCaseOutput;
import com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;

/**
 * <AUTHOR>
 *
 */
public interface FreezeService {

	public CheckFreezeEligiblityOutput checkFreezeEligibility(CheckFreezeEligibilityInput checkFreezeEligibilityInput);

	public RetrieveFreezeReasonOutput retrieveFreezeReason(String mosoMemberId, String freezeReason, int duration);

	public FreezeExtensionOutput freezeExtension(FreezeExtensionInput freezeExtensionInput);

	public FreezeMemberResponse freezeMembership(FreezeMembershipInput freezeMembershipInput);

	public FreezeMemberResponse freezeMembershipV2(FreezeMembershipInputV2 freezeMembershipInput);

	public FreezeExtensionOutput freezeExtensionV2(FreezeExtensionInputV2 freezeExtensionInput,MemberAgreementDetail memberAgreementDetail);
	
	public FreezeMemberResponse freezeMembershipV3(FreezeMembershipInputV3 freezeExtensionInput);
	
	public CheckFreezeEligiblityOutput checkFreezeEligibilityV2(CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput);

	public FreezeExtensionOutput freezeExtensionV4(FreezeExtensionInputV2 freezeExtensionInputV4, MemberAgreementDetail memberAgreementDetail, FreezeMembershipInputV2 freezeMembershipInput, String emailAddress);

	public FreezeMemberResponse freezeMembershipV4(FreezeMembershipInputV2 freezeMembershipInput, boolean waveFreezeFee, int countryCode, String email, BigDecimal freezeFees, MemberAgreementDetail memberAgreementDetail);
	
	public CheckFreezeEligiblityOutput checkFreezeEligibilityV4(CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput);

	public FreezeEmailIOutput sendMemberFreezeEmail(FreezeEmailInput freezeEmailInput, String correlationId);

	public MemberFreezeCaseOutput createMemberFreezeCase(MemberFreezeCase memberFreezeCase);

	public EngageFreezeEmailIOutput sendFreezeEmailLink(FreezeEmailInput freezeEmailInput, String correlationId);
	
	public CheckFreezeEligiblityOutput checkDurationInDaysForEligibility(String facilityId, CheckFreezeEligiblityOutput output);
	
	public FreezeExtensionOutput freezeExtensionV5(FreezeExtensionInputV3 freezeExtensionInput, MemberAgreementDetail memberAgreementDetail, 
			FreezeMembershipInputV4 freezeMembershipInput,String email);

	public FreezeMemberResponse freezeMembershipV5(FreezeMembershipInputV4 input, boolean waveFreezeFee,
			int countryCode, String emailAddress, BigDecimal freezeFees, MemberAgreementDetail memberAgreementDetail);

	CheckFreezeEligiblityOutput checkFreezeEligibilityV3(CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput);
}
