/**
 * 
 */
package com.equinoxfitness.freezeservice.service;

import java.sql.Date;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.common.moso.contract.MosoTokenResponse;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInput;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV3;
import com.equinoxfitness.freezeservice.contract.MemberAgreement;
import com.equinoxfitness.freezeservice.contract.moso.FreezeReasonResponse;
import com.equinoxfitness.freezeservice.contract.moso.InvoiceConfigValues;
import com.equinoxfitness.freezeservice.contract.moso.Item;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionResponse;
import com.equinoxfitness.freezeservice.contract.moso.invoice.Invoice;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewFinalizeInvoice;

/**
 * <AUTHOR>
 *
 */
public interface FreezeMosoService {

	public FreezeReasonResponse getFreezeData(String freezeReason, int duration, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity);

	public Item itemSearch(String name, GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity);
	
	public Item itemSearch(String name, GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity, String mosoId, String correlationId, String facilityId);

	public Item itemSearch(String name, MosoTokenResponse mosoTokenResponse, HttpEntity<HttpHeaders> entity, String facilityId, String mosoId, String correlationId);

	public SuspensionResponse freezeExtension(FreezeExtensionInput freezeExtensionInput, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity, String freezeId, Date freezeExtStartDate);

	public InvoiceConfigValues getTaxRate(String businessUnit, String type, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity);

	public Invoice createAndfinalizeInvoice(NewFinalizeInvoice newFinalizeInvoice, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity, String correlationId, int countryCode, String accountingCode);

	public SuspensionResponse freezeAgreement(SuspensionRequest suspensionRequest,
			HttpEntity<HttpHeaders> entity, String correlationId, String accountingCode, int countryCode);

	public Note addNote(GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity, Note note);
	
	public Note addNote(GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity, Note note, String correlationId, int countryCode, String accountingCode);

	public HttpEntity<HttpHeaders> getSession(Integer countryCode, String facilityId,String threadId);
	
	public MemberAgreement updateObligationExpirationDate(String agreementId, String obligationExpirationDate, 
			GetSessionOutput getSessionOut, String memberId, String correlationId);

	public SuspensionResponse freezeExtensionV2(FreezeExtensionInputV2 freezeExtensionInput, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity, String freezeId, Date freezeExtStartDate, int countryCode, String accountingCode);

	public SuspensionResponse freezeExtensionV4(FreezeExtensionInputV2 freezeExtensionInput, String freezeId,
			Date freezeEndTime, int countryCode, String accountingCode);

	public Item itemSearchV4(String name, MosoTokenResponse mosoTokenResponse, HttpEntity<HttpHeaders> entity,
			String mosoId, String correlationId, String accountingCode);

	public Invoice createAndfinalizeInvoiceV4(NewFinalizeInvoice newFinalizeInvoice, String correlationId,
			int countryCode, String accountingCode);

	public Note addNoteV4(Note note, String correlationId, int countryCode, String accountingCode);

	public MemberAgreement updateObligationExpirationDateV4(String memberAgreementId, String obligationExpirationDate,
			MosoTokenResponse mosoTokenResponse, String mosoId, String correlationId, int countryCode, String accountingCode);

	public SuspensionResponse freezeAgreementV4(SuspensionRequest suspensionRequest,
			MosoTokenResponse mosoTokenResponse, HttpEntity<HttpHeaders> entity, String correlationId, String accountingCode, int countryCode);
	
	public SuspensionResponse freezeExtensionV5(FreezeExtensionInputV3 freezeExtensionInput, String freezeId,
			Date freezeEndTime, int countryCode, String accountingCode);



}
