package com.equinoxfitness.freezeservice.service.impl;

import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.commons.output.BaseResponseJson;
import com.equinoxfitness.freezeservice.contract.CaptureRequest;
import com.equinoxfitness.freezeservice.contract.CaptureResult;
import com.equinoxfitness.freezeservice.contract.EncryptInput;
import com.equinoxfitness.freezeservice.contract.EncryptOutput;
import com.equinoxfitness.freezeservice.contract.PreAuthorizeRequest;
import com.equinoxfitness.freezeservice.contract.PreAuthorizeResult;
import com.equinoxfitness.freezeservice.contract.VoidRequest;
import com.equinoxfitness.freezeservice.contract.VoidResult;
import com.equinoxfitness.freezeservice.service.PaymentService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

@Service
public class PaymentServiceImpl implements PaymentService {
	
	private static final Logger logger = LoggerFactory.getLogger(PaymentServiceImpl.class);
	private static final String APPROVED = "approved";
	
	@Autowired
	@Qualifier("preAuthorizeUrl")
	private String preAuthorizeUrl;
	
	@Autowired
	@Qualifier("captureUrl")
	private String captureUrl;

	@Autowired
	@Qualifier("voidUrl")
	private String voidUrl;
	
	@Value("${encrypt.url}")
	private String encryptUrl;

	@Autowired
	RestTemplate restTemplate;
	
	@Autowired
	private HttpServletRequest httpServletRequest;
	
	@Value("#{${ccType.map}}")
	private Map<String, String> ccTypeMap; 
	
	//@LoadBalanced
	@Bean
	RestTemplate restTemplate() {
		return new RestTemplate();
	}
	
	@Override
	public PreAuthorizeResult preAuthorize(PreAuthorizeRequest preAuthorizeRequest) {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		
		//header
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Correlation-ID", httpServletRequest.getHeader("Correlation-ID"));

		HttpEntity<PreAuthorizeRequest> preAuthorizeRequestEntity = new HttpEntity<PreAuthorizeRequest>(preAuthorizeRequest, headers);
		ResponseEntity<PreAuthorizeResult> preAuthorizeRestResponse = null;
		try {
			logger.info("PreAuth Req: {}", mapper.writeValueAsString(preAuthorizeRequestEntity));
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		// Call to PreAuthorize Service		
		try {
			preAuthorizeRestResponse = restTemplate.postForEntity(preAuthorizeUrl, preAuthorizeRequestEntity, PreAuthorizeResult.class);
		} catch (HttpServerErrorException | HttpClientErrorException e) {
			logger.error(e.getResponseBodyAsString());
			PreAuthorizeResult result = new PreAuthorizeResult();
			ExceptionMessage messages = clientError(e.getResponseBodyAsString());		
			result.setMessages(new ExceptionMessage[] {messages});
			return result;
		}
		
		if(preAuthorizeRestResponse == null || preAuthorizeRestResponse.getBody() == null) {
			logger.error("preAuthorization process failed!!!");
			return null;
		}
		return preAuthorizeRestResponse.getBody();
	}

	@Override
	public CaptureResult capture(CaptureRequest captureRequest, String referenceNumber) {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);

		//header
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Correlation-ID", httpServletRequest.getHeader("Correlation-ID"));

		HttpEntity<CaptureRequest> captureRequestEntity = new HttpEntity<CaptureRequest>(captureRequest, headers);
		ResponseEntity<CaptureResult> captureResult = null;
		
		try {
			logger.info("Capture Req: {}", mapper.writeValueAsString(captureRequestEntity));
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}

		try {
			captureResult = restTemplate.postForEntity(captureUrl, captureRequestEntity, CaptureResult.class, referenceNumber);
		} catch (HttpServerErrorException e) {
			logger.error(e.getResponseBodyAsString());
			CaptureResult result = new CaptureResult();
			result.setMessage(e.getResponseBodyAsString());
			return result;
		}
		
//		try {
//			logger.info(mapper.writeValueAsString(captureResult));
//		} catch (Exception e) {
//			logger.error("Exception occurred while converting capture Rest Response", e);
//			//e.printStackTrace();
//		}
		
		if(captureResult == null || captureResult.getBody() == null) {
			logger.error("capture failed!!!");
			return null;
		}
		return captureResult.getBody();
	}

	@Override
	public VoidResult voidCharge(VoidRequest voidRequest, String referenceNumber) {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);

		//header
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Correlation-ID", httpServletRequest.getHeader("Correlation-ID"));

		HttpEntity<VoidRequest> voidRequestEntity = new HttpEntity<VoidRequest>(voidRequest, headers);

		logger.info("newVoidUrl :  {}", voidUrl);

		try {
			logger.info(mapper.writeValueAsString(voidRequest));
		} catch (Exception e) {
			e.printStackTrace();
		}
		ResponseEntity<VoidResult> voidResponse = null;
		
		try {
			voidResponse = restTemplate.postForEntity(voidUrl, voidRequestEntity, VoidResult.class, referenceNumber);
		} catch (HttpServerErrorException e) {
			logger.error(e.getResponseBodyAsString());
			VoidResult result = new VoidResult();
			result.setMessage(e.getResponseBodyAsString());
			return result;
		} catch (Exception ex) {
			logger.error(ex.getLocalizedMessage());
			VoidResult result = new VoidResult();
			result.setMessage(ex.getMessage());
			return result;
		}

//		try {
//			logger.info(mapper.writeValueAsString(voidResponse));
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
		
		return voidResponse.getBody();
	}


	@Override
	public EncryptOutput encrypt(EncryptInput input) {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		
		//header
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Correlation-ID", httpServletRequest.getHeader("Correlation-ID"));

		HttpEntity<EncryptInput> encryptRequestEntity = new HttpEntity<EncryptInput>(input, headers);
		ResponseEntity<EncryptOutput> encryptRestResponse = null;
		try {
			//Masking the CC in the logs
			EncryptInput encryptRequest = (EncryptInput)input.clone();
			String rawCreditCard = encryptRequest.getCreditCardNumber();
			String last4 = rawCreditCard.substring(rawCreditCard.length()-4);
			String maskedCreditCard = rawCreditCard.replaceAll(rawCreditCard, "XXXXXXXXXX"+last4);
			encryptRequest.setCreditCardNumber(maskedCreditCard);
			logger.info("Encrypt Req "+mapper.writeValueAsString(encryptRequest));
		} catch (JsonProcessingException | CloneNotSupportedException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		// Call to Encrypt Service		
		try {
			logger.info("Encrypt URL "+encryptUrl);
			encryptRestResponse = restTemplate.postForEntity(encryptUrl, encryptRequestEntity, EncryptOutput.class);
		} catch (HttpServerErrorException | HttpClientErrorException e) {
			logger.error(e.getResponseBodyAsString());
			EncryptOutput result = new EncryptOutput();
			ExceptionMessage messages = clientError(e.getResponseBodyAsString());
			
			result.setMessages(new ExceptionMessage[] {messages});
			return result;
		}

		
		if(encryptRestResponse == null || encryptRestResponse.getBody() == null) {
			logger.error("Encrypt process failed!!!");
			return null;
		}
		return encryptRestResponse.getBody();
	}

	private ExceptionMessage clientError(String errorBody) {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		ExceptionMessage exceptionMessage = new ExceptionMessage();
		try {
			BaseResponseJson baseResponse = mapper.readValue(errorBody, BaseResponseJson.class);
			exceptionMessage.setErrorMessage(baseResponse.getMessages()[0].getErrorMessage());
			exceptionMessage.setFriendlyMessage(baseResponse.getMessages()[0].getFriendlyMessage());
			exceptionMessage.setMessageID(baseResponse.getMessages()[0].getMessageID());
			exceptionMessage.setMessageType(baseResponse.getMessages()[0].getMessageType());
		} catch (IOException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		return exceptionMessage;
	}
}
