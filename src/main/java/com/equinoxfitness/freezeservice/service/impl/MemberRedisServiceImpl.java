/**
 * 
 */
package com.equinoxfitness.freezeservice.service.impl;

import java.util.Map;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.equinoxfitness.commons.redis.RedisKeyGenerator;
import com.equinoxfitness.commons.redis.RedisManager;
import com.equinoxfitness.commons.redis.dto.MosoMemberDataVo;
import com.equinoxfitness.commons.utils.CountryCode;
import com.equinoxfitness.freezeservice.service.MemberRedisService;

/**
 * <AUTHOR>
 *
 */
@Component
public class MemberRedisServiceImpl implements MemberRedisService {

	@Autowired
	RedisManager redisManager;

	private static final org.slf4j.Logger logger = LoggerFactory.getLogger(MemberRedisServiceImpl.class);

	@Override
	public MosoMemberDataVo getMosoMemberData(String memberId, int countryCode) {
		logger.debug("getMosoMemberData: countryCode: {}, memberId: {}", countryCode, memberId);

		MosoMemberDataVo mosoMemberData = null;

		// Get the countryCode for the countryCode
		String country = getCountry(countryCode);
		if (!country.isEmpty()) {
			// Generate the memberKey for redis
			String mosoMemberKey = RedisKeyGenerator.generateMosoMemberKey(memberId, country);
			logger.debug("Moso Member Key: {}", mosoMemberKey);

			// Get the member data from MoSo
			Map<String, String> mosoMemberMap = redisManager.getMosoMemberData(mosoMemberKey);
			if (mosoMemberMap == null || mosoMemberMap.size() == 0) {
				logger.error("Moso member not found for the memberKey: {}", mosoMemberKey);
				return null;
			}

			// Map the MoSo member data from Redis
			mosoMemberData = mapMosoMemberData(mosoMemberMap);
		}
		return mosoMemberData;
	}

	private MosoMemberDataVo mapMosoMemberData(Map<String, String> mosoMemberMap) {

		MosoMemberDataVo mosoMember = new MosoMemberDataVo();
		mosoMember.setHomeFacilityId(mosoMemberMap.get("home_club_code"));
		mosoMember.setMosoMemberId(mosoMemberMap.get("moso_id"));
		mosoMember.setCountryCode(getCountryCode(mosoMemberMap.get("source_system")));
		return mosoMember;
	}

	private String getCountryCode(String country) {
		CountryCode countryCode = CountryCode.valueOf(country);
		return String.valueOf(countryCode.intValue());
	}

	private String getCountry(int countryCode) {

		String country = "";
		if (countryCode == 1) {
			country = "us";
		} else if (countryCode == 5) {
			country = "uk";
		} else if (countryCode == 6) {
			country = "ca";
		}
		return country;
	}
}
