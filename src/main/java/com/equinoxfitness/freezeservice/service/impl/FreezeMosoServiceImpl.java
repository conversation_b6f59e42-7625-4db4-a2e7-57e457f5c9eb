/**
 * 
 */
package com.equinoxfitness.freezeservice.service.impl;

import java.sql.Date;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.common.moso.contract.MosoTokenResponse;
import com.equinoxfitness.common.moso.service.impl.MosoOptimizedTokenService;
import com.equinoxfitness.commons.service.EventsService;
import com.equinoxfitness.commons.utils.DynamoPK;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInput;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV3;
import com.equinoxfitness.freezeservice.contract.MemberAgreement;
import com.equinoxfitness.freezeservice.contract.moso.FreezeReasonResponse;
import com.equinoxfitness.freezeservice.contract.moso.InvoiceConfigValues;
import com.equinoxfitness.freezeservice.contract.moso.Item;
import com.equinoxfitness.freezeservice.contract.moso.MosoApiExceptionResponse;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionListResponse;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionResponse;
import com.equinoxfitness.freezeservice.contract.moso.UpdateSuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.invoice.Invoice;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewFinalizeInvoice;
import com.equinoxfitness.freezeservice.service.FreezeMosoService;
import com.equinoxfitness.freezeservice.utils.FreezeMembershipConstants;
import com.equinoxfitness.freezeservice.utils.MosoSessionMediatorForFreeze;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.gson.Gson;

/**
 * <AUTHOR>
 *
 */
@Service
public class FreezeMosoServiceImpl implements FreezeMosoService {

	@Value("${freeze.moso.freezedata.url}")
	String freezeDataURL;
	@Value("${freeze.moso.itemSearch.url}")
	String itemSearchURL;
	@Value("${freeze.moso.taxRate.url}")
	String taxRateURL;
	@Value("${freeze.moso.freezeExtension.url}")
	String freezeExtensionUrl;
	@Value("${freeze.moso.createAndFinalizeInvoice.url}")
	String createAndFinalizeInvoiceUrl;
	@Value("${freeze.moso.freezeAgreement.url}")
	String freezeAgreementurl;
	@Value("${freeze.moso.addNote.url}")
	String addNoteUrl;
	@Value("${freeze.moso.obligationExpirationDate.url}")
	String updateExpirationDateUrl;
	
	@Autowired
	private MosoSessionMediatorForFreeze mosoSessionMediator;
	
	@Autowired
	private EventsService eventService;
	
	@Autowired
	MosoOptimizedTokenService mosoOptimizedTokenService;
	
	@Value("${moso.api.user.name}")
	private String apiApiUserName;

	RestTemplate restTemplate = new RestTemplate();

	private final static Logger logger = LoggerFactory.getLogger(FreezeMosoServiceImpl.class);

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.equinoxfitness.freezeservice.service.FreezeMosoService#getFreezeData(java
	 * .lang.String, int, com.equinoxfitness.common.moso.contract.GetSessionOutput,
	 * org.springframework.http.HttpEntity)
	 */
	@Override
	public FreezeReasonResponse getFreezeData(String freezeReason, int duration, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity) {
		logger.debug("Moso API Service Impl");
		FreezeReasonResponse response = new FreezeReasonResponse();
		String freezeDataWithAuth = freezeDataURL + "&AUTHTOKEN={authToken}";
		String suspensionName = freezeReason + " " + duration + " Month";
		// String suspensionName = "Regular 1 Month";
		ResponseEntity<FreezeReasonResponse[]> freezeReasonResponse = null;
		logger.debug("Suspension name {}", suspensionName.trim());
		Map<String, String> params = new HashMap<>();
		params.put("suspensionName", suspensionName.trim());
		params.put("authToken", getSessionOut.getAuthTokenValue());
		logger.debug("URL {}", freezeDataURL);
		logger.debug("URl Variables {}", params.toString());
		logger.debug("Entity {}", entity.toString());
		try {
			freezeReasonResponse = restTemplate.exchange(freezeDataWithAuth, HttpMethod.GET, entity,
					FreezeReasonResponse[].class, params);
		} catch (HttpStatusCodeException e) {
			logger.error("Moso API error {}", e.getMessage());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			return response;
		}
		if (freezeReasonResponse != null && freezeReasonResponse.getBody() != null
				&& freezeReasonResponse.getBody().length > 0) {
			logger.debug("Size {}", freezeReasonResponse.getBody().length);
			ObjectMapper mapper = new ObjectMapper();
			mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
			try {
				logger.debug("Values {}", mapper.writeValueAsString(freezeReasonResponse));
			} catch (JsonProcessingException e) {
				// TODO Auto-generated catch block
				logger.error(e.getMessage());
			}
			logger.debug("Suspension reason description {}",
					freezeReasonResponse.getBody()[0].getSuspensionReasonDescription());
			return freezeReasonResponse.getBody()[0];
		}
		return response;
	}

	@Override
	public Item itemSearch(String name, GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity) {

		Item response = new Item();
		String itemSearchUrlWithAuth = itemSearchURL + "&AUTHTOKEN={authToken}";
		ResponseEntity<Item[]> itemResponse = null;
		Map<String, String> params = new HashMap<>();
		params.put("authToken", getSessionOut.getAuthTokenValue());
		params.put("name", name);
		long startTime = System.currentTimeMillis();
		try {
			itemResponse = restTemplate.exchange(itemSearchUrlWithAuth, HttpMethod.GET, entity, Item[].class, params);
		} catch (HttpStatusCodeException e) {
			logger.error("Moso API Error {}", e.getMessage());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			return response;
		}
		if (itemResponse != null && itemResponse.getBody().length > 0) {
			logger.debug("ID {}", itemResponse.getBody()[0].getId());
			return itemResponse.getBody()[0];
		} else {
			logger.error("Item API response null");
			response.setMessage("Item API response null");
			return response;
		}
	}

	@Override
	public Item itemSearch(String name, MosoTokenResponse mosoTokenResponse, HttpEntity<HttpHeaders> entity, String facilityId, String mosoId, String correlationId) {

		Item response = new Item();
		String itemSearchUrlWithAuth = itemSearchURL + "&AUTHTOKEN={authToken}";
		ResponseEntity<Item[]> itemResponse = null;
		Map<String, String> params = new HashMap<>();
		params.put("authToken", mosoTokenResponse.getMosoSessionToken());
		params.put("name", name);
		params.put("businessUnitCode", facilityId);
		long startTime = System.currentTimeMillis();
		try {
			itemResponse = restTemplate.exchange(itemSearchUrlWithAuth, HttpMethod.GET, entity, Item[].class, params);
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "moso.getItem."+mosoId, "", correlationId, "/invoices/itemsearch?name="+name+"businessUnitCode="+facilityId,
					itemResponse, itemResponse.getStatusCode(), System.currentTimeMillis()-startTime);
		} catch (HttpStatusCodeException e) {
			logger.error("Moso API Error {}", new Gson().toJson(e.getResponseBodyAsString()));
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			return response;
		}
		if (itemResponse != null && itemResponse.getBody().length > 0) {
			logger.debug("ID {}", itemResponse.getBody()[0].getId());
			return itemResponse.getBody()[0];
		} else {
			logger.error("Item API response null");
			response.setMessage("Item API response null");
			return response;
		}
	}

	@Override
	public InvoiceConfigValues getTaxRate(String businessUnit, String type, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity) {
		String taxRateURLWithAuth = taxRateURL + "&AUTHTOKEN={authToken}";
		ResponseEntity<InvoiceConfigValues> invoiceConfig = null;
		Map<String, String> params = new HashMap<>();
		InvoiceConfigValues response = new InvoiceConfigValues();

		params.put("authToken", getSessionOut.getAuthTokenValue());
		params.put("businessUnitCode", businessUnit);
		params.put("type", type);
		try {
			invoiceConfig = restTemplate.exchange(taxRateURLWithAuth, HttpMethod.GET, entity, InvoiceConfigValues.class,
					params);
		} catch (HttpStatusCodeException e) {
			logger.error("Moso API Error {} ", e.getMessage());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			return response;

		}
		if (invoiceConfig.getBody() != null && invoiceConfig.getBody().getTaxRates().length==0) {
			logger.error("Item API response null");
			response.setMessage("Item API response null");
			return response;
		} else {
			logger.debug(" Rates {}", invoiceConfig.getBody().getTaxRates()[0].getRate());
		}
		return invoiceConfig.getBody();
	}

	@Override
	public SuspensionResponse freezeExtension(FreezeExtensionInput freezeExtensionInput, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity, String freezeId, Date freezeExtStartDate) {

		String freezeExtensionUrlWithAuth = freezeExtensionUrl + "?AUTHTOKEN={authToken}";
		DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");

		ResponseEntity<SuspensionResponse> freezeExtensionResponse = null;
		Map<String, String> params = new HashMap<>();
		params.put("authToken", getSessionOut.getAuthTokenValue());
		HttpHeaders headers = new HttpHeaders();
		headers = entity.getHeaders();
		UpdateSuspensionRequest freezeExtensionRequest = new UpdateSuspensionRequest();
		freezeExtensionRequest.setComments(freezeExtensionInput.getNotes());
		freezeExtensionRequest.setFreezeId(freezeId);
		freezeExtensionRequest.setFreezeEndDate(
				dateFormat.format(freezeExtensionInput.getFreezeExtensionEndDate()).replaceAll("-", ""));
		// freezeExtensionRequest.setFreezeStartDate(dateFormat.format(freezeExtStartDate).replaceAll("-",
		// ""));
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request {}", mapper.writeValueAsString(freezeExtensionRequest));
		} catch (JsonProcessingException e1) {
			// TODO Auto-generated catch block
			logger.error(e1.getMessage());
		}
		HttpEntity<UpdateSuspensionRequest> freezeExtensionrequestEntity = new HttpEntity<UpdateSuspensionRequest>(
				freezeExtensionRequest, headers);
		try {
			freezeExtensionResponse = restTemplate.exchange(freezeExtensionUrlWithAuth, HttpMethod.PUT,
					freezeExtensionrequestEntity, SuspensionResponse.class, params);
		} catch (HttpStatusCodeException e) {
			logger.error("Error {}", e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			SuspensionResponse response = new SuspensionResponse();
			response.setMessage(mosoError.getErrorMessage());
			return response;
		}
		return freezeExtensionResponse.getBody();
	}
	
	@Override
	public SuspensionResponse freezeExtensionV2(FreezeExtensionInputV2 freezeExtensionInput, GetSessionOutput getSessionOut,
			HttpEntity<HttpHeaders> entity, String freezeId, Date freezeExtStartDate, int countryCode, String accountingCode) {

		//String freezeExtensionUrlWithAuth = freezeExtensionUrl + "?AUTHTOKEN={authToken}";
		DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");

		SuspensionResponse freezeExtensionResponse = null;
		
		UpdateSuspensionRequest freezeExtensionRequest = new UpdateSuspensionRequest();
		freezeExtensionRequest.setComments(freezeExtensionInput.getNotes());
		freezeExtensionRequest.setFreezeId(freezeId);
		freezeExtensionRequest.setFreezeEndDate(
				dateFormat.format(freezeExtensionInput.getFreezeExtensionEndDate()).replaceAll("-", ""));
		
		long startTime = System.currentTimeMillis();
		try {			
			freezeExtensionResponse = mosoOptimizedTokenService.putForEntityWithMosoAuth(freezeExtensionUrl, (Object)freezeExtensionRequest, SuspensionResponse.class, countryCode, accountingCode, apiApiUserName);
		} catch (HttpStatusCodeException e) {
			logger.error("Error {}", e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			SuspensionResponse response = new SuspensionResponse();
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInput.getMosoId(), "moso.freeze.extension."+freezeExtensionInput.getMosoId(), "",
					freezeExtensionInput.getCorrelationId(), freezeExtensionRequest, mosoError, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		return freezeExtensionResponse;
	}

	@Override
	public Invoice createAndfinalizeInvoice(NewFinalizeInvoice newFinalizeInvoiceRequest,
			GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity, String correlationId, int countryCode, String accountingCode) {

		Invoice response = new Invoice();		

		logger.info("CreateAndFinalizeInvoice Moso request for member: {}, {}", newFinalizeInvoiceRequest.getMemberId(), new Gson().toJson(newFinalizeInvoiceRequest));
		long startTime = System.currentTimeMillis();
		try {		

			response = mosoOptimizedTokenService.postForEntityWithMosoAuth(createAndFinalizeInvoiceUrl, (Object)newFinalizeInvoiceRequest, Invoice.class, countryCode, accountingCode, apiApiUserName);

		} catch (HttpStatusCodeException e) {
			logger.error("Error {}", e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+newFinalizeInvoiceRequest.getMemberId(), "moso.createandfinalizeinvoice."+newFinalizeInvoiceRequest.getMemberId(), "", 
					correlationId, newFinalizeInvoiceRequest, response, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		return response;
	}

	@Override
	public SuspensionResponse freezeAgreement(SuspensionRequest suspensionRequest, HttpEntity<HttpHeaders> entity, String correlationId, String accountingCode, int countryCode) {
		SuspensionResponse freezeAgreementResponse = null;
		SuspensionResponse response = new SuspensionResponse();
		HttpHeaders headers = new HttpHeaders();
		headers = entity.getHeaders();
		HttpEntity<SuspensionRequest> freezeAgreementEnity = new HttpEntity<SuspensionRequest>(suspensionRequest, headers);
		long startTime = System.currentTimeMillis();
		logger.info("Freezing agreement in Moso for member: {}, Moso Freeze Request: {}, {}", suspensionRequest.getRoleId(), freezeAgreementurl, new Gson().toJson(freezeAgreementEnity));
		try {

			freezeAgreementResponse = mosoOptimizedTokenService.postForEntityWithMosoAuth(freezeAgreementurl, (Object)freezeAgreementEnity, SuspensionResponse.class, countryCode, accountingCode, apiApiUserName);
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+suspensionRequest.getRoleId(), "moso.freeze."+suspensionRequest.getRoleId(), "", correlationId, 
					freezeAgreementEnity, freezeAgreementResponse, HttpStatus.OK, System.currentTimeMillis()-startTime);
		} catch (HttpStatusCodeException e) {
			logger.error("Error received when freezing the agreement for member: {}, error: {}", suspensionRequest.getRoleId(), e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+suspensionRequest.getRoleId(), "moso.freeze."+suspensionRequest.getRoleId(), "", correlationId, 
					freezeAgreementEnity, mosoError, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		logger.info("Successfully placed the member Freeze in Moso for: {}", suspensionRequest.getRoleId());
		return freezeAgreementResponse;
	}

	@Override
	public Note addNote(GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity, Note note) {

		logger.debug("Inside Add Note ");
		String addNoteUrlWithAuth = addNoteUrl + "?AUTHTOKEN={authToken}";
		ResponseEntity<Note> addNoteResponse = null;
		Note response = new Note();
		Map<String, String> params = new HashMap<>();
		params.put("authToken", getSessionOut.getAuthTokenValue());
		HttpHeaders headers = new HttpHeaders();
		headers = entity.getHeaders();
		HttpEntity<Note> addNoteEnity = new HttpEntity<Note>(note, headers);
		try {
			addNoteResponse = restTemplate.exchange(addNoteUrlWithAuth, HttpMethod.POST, addNoteEnity, Note.class,
					params);
		} catch (HttpStatusCodeException e) {
			logger.error("Error {}", e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			return response;
		}
		return addNoteResponse.getBody();
	}

	@Override
	public HttpEntity<HttpHeaders> getSession(Integer countryCode, String facilityId, String threadId) {

		GetSessionOutput sessionOutput = (GetSessionOutput) mosoSessionMediator.getSession(countryCode, facilityId,
				threadId);
		if (sessionOutput == null) {
			return null;
		} else {
			logger.debug("AuthToken :{} ", sessionOutput.getAuthTokenValue());
			logger.debug("Cookie : {} ", sessionOutput.getCookieValue());

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.set("Cookie", sessionOutput.getCookieValue());
			HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);
			return entity;
		}

	}

	@Override
	public MemberAgreement updateObligationExpirationDate(String agreementId, String obligationExpirationDate, 
			GetSessionOutput getSessionOut, String mosoId, String correlationId) {
		logger.info("Inside updateObligationExpirationDate for member: {}", mosoId);
		
		String updateExpirationDateUrlWithAuth = updateExpirationDateUrl + "&AUTHTOKEN={authToken}";
		ResponseEntity<MemberAgreement> memberAgreement = null;
		
		Map<String, String> params = new HashMap<>();
		params.put("authToken", getSessionOut.getAuthTokenValue());
		params.put("id", agreementId);
		params.put("obligationExpirationDate", obligationExpirationDate);
		
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", getSessionOut.getCookieValue());
		HttpEntity<String> entity = new HttpEntity<>(headers);
		long startTime = System.currentTimeMillis();
		try {
			memberAgreement=restTemplate.exchange(updateExpirationDateUrlWithAuth, HttpMethod.PUT, entity, MemberAgreement.class, params);
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "moso.updateObligationExpirationDate", "", correlationId, 
					params, memberAgreement.getBody(), memberAgreement.getStatusCode(), System.currentTimeMillis()-startTime);
			
		} catch (HttpStatusCodeException e) {
			logger.error("Error received when trying to updateObligationExpirationDate for member: {}, error: {}", mosoId, e.getResponseBodyAsString());
			MemberAgreement response= new MemberAgreement();
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "moso.updateObligationExpirationDate", "", correlationId, 
					params, mosoError.getErrorMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		logger.info("Obligation expiration date is updated for paid in full member: {}", mosoId);
		return memberAgreement.getBody();
	}
	
	@Override
	public Note addNote(GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity, Note note, String correlationId, int countryCode, String accountingCode) {

		logger.debug("Inside Add Note ");
		Note response = new Note();
		long startTime = System.currentTimeMillis();
		try {
			response = mosoOptimizedTokenService.postForEntityWithMosoAuth(addNoteUrl, (Object)note, Note.class, countryCode, accountingCode, apiApiUserName);
		} catch (HttpStatusCodeException e) {
			logger.error("Error {}", e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+note.getId(), "moso.addnote."+note.getId(), "", correlationId, 
					note, mosoError, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		return response;
	}
	
	@Override
	public Item itemSearch(String name, GetSessionOutput getSessionOut, HttpEntity<HttpHeaders> entity, String mosoId, String correlationId, String facilityId) {

		Item response = new Item();
		String itemSearchUrlWithAuth = itemSearchURL + "&AUTHTOKEN={authToken}";
		ResponseEntity<Item[]> itemResponse = null;
		Map<String, String> params = new HashMap<>();
		params.put("authToken", getSessionOut.getAuthTokenValue());
		params.put("name", name);
		params.put("businessUnitCode", facilityId);
		long startTime = System.currentTimeMillis();
		logger.info("GET Price from Moso Itemsearch API: {}, params: {}", itemSearchUrlWithAuth, params);
		try {
			itemResponse = restTemplate.exchange(itemSearchUrlWithAuth, HttpMethod.GET, entity, Item[].class, params);
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "moso.getItem."+mosoId, "", correlationId, "/invoices/itemsearch?name="+name+"businessUnitCode="+facilityId, 
					itemResponse, itemResponse.getStatusCode(), System.currentTimeMillis()-startTime);
		} catch (HttpStatusCodeException e) {
			logger.error("Moso API Error {}", e.getMessage());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "moso.getItem."+mosoId, "", correlationId, "/invoices/itemsearch?name="+name+"businessUnitCode="+facilityId, 
					itemResponse, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		if (itemResponse != null && itemResponse.getBody().length > 0) {
			logger.debug("ID {}", itemResponse.getBody()[0].getId());
			return itemResponse.getBody()[0];
		} else {
			logger.error("Item API response null");
			response.setMessage("Item API response null");
			return response;
		}
	}

	@Override
	public SuspensionResponse freezeExtensionV4(FreezeExtensionInputV2 freezeExtensionInput, String freezeId,
			Date freezeEndTime, int countryCode, String accountingCode) {
		DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");

		SuspensionResponse freezeExtensionResponse = null;

		UpdateSuspensionRequest freezeExtensionRequest = new UpdateSuspensionRequest();
		freezeExtensionRequest.setComments(freezeExtensionInput.getNotes());
		freezeExtensionRequest.setFreezeId(freezeId);
		freezeExtensionRequest.setFreezeEndDate(
				dateFormat.format(freezeExtensionInput.getFreezeExtensionEndDate()).replaceAll("-", ""));

		long startTime = System.currentTimeMillis();
		try {			
			freezeExtensionResponse = mosoOptimizedTokenService.putForEntityWithMosoAuth(freezeExtensionUrl, (Object)freezeExtensionRequest, SuspensionResponse.class, countryCode, accountingCode, apiApiUserName);
		} catch (HttpStatusCodeException e) {
			logger.error("Error {}", e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			SuspensionResponse response = new SuspensionResponse();
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInput.getMosoId(), "moso.freeze.extension."+freezeExtensionInput.getMosoId(), "",
					freezeExtensionInput.getCorrelationId(), freezeExtensionRequest, mosoError, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		return freezeExtensionResponse;
	}

	@Override
	public Item itemSearchV4(String name, MosoTokenResponse mosoTokenResponse, HttpEntity<HttpHeaders> entity,
			String mosoId, String correlationId, String facilityId) {
		Item response = new Item();
		String itemSearchUrlWithAuth = itemSearchURL + "&AUTHTOKEN={authToken}";
		ResponseEntity<Item[]> itemResponse = null;
		Map<String, String> params = new HashMap<>();
		params.put("authToken", mosoTokenResponse.getMosoSessionToken());
		params.put("name", name);
		params.put("businessUnitCode", facilityId);
		long startTime = System.currentTimeMillis();
		logger.info("GET Price from Moso Itemsearch API: {}, params: {}", itemSearchUrlWithAuth, params);
		try {
			itemResponse = restTemplate.exchange(itemSearchUrlWithAuth, HttpMethod.GET, entity, Item[].class, params);
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "moso.getItem."+mosoId, "", correlationId, "/invoices/itemsearch?name="+name+"businessUnitCode="+facilityId, 
					itemResponse, itemResponse.getStatusCode(), System.currentTimeMillis()-startTime);
		} catch (HttpStatusCodeException e) {
			logger.error("Moso API Error {}", e.getMessage());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "moso.getItem."+mosoId, "", correlationId, "/invoices/itemsearch?name="+name+"businessUnitCode="+facilityId, 
					itemResponse, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		if (itemResponse != null && itemResponse.getBody().length > 0) {
			logger.debug("ID {}", itemResponse.getBody()[0].getId());
			return itemResponse.getBody()[0];
		} else {
			logger.error("Item API response null");
			response.setMessage("Item API response null");
			return response;
		}
	}

	@Override
	public Invoice createAndfinalizeInvoiceV4(NewFinalizeInvoice newFinalizeInvoiceRequest, String correlationId,
			int countryCode, String accountingCode) {
		Invoice response = new Invoice();		

		logger.info("CreateAndFinalizeInvoice Moso request for member: {}, {}", newFinalizeInvoiceRequest.getMemberId(), new Gson().toJson(newFinalizeInvoiceRequest));
		long startTime = System.currentTimeMillis();
		try {		

			response = mosoOptimizedTokenService.postForEntityWithMosoAuth(createAndFinalizeInvoiceUrl, (Object)newFinalizeInvoiceRequest, Invoice.class, countryCode, accountingCode, apiApiUserName);

		} catch (HttpStatusCodeException e) {
			logger.error("Error {}", e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+newFinalizeInvoiceRequest.getMemberId(), "moso.createandfinalizeinvoice."+newFinalizeInvoiceRequest.getMemberId(), "", 
					correlationId, newFinalizeInvoiceRequest, response, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		return response;
	}

	@Override
	public Note addNoteV4(Note note, String correlationId, int countryCode, String accountingCode) {
		logger.debug("Inside addNoteV4");
		Note response = new Note();
		long startTime = System.currentTimeMillis();
		try {
			response = mosoOptimizedTokenService.postForEntityWithMosoAuth(addNoteUrl, (Object)note, Note.class, countryCode, accountingCode, apiApiUserName);
		} catch (HttpStatusCodeException e) {
			logger.error("Error {}", e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+note.getId(), "moso.addnote."+note.getId(), "", correlationId, 
					note, mosoError, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		return response;
	}

	@Override
	public MemberAgreement updateObligationExpirationDateV4(String memberAgreementId, String obligationExpirationDate,
			MosoTokenResponse mosoTokenResponse, String mosoId, String correlationId, int countryCode, String accountingCode) {
		logger.info("Inside updateObligationExpirationDateV4 for member: {}", mosoId);		
		MemberAgreement response = null;		
		Map<String, String> params = new HashMap<>();
		params.put("id", memberAgreementId);
		params.put("obligationExpirationDate", obligationExpirationDate);		

		long startTime = System.currentTimeMillis();
		try {			
			response = mosoOptimizedTokenService.putWithMosoAuth(updateExpirationDateUrl, null, MemberAgreement.class, params, countryCode, accountingCode, apiApiUserName);
		} catch (HttpStatusCodeException e) {
			logger.error("Error received when trying to updateObligationExpirationDate for member: {}, error: {}", mosoId, e.getResponseBodyAsString());
			MemberAgreement memberAgreementResponse= new MemberAgreement();
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			memberAgreementResponse.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "moso.updateObligationExpirationDate", "", correlationId, 
					params, mosoError.getErrorMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		logger.info("Obligation expiration date is updated for paid in full member: {}", mosoId);
		return response;
	}

	@Override
	public SuspensionResponse freezeAgreementV4(SuspensionRequest suspensionRequest,
			MosoTokenResponse mosoTokenResponse, HttpEntity<HttpHeaders> entity, String correlationId, String accountingCode, int countryCode) {
		String freezeAgreementUrlWithAuth = freezeAgreementurl + "?AUTHTOKEN={authToken}";
		SuspensionResponse response = new SuspensionResponse();
		Map<String, String> params = new HashMap<>();
		params.put("authToken", mosoTokenResponse.getMosoSessionToken());
		HttpHeaders headers = new HttpHeaders();
		headers = entity.getHeaders();
		HttpEntity<SuspensionRequest> freezeAgreementEnity = new HttpEntity<SuspensionRequest>(suspensionRequest, headers);
		long startTime = System.currentTimeMillis();
		logger.info("Freezing agreement in Moso for member: {}, Moso Freeze Request: {}, {}", suspensionRequest.getRoleId(), freezeAgreementUrlWithAuth, new Gson().toJson(freezeAgreementEnity));
		try {
			SuspensionListResponse suspensionResponseWrapper = mosoOptimizedTokenService.postForEntityWithMosoAuth(
                freezeAgreementurl, suspensionRequest, SuspensionListResponse.class, countryCode, accountingCode, apiApiUserName);

			// Extract the array from the wrapper
			if (suspensionResponseWrapper!= null) {
				if (suspensionResponseWrapper.getMessage() != null) {
					logger.error("Error received when freezing the agreement for member: {}, error: {}", suspensionRequest.getRoleId(), suspensionResponseWrapper.getMessage());
					response.setMessage(suspensionResponseWrapper.getMessage());
				} else if (suspensionResponseWrapper.getSuspensionResponses() != null
						&& suspensionResponseWrapper.getSuspensionResponses().length > 0) {
					response = suspensionResponseWrapper.getSuspensionResponses()[0];
				}
			}

			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID + suspensionRequest.getRoleId(),
					"moso.freeze." + suspensionRequest.getRoleId(), "", correlationId,
					freezeAgreementEnity, suspensionResponseWrapper, HttpStatus.OK, System.currentTimeMillis() - startTime);			
		} catch (HttpStatusCodeException e) {
			logger.error("Error received when freezing the agreement for member: {}, error: {}", suspensionRequest.getRoleId(), e.getResponseBodyAsString());
			MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
					MosoApiExceptionResponse.class);
			response.setMessage(mosoError.getErrorMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+suspensionRequest.getRoleId(), "moso.freeze."+suspensionRequest.getRoleId(), "", correlationId, 
					freezeAgreementEnity, mosoError, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			return response;
		}
		logger.info("Successfully placed the member Freeze in Moso for: {}", suspensionRequest.getRoleId());
		return response;
	}

	@Override
	public SuspensionResponse freezeExtensionV5(FreezeExtensionInputV3 freezeExtensionInput, String freezeId,
			Date freezeEndTime, int countryCode, String accountingCode) {
			DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");

			SuspensionResponse freezeExtensionResponse = null;

			UpdateSuspensionRequest freezeExtensionRequest = new UpdateSuspensionRequest();
			freezeExtensionRequest.setComments(freezeExtensionInput.getNotes());
			freezeExtensionRequest.setFreezeId(freezeId);
			freezeExtensionRequest.setFreezeEndDate(
					dateFormat.format(freezeExtensionInput.getFreezeExtensionEndDate()).replaceAll("-", ""));

			long startTime = System.currentTimeMillis();
			try {			
				freezeExtensionResponse = mosoOptimizedTokenService.putForEntityWithMosoAuth(freezeExtensionUrl, (Object)freezeExtensionRequest, SuspensionResponse.class, countryCode, accountingCode, apiApiUserName);
			} catch (HttpStatusCodeException e) {
				logger.error("Error {}", e.getResponseBodyAsString());
				MosoApiExceptionResponse mosoError = (new Gson()).fromJson(e.getResponseBodyAsString(),
						MosoApiExceptionResponse.class);
				SuspensionResponse response = new SuspensionResponse();
				response.setMessage(mosoError.getErrorMessage());
				eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+freezeExtensionInput.getMosoId(), "moso.freeze.extension."+freezeExtensionInput.getMosoId(), "",
						freezeExtensionInput.getCorrelationId(), freezeExtensionRequest, mosoError, HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
				return response;
			}
			return freezeExtensionResponse;
	}
}

