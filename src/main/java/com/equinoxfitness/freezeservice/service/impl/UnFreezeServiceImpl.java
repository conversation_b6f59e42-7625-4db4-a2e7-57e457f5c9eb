package com.equinoxfitness.freezeservice.service.impl;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.commons.output.Facility;
import com.equinoxfitness.commons.output.GetFacilityResponse;
import com.equinoxfitness.commons.service.FacilityService;
import com.equinoxfitness.commons.utils.DateUtils;
import com.equinoxfitness.commons.utils.DynamoPK;
import com.equinoxfitness.commons.utils.ErrorMessageHandler;
import com.equinoxfitness.commons.utils.ExceptionMessageEnum;
import com.equinoxfitness.freezeservice.adapter.UnfreezeAdapter;
import com.equinoxfitness.freezeservice.contract.ApiResponse;
import com.equinoxfitness.freezeservice.contract.UnFreezeCaseResponse;
import com.equinoxfitness.freezeservice.contract.UnfreezeMembershipResponse;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.UnFreezeRequest;
import com.equinoxfitness.freezeservice.contract.sfdc.Case;
import com.equinoxfitness.freezeservice.contract.sfdc.Contact;
import com.equinoxfitness.freezeservice.contract.sfdc.CreateCaseResponse;
import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.service.FreezeMosoService;
import com.equinoxfitness.freezeservice.service.SFDCService;
import com.equinoxfitness.freezeservice.service.UnFreezeService;
import com.equinoxfitness.freezeservice.utils.FreezeMembershipConstants;
import com.equinoxfitness.freezeservice.utils.FreezeServiceExceptionEnum;
import com.equinoxfitness.freezeservice.utils.FreezeServiceHelper;
import com.equinoxfitness.freezeservice.utils.MosoSessionMediatorForFreeze;
import com.equinoxfitness.redis.contract.member.Member;
import com.equinoxfitness.redis.contract.member.Membership;
import com.equinoxfitness.redis.contract.member.Profile;
import com.equinoxfitness.redis.service.RedisApiService;
import com.equinoxfitness.salesforce.contract.Wrapper;
import com.equinoxfitness.salesforce.service.SalesforceApiService;

@Service
public class UnFreezeServiceImpl implements UnFreezeService {

	@Autowired
	private RedisApiService redisApiService;

	@Autowired
	private FacilityService facilityService;

	@Autowired
	private ErrorMessageHandler errorMessageHandler;

	@Autowired
	private FreezeMosoService mosoService;

	@Autowired
	private UnfreezeAdapter unfreezeAdapter;

	@Autowired
	private FreezeServiceDAO freezeDAO;

	@Autowired
	private MosoSessionMediatorForFreeze mosoMediator;

	@Autowired
	private FreezeServiceHelper freezeHelper;

	@Autowired
	private SFDCService sdfcService;
	
	@Autowired
	private SalesforceApiService salesforceApiService;

	private static final Logger logger = LoggerFactory.getLogger(UnFreezeServiceImpl.class);

	@Override
	public UnfreezeMembershipResponse unFreezeMember(String memberId, String ipAddress) {
		logger.info("getting member from cache");
		UnfreezeMembershipResponse response = new UnfreezeMembershipResponse();

		Member member = redisApiService.getMemberV2(memberId);

		if (Objects.isNull(member)) {
			logger.info("Falied to get member from Globey {}", memberId);
			response.setMessages(
					errorMessageHandler.createExceptionMessageArray(FreezeServiceExceptionEnum.MEMBER_NOT_FOUND.name(),
							FreezeServiceExceptionEnum.MEMBER_NOT_FOUND.getCode(), ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		Optional<String> emailId = Optional.ofNullable(member.getProfile()).map(Profile::getEmail);
		Optional<String> contactId = Optional.ofNullable(member.getMembership()).map(Membership::getSalesforceId);
		String email = emailId.isPresent() ? emailId.get() : "";

		logger.info("Email id for member {} is {}", memberId, email);

		Optional<String> homeFacilityId = Optional.ofNullable(member.getMembership())
				.map(Membership::getHomeFacilityId);

		if (!homeFacilityId.isPresent()) {
			logger.info("Failed to get member homefacilityId from Globey {}", memberId);
			response.setMessages(errorMessageHandler.createExceptionMessageArray(
					FreezeServiceExceptionEnum.FACILITY_ID_NOT_FOUND.name(),
					FreezeServiceExceptionEnum.FACILITY_ID_NOT_FOUND.getCode(), ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		Optional<Facility> facilityResponse = Optional
				.ofNullable(facilityService.getFacilityByIdV2(homeFacilityId.get()))
				.map(GetFacilityResponse::getResult);

		if (!facilityResponse.isPresent()) {
			logger.info("Falied to facilty details of {} for member {}", homeFacilityId.get(), memberId);
			response.setMessages(errorMessageHandler.createExceptionMessageArray(
					FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name(),
					FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.getCode(), ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		if ((homeFacilityId.get().equals("998") || homeFacilityId.get().equals("994"))
				&& StringUtils.isBlank(facilityResponse.get().getTimeZone())) { // Equinox online
			facilityResponse.get().setTimeZone("Eastern Standard Time");
		} else if (homeFacilityId.get().equals("995") && StringUtils.isBlank(facilityResponse.get().getTimeZone())) {
			facilityResponse.get().setTimeZone("GMT Standard Time");
		}

		logger.info("Retrieving member agreement details from DB for member {}", memberId);

		MemberAgreementDetail memberAgreementDetails = freezeDAO.getMemberAgreementDetail(memberId);

		if (Objects.isNull(memberAgreementDetails)) {
			logger.info("Member {} has no agreements", memberId);
			response.setMessages(errorMessageHandler.createExceptionMessageArray(
					FreezeServiceExceptionEnum.NO_AGREEMENTS_FOUND.name(),
					FreezeServiceExceptionEnum.NO_AGREEMENTS_FOUND.getCode(), ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		String freezeStatus = memberAgreementDetails.getFreezeStatus();
		String freezeReasonName = memberAgreementDetails.getFreezeReason();
		String freezeId = memberAgreementDetails.getFreezeId();

		logger.info("MemberId --> {} , freezeStatus --> {} , freezeReasonName --> {} , freezeId --> {}", memberId,
				freezeStatus, freezeReasonName, freezeId);
		if(StringUtils.isEmpty(freezeStatus) || !freezeStatus.equalsIgnoreCase("Active")) {
			response.setMessages(errorMessageHandler.createExceptionMessageArray(
					FreezeServiceExceptionEnum.NOT_ON_FREEZE.name(),
					FreezeServiceExceptionEnum.NOT_ON_FREEZE.getCode(), ExceptionMessageEnum.ERROR.name()));
			return response;			
		}

		if (StringUtils.isEmpty(freezeStatus) || StringUtils.isEmpty(freezeId)) {
			logger.info("Member {} do not have any agreements on freeze", memberId);
			response.setMessages(errorMessageHandler.createExceptionMessageArray(
					FreezeServiceExceptionEnum.NO_ACTIVE_FREEZE_FOUND.name(),
					FreezeServiceExceptionEnum.NO_ACTIVE_FREEZE_FOUND.getCode(), ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		if (!FreezeMembershipConstants.FREEZE_ACTIVE_STATUS.equalsIgnoreCase(freezeStatus)
				&& !FreezeMembershipConstants.FREEZE_PENDING_START.equalsIgnoreCase(freezeStatus)) {
			logger.info("Member {} freeze status is {}", memberId, freezeStatus);
			response.setMessages(
					errorMessageHandler.createExceptionMessageArray(FreezeServiceExceptionEnum.CONTACT_CONCIERGE.name(),
							FreezeServiceExceptionEnum.CONTACT_CONCIERGE.getCode(), ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		if (FreezeMembershipConstants.FREEZE_REASON_FINANCIAL_FREEZE.equalsIgnoreCase(freezeReasonName)
				|| FreezeMembershipConstants.FREEZE_REASON_CORP_SPONSOR_FREEZE.equalsIgnoreCase(freezeReasonName)) {
			logger.info("Not going to unfreeze membership bcz freeze reason is {} for member {}", freezeReasonName,
					memberId);
			response.setMessages(
					errorMessageHandler.createExceptionMessageArray(FreezeServiceExceptionEnum.CONTACT_CONCIERGE.name(),
							FreezeServiceExceptionEnum.CONTACT_CONCIERGE.getCode(), ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		logger.info("Getting session from moso for member {} and businessAccountingCode {}", memberId,
				facilityResponse.get().geteClubFacilityId());

		GetSessionOutput sessionOutput = mosoMediator.getSession(facilityResponse.get().getSourceSystem(),
				facilityResponse.get().geteClubFacilityId(), null);

		if (Objects.isNull(sessionOutput)) {
			logger.info("Getting session failed from moso for member {} and businessAccountingCode {}", memberId,
					facilityResponse.get().geteClubFacilityId());
			response.setMessages(errorMessageHandler.createExceptionMessageArray(
					FreezeServiceExceptionEnum.FAILED_TO_GET_MOSO_SESSION.name(),
					FreezeServiceExceptionEnum.FAILED_TO_GET_MOSO_SESSION.getCode(),
					ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		logger.info("Calling adapter to unfreeze member {}", memberId);
		boolean isUnfreezed = unfreezeAdapter.unFreezeMembership(
				getUnfreezeRequest(facilityResponse.get().getTimeZone(), freezeId), sessionOutput.getAuthTokenValue(),
				sessionOutput.getCookieValue());

		if (!isUnfreezed) {
			response.setMessages(errorMessageHandler.createExceptionMessageArray(
					FreezeServiceExceptionEnum.FAILED_TO_UNFREEZE.name(),
					FreezeServiceExceptionEnum.FAILED_TO_UNFREEZE.getCode(), ExceptionMessageEnum.ERROR.name()));

			return response;
		}

		logger.info("Preparing note content to create note in MoSo for unfreeze member {}", memberId);
		List<String> agreementIdsList = new ArrayList<>();
		agreementIdsList.add(memberAgreementDetails.getMemberAgreementId());

		String contentForNote = "Member Unfroze the Agreement Digitally\n" + "AgreementID(s) : "
				+ agreementIdsList.toString() + "\n" + "EmailAddress: " + email + "\n" + "IPAddress: " + ipAddress;

		logger.info("Adding note in MoSo for unfreeze member {}", memberId);
		Note note = freezeHelper.setnoteRequest(memberId, contentForNote,
				FreezeMembershipConstants.UNFREEZE_NOTE_TYPE_ID);

		mosoService.addNote(sessionOutput, freezeHelper.setEntity(sessionOutput), note);

		logger.info("Creating case in sales force for unfreeze agreement for member {}", memberId);
		CreateCaseResponse caseResponse = sdfcService.createCaseForUnfreeze(homeFacilityId.get(), contactId.isPresent() ? contactId.get() : "", memberId);
		if(Objects.nonNull(caseResponse) && caseResponse.getId() != null) {
			response.setCase_id(caseResponse.getId());
		}
		logger.info("Calling Globey to refresh member status {}", memberId);
		redisApiService.getMemberV2ForceRefresh(memberId);

		response.setSuccess(Boolean.TRUE);
		response.setMemberAgreementId(memberAgreementDetails.getMemberAgreementId());
		return response;
	}
	
	@Override
	public ApiResponse<Object> unFreezeCaseInSalesforce(String mosoId) {
		Case unfreezeCase = createUnFreezeCase(mosoId);
		UnFreezeCaseResponse unFreezeCaseResponse = new UnFreezeCaseResponse();
		ApiResponse<Object> response = new ApiResponse<>();
		
		Wrapper<CreateCaseResponse> caseResponse = salesforceApiService.post(DynamoPK.MEMBER_ID+mosoId, "case", unfreezeCase, CreateCaseResponse.class);
		
		if(Objects.nonNull(caseResponse)) {
			if(Objects.nonNull(caseResponse.getData()))
				unFreezeCaseResponse.setCaseId(caseResponse.getData().getId());
			if(Objects.nonNull(caseResponse.getMessage())) {
				ExceptionMessage exceptionMessage= errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.FAILED_TO_CREATE_UNFREEZE_CASE_IN_SALESFORCE.name(), 
						FreezeServiceExceptionEnum.FAILED_TO_CREATE_UNFREEZE_CASE_IN_SALESFORCE.getCode(), ExceptionMessageEnum.ERROR.toString());
				response.setMessages(new ExceptionMessage[] {exceptionMessage});
			}
		}
		response.setResult(unFreezeCaseResponse);
		return response;
	}

	private UnFreezeRequest getUnfreezeRequest(String timeZone, String freezeId) {
		UnFreezeRequest request = new UnFreezeRequest();

		request.setFreezeId(freezeId);
		request.setFreezeEndDateSet(Boolean.TRUE);
		request.setFreezeEndDate(getCurrentFacilityDate(timeZone));

		return request;
	}

	private String getCurrentFacilityDate(String timeZone) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		String timeZoneCurrentDate = DateUtils.TIMEZONE_MAPPINGS.get(timeZone);
		return formatter.format(ZonedDateTime.now(ZoneId.of(timeZoneCurrentDate)));
	}
	
	private Case createUnFreezeCase(String mosoId) {
		Case caseObject = new Case();
        Contact contact = new Contact();
        
		caseObject.setRecordTypeId("012120000019jsWAAQ");
		caseObject.setCancellationType("Removing someone from Freeze early or cancel a pending request.");
		caseObject.setOrigin("Member Admin");
		caseObject.setSubject("Unfreeze from Club App");
		caseObject.setCategories__c("Membership");
		caseObject.setDescription("Unfreeze member "+mosoId);
		caseObject.setSubCategories("Freeze");
		caseObject.setDateOfIncident(Calendar.getInstance().getTime());
		caseObject.setMemberId(mosoId);
		
		contact.setMosoId(mosoId);
		caseObject.setContact(contact);
		
		return caseObject;
	}
}
