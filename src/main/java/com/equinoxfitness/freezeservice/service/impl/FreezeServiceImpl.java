/**
 * 
 */
package com.equinoxfitness.freezeservice.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import com.equinoxfitness.freezeservice.contract.*;
import com.equinoxfitness.freezeservice.utils.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.common.moso.contract.MosoTokenResponse;
import com.equinoxfitness.common.moso.service.impl.MosoOptimizedSessionMediatorService;
import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.commons.service.EventsService;
import com.equinoxfitness.commons.service.FacilityService;
import com.equinoxfitness.commons.utils.DateUtils;
import com.equinoxfitness.commons.utils.DynamoPK;
import com.equinoxfitness.commons.utils.ErrorMessageHandler;
import com.equinoxfitness.commons.utils.ExceptionMessageEnum;
import com.equinoxfitness.freezeservice.contract.moso.FreezeReasonResponse;
import com.equinoxfitness.freezeservice.contract.moso.InvoiceConfigValues;
import com.equinoxfitness.freezeservice.contract.moso.Item;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.PriceDetails;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionResponse;
import com.equinoxfitness.freezeservice.contract.moso.TaxRate;
import com.equinoxfitness.freezeservice.contract.moso.invoice.Invoice;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewFinalizeInvoice;
import com.equinoxfitness.freezeservice.contract.sfdc.CreateCaseResponse;
import com.equinoxfitness.freezeservice.dao.CardOnFileDao;
import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.CorpAgreementDetail;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.service.EmailService;
import com.equinoxfitness.freezeservice.service.FreezeMosoService;
import com.equinoxfitness.freezeservice.service.FreezeService;
import com.equinoxfitness.freezeservice.service.SFDCService;
import com.equinoxfitness.redis.contract.member.Member;
import com.equinoxfitness.redis.service.RedisApiService;
import com.equinoxfitness.redisUtil.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import com.equinoxfitness.commons.output.GetFacilityResponse;

/**
 * <AUTHOR>
 *
 */
@Service
public class FreezeServiceImpl implements FreezeService {
	
	private final static Logger logger = LoggerFactory.getLogger(FreezeServiceImpl.class);

	@Autowired
	FreezeServiceDAO freezeServiceDAO;

	@Autowired
	FreezeEligibilityRule freezeEligibilityRule;

	@Autowired
	FreezeServiceHelper freezeHelper;

	@Autowired
	private MosoSessionMediatorForFreeze mosoSessionMediator;

	@Autowired
	EventsService eventService;

	@Autowired
	FreezeMosoService mosoService;

	@Autowired
	FacilityService facilityService;

	@Autowired
	SFDCService sfdcService;

	@Autowired
	FacilityConversion facilityConversion;

	@Autowired
	CardOnFileDao cardOnFileDao;
	
	@Autowired
	private EmailService emailService;
	
	@Autowired
	SalesforceUtil salesforceUtil;
	
	@Autowired
	private RedisApiService redisApiService;
	
	@Autowired
	private ErrorMessageHandler errorMessageHandler;
	
	@Autowired
	MosoOptimizedSessionMediatorService mosoOptimizedSessionMediator;
	
	@Autowired
	@Qualifier("tokenNoFreezeTemplate")
	private RedisTemplate tokenNoFreezeTemplate;
	
	@Value("${moso.api.user.name}")
	private String apiApiUserName;
	
	private static final String EST_TIMZONE_REGION = "America/New_York";
	
	private static final String CONTRACT_DATE = "2023-02-06";
	
	private static final String MOSO_ERROR = "MoSo is not able to process the Freeze at the moment. Please try again later.";

	private static final DateFormat sourceFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
	
	private static final DateFormat targetFormat = new SimpleDateFormat("yyyy-MM-dd");

	private static final String REDUCED_FREEZE_CONTRACT_DATE = "2025-04-01";

	/* Method to check whether member is eligible for freeze or not */
	@Override
	public CheckFreezeEligiblityOutput checkFreezeEligibility(CheckFreezeEligibilityInput checkFreezeEligibilityInput) {

		CheckFreezeEligiblityOutput output = new CheckFreezeEligiblityOutput();
		Boolean hasCOF = false;
		/* Getting Member agreement detail from tenant database */
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO
				.getMemberAgreementDetail(checkFreezeEligibilityInput.getMemberId());
		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);

			output.setEligibleForFreeze(false);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		FundingSource fundingSource = cardOnFileDao.getFundingSource(checkFreezeEligibilityInput.getMemberId());
		if(fundingSource != null && fundingSource.getCreditCardToken() != null) {
			hasCOF = true;
		}
		/* Calling method that contains rules to verify eligibility of member */
		output = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, hasCOF);
		return output;
	}

	/* Method to check whether member is eligible for freeze or not */
	@Override
	public CheckFreezeEligiblityOutput checkFreezeEligibilityV2(CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput) {

		CheckFreezeEligiblityOutput output = new CheckFreezeEligiblityOutput();
		Boolean hasCOF = false;
		/* Getting Member agreement detail from tenant database */
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO
				.getMemberAgreementDetail(checkFreezeEligibilityInput.getMosoId());
		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			if(checkFreezeEligibilityInput.getSource().equalsIgnoreCase("clubapp")) {
				exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.PENDING_CANCELLED_EXPIRED_MEMBERSHIP.name(), EngageFreezeServiceExceptionEnum.PENDING_CANCELLED_EXPIRED_MEMBERSHIP.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
			}else {
				exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.PENDING_CANCELLED_EXPIRED_MEMBERSHIP.name(), EngageFreezeServiceExceptionEnum.PENDING_CANCELLED_EXPIRED_MEMBERSHIP.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
			}
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			output.setStatus(FreezeMembershipConstants.STATUS_ERROR);
			output.setEligibleForFreeze(false);
			return output;
		}
		FundingSource fundingSource = cardOnFileDao.getFundingSource(checkFreezeEligibilityInput.getMosoId());
		if(fundingSource != null && fundingSource.getCreditCardToken() != null) {
			hasCOF = true;
		}
		/*Getting corp agreement details from tenant database*/
		CorpAgreementDetail corpAgreementDetail = freezeServiceDAO
				.getCorpAgreementDetail(checkFreezeEligibilityInput.getMosoId());
		/*newly added code for https://equinoxfitness.atlassian.net/browse/SPACES-2845*/
		List<MemberAgreementDetail> memberAgreementDetailList = freezeServiceDAO
				.getMemberAgreementDetailList(checkFreezeEligibilityInput.getMosoId());
		BigDecimal balance=new BigDecimal(0);
		//commented below code as per https://equinoxfitness.atlassian.net/browse/SPACES-3065
		//BigDecimal balance =  freezeServiceDAO.getMosoMemberAccountBalance(checkFreezeEligibilityInput.getMosoId());		
		/* Calling method that contains rules to verify eligibility of member */
		String facilityId = facilityConversion.facilityConversion(memberAgreementDetail.getHomeFacilityId());
		output = freezeEligibilityRule.eligibilityRuleCheckV2(memberAgreementDetail, checkFreezeEligibilityInput, hasCOF,corpAgreementDetail,memberAgreementDetailList,balance,facilityId);
		/* adding facility id*/
		output.setEligibleForOneMonthFreeze(eligibleForOneMonthFreeze(facilityId));
		if (!checkFreezeEligibilityInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_MEDICAL) &&
				!checkFreezeEligibilityInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_PREGNANCY)) {
			checkDurationInDaysForEligibility(facilityId, output);
		} else {
			// check for medical and pregnancy fee flag
			output.setMonthlyFeeRate(0.0);
			String medicalPregnancyFeeFlag = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_MEDICAL_FREEZE_FEE);
			if (StringUtils.isNotBlank(medicalPregnancyFeeFlag)) {
				List<String> medicalPregnancyFeeFacilities = Arrays.asList(medicalPregnancyFeeFlag.split(","));
				if (medicalPregnancyFeeFacilities.contains(facilityId)) {
					output.setMonthlyFeeRate(FreezeMembershipConstants.MED_PREGNANCY_FEE);
				}
			}
		}
		return output;
	}
	
	private boolean eligibleForOneMonthFreeze(String homeFacilityId) {
		Map<String, String> pilotKeys = tokenNoFreezeTemplate.getRedisHashOpsData(FreezeMembershipConstants.PILOT_WEB_CANCELLATION);
		if (Objects.nonNull(pilotKeys)) {
			String facilityChangeDuration = pilotKeys.get("duration");
			List<String> facilityChangeDurations = Arrays.asList(facilityChangeDuration.split(","));
			if (Objects.nonNull(facilityChangeDurations)
				&& facilityChangeDurations.contains(homeFacilityId))
				return true;
				
		}
		return false;
	}
	
	/* Method to check whether member is eligible for freeze or not */
	public CheckFreezeEligiblityOutput checkDurationInDaysForEligibility(String facilityId, CheckFreezeEligiblityOutput output) {
		boolean isThirtyDays = false;
		boolean isFortyFiveDays = false;

		setFreezeDurationInDays(facilityId, output, isThirtyDays, isFortyFiveDays);
		setMonthlyFeeRate(facilityId, output);
		return output;
	}

	/* Method to check whether member is eligible for freeze or not */
	public CheckFreezeEligiblityOutput checkDurationInDaysForEligibilityV2(String facilityId, CheckFreezeEligiblityOutput output, String source) {
		boolean isThirtyDays = false;
		boolean isFortyFiveDays = false;

		// Retrieve contract change dates once at the top
		String reducedFreezeContractDate30Str = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_FREEZE_REDUCED_CONTRACT_CHANGE_DATE_30);
		String reducedFreezeContractDate45Str = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_FREEZE_REDUCED_CONTRACT_CHANGE_DATE_45);

		if (source.equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUBAPP)) {
			Map<String, String> clubAppKeysMap = tokenNoFreezeTemplate.getRedisHashOpsData(FreezeMembershipConstants.PILOT_CLUB_APP_FACILITIES);
			if (StringUtils.isNotBlank(output.getMemberSince())) {
				LocalDate memberSinceDate = LocalDate.parse(output.getMemberSince());
				
				// Check 30 days contract change date first
				if (Objects.nonNull(clubAppKeysMap) && !ObjectUtils.isEmpty(clubAppKeysMap)) {

					// Check 30 days logic
					if (StringUtils.isNotBlank(reducedFreezeContractDate30Str)) {
						LocalDate reducedFreezeContractDate30 = LocalDate.parse(reducedFreezeContractDate30Str);
						if (memberSinceDate.isAfter(reducedFreezeContractDate30) || memberSinceDate.isEqual(reducedFreezeContractDate30)) {
							isThirtyDays = isClubAppThirtyDaysPerYear(clubAppKeysMap, facilityId);
							output.setPilotFreezeDaysPerYear(true);
						}
					}
					
					// Check 45 days logic
					if (StringUtils.isNotBlank(reducedFreezeContractDate45Str)) {
						LocalDate reducedFreezeContractDate45 = LocalDate.parse(reducedFreezeContractDate45Str);
						if (memberSinceDate.isAfter(reducedFreezeContractDate45) || memberSinceDate.isEqual(reducedFreezeContractDate45)) {
							isFortyFiveDays = isClubAppFortyFiveDaysPerYear(clubAppKeysMap, facilityId);
							output.setPilotFreezeDaysPerYear(true);
						}
					}
				}
			}
			setClubAppFreezeDurationInDays(facilityId, output, isThirtyDays, isFortyFiveDays, clubAppKeysMap);
		} else if (source.equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE) || source.equalsIgnoreCase(FreezeMembershipConstants.SOURCE_WEB) || source.equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE)) {
			if (StringUtils.isNotBlank(output.getMemberSince())) {
				LocalDate memberSinceDate = LocalDate.parse(output.getMemberSince());
				
				// Check 30 days logic
				if (StringUtils.isNotBlank(reducedFreezeContractDate30Str)) {
					LocalDate reducedFreezeContractDate30 = LocalDate.parse(reducedFreezeContractDate30Str);
					if (memberSinceDate.isAfter(reducedFreezeContractDate30) || memberSinceDate.isEqual(reducedFreezeContractDate30)) {
						isThirtyDays = checkThirtyDaysPerYear(facilityId);
						output.setPilotFreezeDaysPerYear(true);
					}
				}
				
				// Check 45 days logic
				if (StringUtils.isNotBlank(reducedFreezeContractDate45Str)) {
					LocalDate reducedFreezeContractDate45 = LocalDate.parse(reducedFreezeContractDate45Str);
					if (memberSinceDate.isAfter(reducedFreezeContractDate45) || memberSinceDate.isEqual(reducedFreezeContractDate45)) {
						isFortyFiveDays = checkFortyFiveDaysPerYear(facilityId);
						output.setPilotFreezeDaysPerYear(true);
					}
				}
			}
			setFreezeDurationInDays(facilityId, output, isThirtyDays, isFortyFiveDays);
		}
		setMonthlyFeeRate(facilityId, output);
		return output;
	}

	private void setMonthlyFeeRate(String facilityId, CheckFreezeEligiblityOutput output) {
		Map<String, String> pilotFreezeFeeKeys = tokenNoFreezeTemplate.getRedisHashOpsData(FreezeMembershipConstants.PILOT_MONTHLY_FREEZE_FEE);
		if (Objects.nonNull(pilotFreezeFeeKeys) && !ObjectUtils.isEmpty(pilotFreezeFeeKeys)) {
			for (Map.Entry<String, String> entry : pilotFreezeFeeKeys.entrySet()) {
				if (entry.getValue().contains(facilityId)) {
					output.setMonthlyFeeRate(Double.parseDouble(entry.getKey()));
					break;
				}
			}

			Double monthlyFeeRate = output.getMonthlyFeeRate();
			if (monthlyFeeRate == null || monthlyFeeRate == 0.0) {
				output.setMonthlyFeeRate(FreezeMembershipConstants.FREEZE_FEE_50);
			}

		} else
			output.setMonthlyFeeRate(FreezeMembershipConstants.FREEZE_FEE_50);
	}

	private void setFreezeDurationInDays(String facilityId, CheckFreezeEligiblityOutput output, boolean isThirtyDays, boolean isFortyFiveDays) {
		Map<String, String> pilotFreezeEligibilityKeys = tokenNoFreezeTemplate.getRedisHashOpsData(FreezeMembershipConstants.PILOT_FREEZE_DURATION_DAYS);
		if (Objects.nonNull(pilotFreezeEligibilityKeys) && !ObjectUtils.isEmpty(pilotFreezeEligibilityKeys)) {
			List<String> matchingKeys = new ArrayList<>();
			for (Map.Entry<String, String> entry : pilotFreezeEligibilityKeys.entrySet()) {
				if (entry.getValue().contains(facilityId) && entry.getKey().contains("days")) {
					String keyValue = entry.getKey().replace("_days", "");
					matchingKeys.add(keyValue);
				}
			}
			if (!matchingKeys.isEmpty()) {
				output.setFreezeDurationInDays(matchingKeys);
				output.setDurationType(FreezeMembershipConstants.DAYS);
			} else {
				output.setFreezeDurationInDays(null);
				output.setDurationType(FreezeMembershipConstants.MONTHS);
			}

			output.setFreezeDurationInDaysAllOptions(new ArrayList<>());
		}
	}

	private void setClubAppFreezeDurationInDays(String facilityId, CheckFreezeEligiblityOutput output, boolean isThirtyDays, boolean isFortyFiveDays, Map<String, String> pilotFreezeEligibilityKeys) {
		List<String> facilityDays = getClubAppFacilityDays(pilotFreezeEligibilityKeys, facilityId);
		if (!ObjectUtils.isEmpty(facilityDays)) {
			List<String> matchingKeys = new ArrayList<>();
			List<String> allOptionsMatchingKeys = new ArrayList<>();
			if (!facilityDays.isEmpty()) {
				for (String days : facilityDays) {
					allOptionsMatchingKeys.add(days);// this is set in case we wanted to show all available options

					if (output.isJoinedAfterContractChange() && days.equals("90")) {
						continue;
					}
					if ((isThirtyDays && Integer.parseInt(days) <= 30) || (isFortyFiveDays && Integer.parseInt(days) <= 45)) {
						matchingKeys.add(days);
					} else if (!isThirtyDays && !isFortyFiveDays) {
						matchingKeys.add(days);
					}
				}
			}
			if (!matchingKeys.isEmpty()) {
				output.setFreezeDurationInDays(matchingKeys);
				output.setDurationType(FreezeMembershipConstants.DAYS);
			} else {
				output.setFreezeDurationInDays(null);
				output.setDurationType(FreezeMembershipConstants.MONTHS);
			}

			if (!allOptionsMatchingKeys.isEmpty()) {
				output.setFreezeDurationInDaysAllOptions(allOptionsMatchingKeys);
			} else {
				output.setFreezeDurationInDaysAllOptions(null);
			}
		}
	}

	private boolean checkThirtyDaysPerYear(String facilityId) {
		String pilotThirtyDaysPerYear = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_FREEZE_30_DAYS_PER_YEAR);
		List<String> thirtyDaysPerYearFacilities = pilotThirtyDaysPerYear != null && !pilotThirtyDaysPerYear.isEmpty() ? Arrays.asList(pilotThirtyDaysPerYear.split(",")) : null;
		return !CollectionUtils.isEmpty(thirtyDaysPerYearFacilities) && thirtyDaysPerYearFacilities.contains(facilityId);
	}

	private boolean checkFortyFiveDaysPerYear(String facilityId) {
		String pilotFortyFiveDaysPerYear = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_FREEZE_45_DAYS_PER_YEAR);
		List<String> fortyFiveDaysPerYearFacilities = pilotFortyFiveDaysPerYear != null && !pilotFortyFiveDaysPerYear.isEmpty() ? Arrays.asList(pilotFortyFiveDaysPerYear.split(",")) : null;
		return !CollectionUtils.isEmpty(fortyFiveDaysPerYearFacilities) && fortyFiveDaysPerYearFacilities.contains(facilityId);
	}

	/* V4 - Method to check whether member is eligible for freeze or not */
	@Override
	public CheckFreezeEligiblityOutput checkFreezeEligibilityV4(CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput) {

		CheckFreezeEligiblityOutput output = new CheckFreezeEligiblityOutput();
		Boolean hasCOF = false;
		/* Getting Member agreement detail from tenant database */
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO
				.getMemberAgreementDetail(checkFreezeEligibilityInput.getMosoId());
		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.name());
			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);

			output.setEligibleForFreeze(false);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		FundingSource fundingSource = cardOnFileDao.getFundingSource(checkFreezeEligibilityInput.getMosoId());
		if(fundingSource != null && fundingSource.getCreditCardToken() != null) {
			hasCOF = true;
		}
		/* Calling method that contains rules to verify eligibility of member */
		output = freezeEligibilityRule.eligibilityRuleCheckV4(memberAgreementDetail, checkFreezeEligibilityInput, hasCOF);
		
		return output;
	}

	/* Method to retrieve freeze reason and freeze fee amount */
	@Override
	public RetrieveFreezeReasonOutput retrieveFreezeReason(String mosoMemberId, String freezeReason, int duration) {
		logger.debug("Calling Service Impl");
		// Declaration
		RetrieveFreezeReasonOutput output = new RetrieveFreezeReasonOutput();
		String correlationId = "";
		double amount = 0;
		long startTime = System.currentTimeMillis();
		String suspensionReasonName = "";
		//int countryCode = 1 ;
		String accountingCode = "";

		/* Getting Member agreement detail from tenant database */
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(mosoMemberId);
		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		String facilityId = memberAgreementDetail.getHomeFacilityId();
		/* Calling facilityService to obtain accountingCode for Moso API calls */
		/*GetFacilityResponse facilityResponse = facilityService.getFacilityByIdV2(facilityId);

		if (facilityResponse != null && facilityResponse.getResult() != null) {*/
		accountingCode =facilityId; //facilityResponse.getResult().geteClubFacilityId();
		int countryCode = Integer.parseInt(memberAgreementDetail.getCountryCode()); //facilityResponse.getResult().getSourceSystem();

		// Get Session
		GetSessionOutput sessionOutput = (GetSessionOutput) mosoSessionMediator.getSession(countryCode,
				accountingCode, correlationId);
		if (sessionOutput == null) {
			logger.error("Fail Auth");

			/*
			 * eventService.logEvents("RetrieveFreezeREason:::" + mosoMemberId,
			 * correlationId, correlationId, mosoMemberId + ":::" + freezeReason + ":::" +
			 * duration, "GetSession Failed..!!", HttpStatus.BAD_REQUEST,
			 * System.currentTimeMillis() - startTime);
			 */

			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoMemberId, "RetrieveFreezeREason:::" + mosoMemberId, correlationId,
					correlationId, mosoMemberId + ":::" + freezeReason + ":::" + duration, "GetSession Failed..!!" ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);

			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
			message.setFriendlyMessage("Get Session failed");
			message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			return output;
		}
		/*
		 * logger.debug("AuthToken :{} " ,sessionOutput.getAuthTokenValue());
		 * logger.debug("Cookie : {} ", sessionOutput.getCookieValue());
		 * 
		 * HttpHeaders headers = new HttpHeaders();
		 * headers.setContentType(MediaType.APPLICATION_JSON); headers.set("Cookie",
		 * sessionOutput.getCookieValue());
		 */
		HttpEntity<HttpHeaders> entity = freezeHelper.setEntity(sessionOutput);

		logger.debug("Calling freeze data Moso Api");
		FreezeReasonResponse freezeReasonResponse = mosoService.getFreezeData(freezeReason, duration, sessionOutput,
				entity);
		if (freezeReasonResponse == null
				|| (freezeReasonResponse != null && !StringUtils.isBlank(freezeReasonResponse.getMessage()))) {
			/*
			 * eventService.logEvents("RetrieveFreezeREason:::" + mosoMemberId,
			 * correlationId, correlationId, mosoMemberId + ":::" + freezeReason + ":::" +
			 * duration, "Get freeze Data failed..!!", HttpStatus.BAD_REQUEST,
			 * System.currentTimeMillis() - startTime);
			 */

			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoMemberId, "RetrieveFreezeREason:::" + mosoMemberId, correlationId,
					correlationId, mosoMemberId + ":::" + freezeReason + ":::" + duration, "Get freeze Data failed..!!" ,HttpStatus.BAD_REQUEST, System.currentTimeMillis() - startTime);

			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage("Get Freeze Data moso API failed.");
			message.setFriendlyMessage("Get Freeze Data moso API failed.");
			message.setMessageID(1001);
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			return output;
		} else {
			String oneTimeFeeItem = freezeReasonResponse.getEffects().getOneTimeFeeItem();
			suspensionReasonName = freezeReasonResponse.getSuspensionReasonDescription();
			logger.debug("OneTimeFeeItem {} ", oneTimeFeeItem);
			if (StringUtils.isBlank(oneTimeFeeItem)) {
				logger.error("No item Searched required");
				ExceptionMessage message = new ExceptionMessage();
				message.setErrorMessage(FreezeServiceExceptionEnum.NO_ONE_TIME_FEE_ITEM.name());
				message.setFriendlyMessage(FreezeServiceExceptionEnum.NO_ONE_TIME_FEE_ITEM.name());
				message.setMessageID(FreezeServiceExceptionEnum.NO_ONE_TIME_FEE_ITEM.getCode());
				message.setMessageType("ERROR");
				output.setMessages(new ExceptionMessage[] { message });
				return output;
			} else {
				/* Calling moso itemSearch API to obtain item price */
				Item item = mosoService.itemSearch(oneTimeFeeItem, sessionOutput, entity);

				if (item != null && !StringUtils.isBlank(item.getMessage())) {
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(item.getMessage());
					message.setFriendlyMessage(item.getMessage());
					message.setMessageID(1001);
					message.setMessageType("Exception");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
				double itemPrice = 0.0;
				for (PriceDetails price : item.getPrices()) {
					if (price.getSourceId() == 1 && countryCode == 1) {
						itemPrice = (double) price.getPrice();
					} else if (price.getSourceId() == 2 && countryCode == 5) {
						itemPrice = (double) price.getPrice();
					} else if (price.getSourceId() == 3 && countryCode == 6) {
						itemPrice = (double) price.getPrice();
					} else
						itemPrice = 0;
					if (itemPrice > 0) {
						break;
					}
				}
				amount = itemPrice;
				/* Calling moso API to obtain tax rate */
				InvoiceConfigValues invoiceConfig = mosoService.getTaxRate(accountingCode, "TaxCodes",
						sessionOutput, entity);
				if (invoiceConfig != null && !StringUtils.isBlank(invoiceConfig.getMessage())) {
					output.setOneTimeFee(amount);
					output.setSuspensionReasonName(suspensionReasonName);
					return output;
				}
				for (TaxRate taxRate : invoiceConfig.getTaxRates()) {
					if (taxRate.getTaxGroup().getId() == 7) {
						logger.debug("Tax Rate {} ", taxRate.getRate());
						amount = amount + (itemPrice * taxRate.getRate().doubleValue()) / 100;
						logger.debug("Amount {} ", amount);
					}
				}
			}
		}
		output.setOneTimeFee(amount);
		output.setSuspensionReasonName(suspensionReasonName);
		return output;
		/*} else {
			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
			message.setFriendlyMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
			message.setMessageID(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.getCode());
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			return output;
		}*/
	}

	/* Method to extend member freeze */
	@Override
	public FreezeExtensionOutput freezeExtension(FreezeExtensionInput freezeExtensionInput) {

		logger.debug("Inside freezeExtension Impl");
		FreezeExtensionOutput output = new FreezeExtensionOutput();

		/* Getting Member agreement detail from tenant database */
		logger.debug("Calling DAO ");
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO
				.getMemberAgreementDetail(freezeExtensionInput.getMemberId());

		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		/* freeze extension is allowed only on active/Pending freeze agreements */
		if (memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("Active")
				|| memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("PENDING START")) {
			String freezeId = memberAgreementDetail.getFreezeId();
			logger.debug("FreezeId {} ", freezeId);
			String accountingCode = "";
			int countryCode = 0;
			GetFacilityResponse facilityResponse = facilityService
					.getFacilityByIdV2(freezeExtensionInput.getFacilityId());
			if (facilityResponse != null && facilityResponse.getResult() != null) {
				accountingCode = facilityResponse.getResult().geteClubFacilityId();
				countryCode = facilityResponse.getResult().getSourceSystem();
				// Get Session
				GetSessionOutput sessionOutput = (GetSessionOutput) mosoSessionMediator.getSession(countryCode,
						accountingCode, "");
				if (sessionOutput == null) {
					logger.error("Fail Auth");
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
					message.setFriendlyMessage("Get Session failed");
					message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
					message.setMessageType("Exception");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
				/*
				 * logger.debug("AuthToken : {} " , sessionOutput.getAuthTokenValue());
				 * logger.debug("Cookie : {}" ,sessionOutput.getCookieValue());
				 * 
				 * HttpHeaders headers = new HttpHeaders();
				 * headers.setContentType(MediaType.APPLICATION_JSON); headers.set("Cookie",
				 * sessionOutput.getCookieValue());
				 */
				HttpEntity<HttpHeaders> entity = freezeHelper.setEntity(sessionOutput);

				/* Calling moso API to extend freeze */
				SuspensionResponse freezeExtResponse = mosoService.freezeExtension(freezeExtensionInput, sessionOutput,
						entity, freezeId, memberAgreementDetail.getFreezeEndTime());

				if (freezeExtResponse != null && StringUtils.isBlank(freezeExtResponse.getMessage())) {
					logger.debug("Success ");
					logger.debug(freezeExtResponse.getFreezeStatus());
					output.setFreezeFeeAmount(String.valueOf(freezeExtResponse.getFreezeFeeAmount()));
					output.setFreezeExtensionEndDate(freezeExtResponse.getFreezeEndDate());
					output.setFreezeExtensionStartDate(freezeExtResponse.getFreezeStartDate());
					output.setFreezeFeeItem(freezeExtResponse.getFreezeFeeItem());
					output.setFreezeFeeStartDate(freezeExtResponse.getFreezeFeeStartDate());
					output.setFreezeStatus(freezeExtResponse.getFreezeStatus());
					output.setMemberFirstName(freezeExtResponse.getMemberFirstName());
					output.setMemberLastName(freezeExtResponse.getMemberLastName());
					output.setSuspendedAgreementName(freezeExtResponse.getSuspendedAgreementName());
					output.setSuspensionId(freezeExtResponse.getSuspensionId());

					/*
					 * If freeze fee wave off is false, obtain freeze fee and charge member on
					 * account
					 */
					if (!freezeExtensionInput.isWaiveOffExtensionFee()
							&& freezeExtensionInput.getFreezeReason().equalsIgnoreCase("Regular")) {

						/* Calling moso API to obtain free fee */
						Item item = mosoService.itemSearch("Freeze Fee - Non Refundable", sessionOutput, entity);

						double itemPrice = 0.0;
						for (PriceDetails price : item.getPrices()) {
							if (price.getSourceId() == 1 && countryCode == 1) {
								itemPrice = (double) price.getPrice();
							} else if (price.getSourceId() == 2 && countryCode == 5) {
								itemPrice = (double) price.getPrice();
							} else if (price.getSourceId() == 3 && countryCode == 6) {
								itemPrice = (double) price.getPrice();
							} else
								itemPrice = 0;
							if (itemPrice > 0) {
								break;
							}
						}
						logger.debug("Item price {} ", itemPrice);
						String itemCode = item.getCode();

						/* Setting createAndFinalizeInvoice request */
						NewFinalizeInvoice newFinalizeInvoice = freezeHelper.setCreateAndFinalizeReq(itemPrice,
								freezeExtensionInput, itemCode, memberAgreementDetail.getClientAccountId(),
								accountingCode);

						/* Calling moso API to finalize invoice */
						Invoice invoice = mosoService.createAndfinalizeInvoice(newFinalizeInvoice, sessionOutput, entity, null, countryCode, accountingCode);

						if (invoice != null && StringUtils.isBlank(invoice.getMessage())) {
							logger.debug("Success ");
							logger.debug("{}", invoice.getBalance());

						}
					}
					// Add Note in Moso
					DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
					String content = "Flow Name : FreezeExtesnion - Freeze Reason : "
							+ freezeExtensionInput.getFreezeReason() + "- Duration : "
							+ freezeExtensionInput.getExtensionDurationMonths() + " Months - Freeze Fee Waived : "
							+ freezeExtensionInput.isWaiveOffExtensionFee() + " -  Freeze End Date :  "
							+ dateFormat.format(freezeExtensionInput.getFreezeExtensionEndDate());
					Note note = freezeHelper.setnoteRequest(freezeExtensionInput.getMemberId(), content);
					/* Adding note in Moso */
					mosoService.addNote(sessionOutput, entity, note);
				} else {
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(freezeExtResponse.getMessage());
					message.setFriendlyMessage(freezeExtResponse.getMessage());
					message.setMessageID(FreezeServiceExceptionEnum.FREEZE_EXTENSION_FAILED.getCode());
					message.setMessageType("ERROR");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
			} else {
				ExceptionMessage message = new ExceptionMessage();
				message.setErrorMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
				message.setFriendlyMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
				message.setMessageID(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.getCode());
				message.setMessageType("Exception");
				output.setMessages(new ExceptionMessage[] { message });
				return output;
			}
		} else {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("No Active/Pending Freeze request exist for member.");
			exceptionMessage.setFriendlyMessage("No Active/Pending Freeze request exist for member.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		return output;
	}

	@Override
	public FreezeExtensionOutput freezeExtensionV2(FreezeExtensionInputV2 freezeExtensionInput,MemberAgreementDetail memberAgreementDetail) {

		logger.debug("Inside freezeExtension Impl");
		FreezeExtensionOutput output = new FreezeExtensionOutput();

		/* Getting Member agreement detail from tenant database */
		logger.debug("Calling DAO ");
		if (memberAgreementDetail ==null) {
			memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(freezeExtensionInput.getMosoId());
		}

		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		/* freeze extension is allowed only on active/Pending freeze agreements */
		if (memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("Active")
				|| memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("PENDING START")) {
			String freezeId = memberAgreementDetail.getFreezeId();
			logger.debug("FreezeId {} ", freezeId);
			String accountingCode = "";
			int countryCode = 0;
			GetFacilityResponse facilityResponse = facilityService
					.getFacilityByIdV2(freezeExtensionInput.getFacilityId());
			if (facilityResponse != null && facilityResponse.getResult() != null) {
				accountingCode = facilityResponse.getResult().geteClubFacilityId();
				countryCode = facilityResponse.getResult().getSourceSystem();
				// Get Session
				GetSessionOutput sessionOutput = (GetSessionOutput) mosoSessionMediator.getSession(countryCode,
						accountingCode, "");
				if (sessionOutput == null) {
					logger.error("Fail Auth");
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
					message.setFriendlyMessage("Get Session failed");
					message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
					message.setMessageType("Exception");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
				/*
				 * logger.debug("AuthToken : {} " , sessionOutput.getAuthTokenValue());
				 * logger.debug("Cookie : {}" ,sessionOutput.getCookieValue());
				 * 
				 * HttpHeaders headers = new HttpHeaders();
				 * headers.setContentType(MediaType.APPLICATION_JSON); headers.set("Cookie",
				 * sessionOutput.getCookieValue());
				 */
				HttpEntity<HttpHeaders> entity = freezeHelper.setEntity(sessionOutput);

				/* Calling moso API to extend freeze */
				SuspensionResponse freezeExtResponse = mosoService.freezeExtensionV2(freezeExtensionInput, sessionOutput,
						entity, freezeId, memberAgreementDetail.getFreezeEndTime(), countryCode, accountingCode);

				if (freezeExtResponse != null && StringUtils.isBlank(freezeExtResponse.getMessage())) {
					logger.debug("Success ");
					logger.debug(freezeExtResponse.getFreezeStatus());
					output.setFreezeFeeAmount(String.valueOf(freezeExtResponse.getFreezeFeeAmount()));
					output.setFreezeExtensionEndDate(freezeExtResponse.getFreezeEndDate());
					output.setFreezeExtensionStartDate(freezeExtResponse.getFreezeStartDate());
					output.setFreezeFeeItem(freezeExtResponse.getFreezeFeeItem());
					output.setFreezeFeeStartDate(freezeExtResponse.getFreezeFeeStartDate());
					output.setFreezeStatus(freezeExtResponse.getFreezeStatus());
					output.setMemberFirstName(freezeExtResponse.getMemberFirstName());
					output.setMemberLastName(freezeExtResponse.getMemberLastName());
					output.setSuspendedAgreementName(freezeExtResponse.getSuspendedAgreementName());
					output.setSuspensionId(freezeExtResponse.getSuspensionId());

					/*
					 * If freeze fee wave off is false, obtain freeze fee and charge member on
					 * account
					 */
					if (!freezeExtensionInput.isWaiveOffExtensionFee()
							&& freezeExtensionInput.getFreezeReason().equalsIgnoreCase("Regular")) {

						/* Calling moso API to obtain free fee */
						Item item = mosoService.itemSearch("Freeze Fee - Non Refundable", sessionOutput, entity, 
								freezeExtensionInput.getMosoId(), freezeExtensionInput.getCorrelationId(), accountingCode);

						double itemPrice = 0.0;
						for (PriceDetails price : item.getPrices()) {
							if(!StringUtils.isEmpty(price.getBusinessUnitCode())){
								itemPrice = (double) price.getPrice();
							} else if (price.getSourceId() == 1 && countryCode == 1) {
								itemPrice = (double) price.getPrice();
							} else if (price.getSourceId() == 2 && countryCode == 5) {
								itemPrice = (double) price.getPrice();
							} else if (price.getSourceId() == 3 && countryCode == 6) {
								itemPrice = (double) price.getPrice();
							} else
								itemPrice = 0;
							if (itemPrice > 0) {
								break;
							}
						}
						logger.debug("Item price {} ", itemPrice);
						String itemCode = item.getCode();

						/* Setting createAndFinalizeInvoice request */
						NewFinalizeInvoice newFinalizeInvoice = freezeHelper.setCreateAndFinalizeReqV2(itemPrice,
								freezeExtensionInput, itemCode, memberAgreementDetail.getClientAccountId(),
								accountingCode, countryCode);

						/* Calling moso API to finalize invoice */
						Invoice invoice = mosoService.createAndfinalizeInvoice(newFinalizeInvoice, sessionOutput,
								entity, freezeExtensionInput.getCorrelationId(), countryCode, accountingCode);

						if (invoice != null && StringUtils.isBlank(invoice.getMessage())) {
							logger.debug("Success ");
							logger.debug("{}", invoice.getBalance());

						}
					}
					// Add Note in Moso
					DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
					String content = "Flow Name : FreezeExtesnion - Freeze Reason : "
							+ freezeExtensionInput.getFreezeReason() + "- Duration : "
							+ freezeExtensionInput.getExtensionDurationMonths() + " Months - Freeze Fee Waived : "
							+ freezeExtensionInput.isWaiveOffExtensionFee() + " -  Freeze End Date :  "
							+ dateFormat.format(freezeExtensionInput.getFreezeExtensionEndDate());
					Note note = freezeHelper.setnoteRequest(freezeExtensionInput.getMosoId(), content);
					/* Adding note in Moso */
					mosoService.addNote(sessionOutput, entity, note, freezeExtensionInput.getCorrelationId(), countryCode, accountingCode);
				} else {
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(freezeExtResponse.getMessage());
					message.setFriendlyMessage(freezeExtResponse.getMessage());
					message.setMessageID(FreezeServiceExceptionEnum.FREEZE_EXTENSION_FAILED.getCode());
					message.setMessageType("ERROR");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
			} else {
				ExceptionMessage message = new ExceptionMessage();
				message.setErrorMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
				message.setFriendlyMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
				message.setMessageID(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.getCode());
				message.setMessageType("Exception");
				output.setMessages(new ExceptionMessage[] { message });
				return output;
			}
		} else {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("No Active/Pending Freeze request exist for member.");
			exceptionMessage.setFriendlyMessage("No Active/Pending Freeze request exist for member.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		return output;
	}

	@Override
	public FreezeMemberResponse freezeMembership(FreezeMembershipInput freezeMembershipInput) {

		// Declarations
		FreezeMemberResponse output = new FreezeMemberResponse();
		String suspensionName = freezeMembershipInput.getFreezeReasonId();
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();
		String accountingCode = "";
		int countryCode = 0;

		logger.debug("Inside freezeMembership Service Impl for member: {}", freezeMembershipInput.getMemberId());

		/*
		 * If request is from web and freeze reason is Medical/Pregnancy, create
		 * exception case in Sales Force
		 */

		if (freezeMembershipInput.getEndDate() == null && freezeMembershipInput.getDurationMonths() > 0) {
			Date startDate = freezeMembershipInput.getStartDate();
			Calendar cal = Calendar.getInstance();
			cal.setTime(startDate);
			cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
			freezeMembershipInput.setEndDate(cal.getTime());
		}

		/* Get Member agreement detail from tenant database */
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMemberId());
		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		accountingCode = memberAgreementDetail.getHomeFacilityId();
		String eClubFacilityID = facilityConversion.facilityConversion(accountingCode);

		/*
		 * If validation not required proceed with freeze else check member freeze
		 * eligibility
		 */
		if (!freezeMembershipInput.isSkipValidations()) {
			CheckFreezeEligibilityInput checkFreezeEligibilityInput = freezeHelper
					.setCheckFreezeEligibilityInput(freezeMembershipInput);
			checkFreezeEligiblityOutput = checkFreezeEligibility(checkFreezeEligibilityInput);

			/* Member not eligible for freeze */
			if (!checkFreezeEligiblityOutput.isEligibleForFreeze()) {
				logger.debug("Not Eligible for Freeze");
				ExceptionMessage exceptionMessage = new ExceptionMessage();
				exceptionMessage.setErrorMessage(checkFreezeEligiblityOutput.getMessages()[0].getErrorMessage());
				exceptionMessage.setFriendlyMessage(checkFreezeEligiblityOutput.getMessages()[0].getFriendlyMessage());
				exceptionMessage.setMessageID(checkFreezeEligiblityOutput.getMessages()[0].getMessageID());
				exceptionMessage.setMessageType(checkFreezeEligiblityOutput.getMessages()[0].getMessageType());
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				return output;
			}
		}

		countryCode = 1;
		// Get Session
		GetSessionOutput sessionOutput = (GetSessionOutput) mosoSessionMediator.getSession(countryCode, accountingCode, "");
		if (sessionOutput == null) {
			logger.error("Fail Auth");
			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
			message.setFriendlyMessage("Get Session failed");
			message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			return output;
		}
		HttpEntity<HttpHeaders> entity = freezeHelper.setEntity(sessionOutput);

		if(freezeMembershipInput.getDurationMonths() > 0 && memberAgreementDetail.getAgreementTermId() == 1) {
			logger.debug("Inside paid in full member for updating obligation expiration date for member: {}", freezeMembershipInput.getMemberId());
			DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
			Date startDate=null;
			if(memberAgreementDetail.getEndDate() != null) {
				startDate=memberAgreementDetail.getEndDate();
			}
			else {
				if( memberAgreementDetail.getObligationDate() != null) {
					startDate = memberAgreementDetail.getObligationDate();
				}
			}
			Calendar cal = Calendar.getInstance();
			cal.setTime(startDate);
			cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
			String obligationExpirationDate =dateFormat.format(cal.getTime()).replaceAll("-", "");
			MemberAgreement memberAgreement =mosoService.updateObligationExpirationDate(memberAgreementDetail.getMemberAgreementId(),
					obligationExpirationDate, sessionOutput, freezeMembershipInput.getMemberId(), "");
			if (!StringUtils.isBlank(memberAgreement.getMessage())) {

				logger.debug("Failed to update obligation expiration date for paid in full member for member: {}", freezeMembershipInput.getMemberId());
				ExceptionMessage message = new ExceptionMessage();
				message.setErrorMessage(memberAgreement.getMessage());
				message.setFriendlyMessage(memberAgreement.getMessage());
				message.setMessageID(FreezeServiceExceptionEnum.UPDATE_EXPIRYDATE_FAILED.getCode());
				message.setMessageType("Exception");
				output.setMessages(new ExceptionMessage[] { message });
				output.setRequestReceivedSuccessfully(false);
				return output;
			}
		}
		if ((freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Pregnancy")
				|| freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Medical"))
				&& freezeMembershipInput.getSource().equalsIgnoreCase("Web")) {
			if(sfdcService.createCase(freezeMembershipInput, eClubFacilityID, 0.0) == null)
			{
				logger.debug("Failed to Create Exception case in SF for member: {}", freezeMembershipInput.getMemberId());
				output.setRequestReceivedSuccessfully(false);
			}
			logger.debug("Create Exception case in SF for member: {}", freezeMembershipInput.getMemberId());
			output.setRequestReceivedSuccessfully(true);
			return output;
		}

		if (freezeMembershipInput.isWaveFreezeFee()) {
			suspensionName = "Regular (Fee Waive)";
		} else if (freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Regular")) {
			suspensionName = freezeMembershipInput.getFreezeReasonId() + " " + freezeMembershipInput.getDurationMonths() + " Month";
		}

		logger.info("Suspension Name: {} for member: {}", suspensionName, freezeMembershipInput.getMemberId());

		/* Obtain freeze reason id from tenant */
		String freezeReasonId = freezeServiceDAO.getSuspensionReasonId(suspensionName);

		logger.info("FreezeReasonId: {} for member: {}", freezeReasonId, freezeMembershipInput.getMemberId());

		/* Freeze reason id is mandatory for freeze */
		if (StringUtils.isBlank(freezeReasonId)) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("Freeze reasonID is null");
			exceptionMessage.setFriendlyMessage("Freeze reasonID is null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType("Error");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		SuspensionRequest suspensionRequest = freezeHelper.setSuspensionRequest(freezeReasonId, memberAgreementDetail, freezeMembershipInput);

		/* Calling moso API to freeze member agreement */
		SuspensionResponse suspensionResponse = mosoService.freezeAgreement(suspensionRequest, entity, "", accountingCode, countryCode);

		/* If success add note in Moso and create resolved case in sales force */
		if (suspensionResponse != null && StringUtils.isBlank(suspensionResponse.getMessage())) {
			output.setRequestReceivedSuccessfully(true);
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			String content = "Flow Name : Freeze - Freeze Reason : " + freezeMembershipInput.getFreezeReasonId()
			+ "- Duration : " + freezeMembershipInput.getDurationMonths() + " Months - Freeze Fee Waived : "
			+ freezeMembershipInput.isWaveFreezeFee() + " Freeze Start Date :  "
			+ dateFormat.format(freezeMembershipInput.getStartDate()) + " -  Freeze End Date :  "
			+ dateFormat.format(freezeMembershipInput.getEndDate());

			/* Setting note request */
			Note note = freezeHelper.setnoteRequest(freezeMembershipInput.getMemberId(), content);
			/* Adding note in Moso */
			mosoService.addNote(sessionOutput, entity, note);

			/* Creating resolved case in Sales force */
			if (!StringUtils.isBlank(freezeMembershipInput.getSource()) && (freezeMembershipInput.getSource().equalsIgnoreCase("Web") ||
					freezeMembershipInput.getSource().equalsIgnoreCase("Website"))) {
				Double freezeFeeAmount = Objects.nonNull(suspensionResponse) && Objects.nonNull(suspensionResponse.getFreezeFeeAmount()) && freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Regular") ? suspensionResponse.getFreezeFeeAmount().doubleValue() : 0.0;
				CreateCaseResponse createCaseResponse = sfdcService.createCase(freezeMembershipInput, eClubFacilityID, freezeFeeAmount);
				
				SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy"); 
				//Send Freeze Confirmation Email to member if Freeze Reason is Regular
				if(freezeMembershipInput.getSource().equalsIgnoreCase("Website") && freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Regular")) {
					logger.info("Sending Freeze confirmation email to {} for member: {}, as Freeze Reason is : {}", freezeMembershipInput.getEmailAddress(), freezeMembershipInput.getMemberId(), freezeMembershipInput.getFreezeReasonId());
					emailService.sendRegularFreezeConfirmationEmail(freezeMembershipInput.getMemberId(), freezeMembershipInput.getEmailAddress(), 
							memberAgreementDetail.getFirstName(), memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), freezeMembershipInput.getFreezeFees(), freezeMembershipInput.getDurationMonths(), freezeMembershipInput.getChargeDate());
				}
				
				//Send Freeze Requested Email to member if Freeze Reason is Medical or Pregnancy and Store the Freeze Document in Salesforce
				if(freezeMembershipInput.getSource().equalsIgnoreCase("Website") && !freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Regular")) {
					logger.info("Sending Freeze Requested email to {} for member: {}, as Freeze Reason is : {}", freezeMembershipInput.getEmailAddress(), freezeMembershipInput.getMemberId(), freezeMembershipInput.getFreezeReasonId());
					emailService.sendMedicalAndPregnancyFreezeRequestEmail(freezeMembershipInput.getMemberId(), freezeMembershipInput.getEmailAddress(), 
							memberAgreementDetail.getFirstName(), sdf.format(freezeMembershipInput.getStartDate()), freezeMembershipInput.getDurationMonths());

					boolean isEmailDetailsStored = freezeServiceDAO.storeEmailDetailsForMedicalPreganancyFreeze(freezeMembershipInput.getMemberId(), memberAgreementDetail.getFirstName(), freezeMembershipInput.getEmailAddress(), 
							freezeMembershipInput.getStartDate(), freezeMembershipInput.getDurationMonths(), freezeMembershipInput.getFreezeReasonId(), 
							LocalDate.now().plusDays(1).toString());
					logger.info("Is Email details stored successfully in DB dbo.Freeze_Confirmation_Email for member: {}, {}", freezeMembershipInput.getMemberId(), isEmailDetailsStored);
					
					//Store the Freeze documents in Salesforce for 'Medical' type only if the Case is created successfully
					if(Objects.nonNull(createCaseResponse) && !StringUtils.isEmpty(createCaseResponse.getId())) {
						sfdcService.storeFreezeDocumentinSF(freezeMembershipInput, memberAgreementDetail.getFirstName(), createCaseResponse.getId());
					}	
				}
				if(Objects.nonNull(freezeMembershipInput.getFreezeFees()))
					output.setFreezeFeeAmount(freezeMembershipInput.getFreezeFees().toString());
				
				output.setFreezeFeeStartDate(sdf.format(freezeMembershipInput.getStartDate()));
				output.setFreezeStatus("Freeze");
				output.setMemberFirstName(memberAgreementDetail.getFirstName());
				output.setSuspendedAgreementName(memberAgreementDetail.getMembershipClass());
				output.setSuspensionId(Integer.valueOf(freezeReasonId));
				output.setSuspensionReasonName(suspensionName);
			}
		} else {
			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage(suspensionResponse.getMessage());
			message.setMessageID(FreezeServiceExceptionEnum.FREEZE_MEMBERSHIP_FAILED.getCode());
			message.setMessageType("Exception");
			
			if(Objects.nonNull(suspensionResponse) && suspensionResponse.getMessage().contains("already"))
				message.setFriendlyMessage(FreezeMembershipConstants.ACTIVE_PENDING_FREEZE_FRIENDLY_MESSAGE);
			else
				message.setFriendlyMessage(suspensionResponse.getMessage());
			
			output.setMessages(new ExceptionMessage[] { message });
			output.setRequestReceivedSuccessfully(false);
		}

		return output;
	}

	@Override
	public FreezeMemberResponse freezeMembershipV2(FreezeMembershipInputV2 freezeMembershipInput) {

		// Declarations
		FreezeMemberResponse output = new FreezeMemberResponse();
		String suspensionName = freezeMembershipInput.getFreezeReasonId();
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();
		String accountingCode = "";
		int countryCode = 0;

		logger.debug("Inside Service Impl");

		/*
		 * If request is from web and freeze reason is Medical/Pregnancy, create
		 * exception case in Sales Force
		 */

		if (freezeMembershipInput.getEndDate() == null && freezeMembershipInput.getDurationMonths() > 0) {
			Date startDate = freezeMembershipInput.getStartDate();
			Calendar cal = Calendar.getInstance();
			cal.setTime(startDate);
			cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
			freezeMembershipInput.setEndDate(cal.getTime());
		}

		/* Get Member agreement detail from tenant database */
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO
				.getMemberAgreementDetail(freezeMembershipInput.getMosoId());
		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		accountingCode = memberAgreementDetail.getHomeFacilityId();
		String eClubFacilityID = facilityConversion.facilityConversion(accountingCode);

		/*
		 * If FreezeMembershipConstants.VALIDATION not required proceed with freeze else check member freeze
		 * eligibility
		 */
		if (!freezeMembershipInput.isSkipValidations()) {
			CheckFreezeEligibilityInput checkFreezeEligibilityInput = freezeHelper
					.setCheckFreezeEligibilityInputV2(freezeMembershipInput);
			checkFreezeEligiblityOutput = checkFreezeEligibility(checkFreezeEligibilityInput);

			/* Member not eligible for freeze */
			if (!checkFreezeEligiblityOutput.isEligibleForFreeze()) {
				logger.debug("Not Eligible for Freeze");
				ExceptionMessage exceptionMessage = new ExceptionMessage();
				exceptionMessage.setErrorMessage(checkFreezeEligiblityOutput.getMessages()[0].getErrorMessage());
				exceptionMessage.setFriendlyMessage(checkFreezeEligiblityOutput.getMessages()[0].getFriendlyMessage());
				exceptionMessage.setMessageID(checkFreezeEligiblityOutput.getMessages()[0].getMessageID());
				exceptionMessage.setMessageType(checkFreezeEligiblityOutput.getMessages()[0].getMessageType());
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				return output;
			}
		}

		if(memberAgreementDetail.getStartDate() != null) {
			if(freezeMembershipInput.getStartDate().before(memberAgreementDetail.getStartDate())) {
				logger.debug("Not eligible for freeze since the freeze start date cannot be before agreement start date");
				ExceptionMessage exceptionMessage = new ExceptionMessage();
				exceptionMessage.setErrorMessage("The FreezeStartDate cannot be before AgreementStartDate.");
				exceptionMessage.setFriendlyMessage("The FreezeStartDate cannot be before AgreementStartDate.");
				exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_STARTDATE_INVALID.getCode());
				exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				return output;
			}
		}
		countryCode = 1;
		// Get Session
		GetSessionOutput sessionOutput = (GetSessionOutput) mosoSessionMediator.getSession(countryCode, accountingCode,
				"");
		if (sessionOutput == null) {
			logger.error("Fail Auth");
			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
			message.setFriendlyMessage("Get Session failed");
			message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			return output;
		}
		HttpEntity<HttpHeaders> entity = freezeHelper.setEntity(sessionOutput);

		if(freezeMembershipInput.getDurationMonths() > 0 && memberAgreementDetail.getAgreementTermId() == 1) {
			logger.debug("Inside paid in full member for updating obligation expiration date");
			DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
			Date startDate=null;
			if(memberAgreementDetail.getEndDate() != null) {
				startDate=memberAgreementDetail.getEndDate();
			}
			else {
				if( memberAgreementDetail.getObligationDate() != null) {
					startDate = memberAgreementDetail.getObligationDate();
				}
			}
			Calendar cal = Calendar.getInstance();
			cal.setTime(startDate);
			cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
			String obligationExpirationDate =dateFormat.format(cal.getTime()).replaceAll("-", "");
			MemberAgreement memberAgreement =mosoService.updateObligationExpirationDate(memberAgreementDetail.getMemberAgreementId(),
					obligationExpirationDate, sessionOutput, freezeMembershipInput.getMosoId(), freezeMembershipInput.getCorrelationId());
			if (!StringUtils.isBlank(memberAgreement.getMessage())) {

				logger.debug("Failed to update obligation expiration date for paid in full member");
				ExceptionMessage message = new ExceptionMessage();
				message.setErrorMessage(memberAgreement.getMessage());
				message.setFriendlyMessage(memberAgreement.getMessage());
				message.setMessageID(FreezeServiceExceptionEnum.UPDATE_EXPIRYDATE_FAILED.getCode());
				message.setMessageType("Exception");
				output.setMessages(new ExceptionMessage[] { message });
				output.setRequestReceivedSuccessfully(false);
				return output;
			}
		}
		if ((freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Pregnancy")
				|| freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Medical"))
				&& freezeMembershipInput.getSource().equalsIgnoreCase("Web")) {
			if(sfdcService.createCaseV2(freezeMembershipInput, eClubFacilityID, 0.0,false) == null)
			{
				logger.debug("Failed to Create Excption case in SF");
				output.setRequestReceivedSuccessfully(false);
			}
			logger.debug("Create Excption case in SF");
			output.setRequestReceivedSuccessfully(true);
			return output;
		}

		if (freezeMembershipInput.isWaveFreezeFee() && freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Regular")) {
			suspensionName = "Regular (Fee Waive)";
		} else if(freezeMembershipInput.isWaveFreezeFee() && freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Regular (In Ob Exception)")) {
			suspensionName = "Regular (In Ob Exception)";
		}else if (freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase("Regular")) {
			suspensionName = freezeMembershipInput.getFreezeReasonId() + " " + freezeMembershipInput.getDurationMonths()
			+ " Month";
		}

		logger.debug("Suspension Name: {}",suspensionName);

		/* Obtain freeze reason id from tenant */
		String freezeReasonId = freezeServiceDAO.getSuspensionReasonId(suspensionName);

		logger.debug("freezeReasonId " + freezeReasonId);

		/* Freeze reason id is mandatory for freeze */
		if (StringUtils.isBlank(freezeReasonId)) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("Freeze reasonID is null.");
			exceptionMessage.setFriendlyMessage("Freeze reasonID is null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType("Error");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		SuspensionRequest suspensionRequest = freezeHelper.setSuspensionRequestV2(freezeReasonId, memberAgreementDetail,
				freezeMembershipInput);

		/* Calling moso API to freeze member agreement */
		SuspensionResponse suspensionResponse = mosoService.freezeAgreement(suspensionRequest, entity, 
				freezeMembershipInput.getCorrelationId(), accountingCode, countryCode);

		/* If success add note in Moso and create resolved case in sales force */
		if (suspensionResponse != null && StringUtils.isBlank(suspensionResponse.getMessage())) {
			output.setRequestReceivedSuccessfully(true);
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			String content = "Flow Name : Freeze - Freeze Reason : " + freezeMembershipInput.getFreezeReasonId()
			+ "- Duration : " + freezeMembershipInput.getDurationMonths() + " Months - Freeze Fee Waived : "
			+ freezeMembershipInput.isWaveFreezeFee() + " Freeze Start Date :  "
			+ dateFormat.format(freezeMembershipInput.getStartDate()) + " -  Freeze End Date :  "
			+ dateFormat.format(freezeMembershipInput.getEndDate());

			/* Setting note request */
			Note note = freezeHelper.setnoteRequest(freezeMembershipInput.getMosoId(), content);
			/* Adding note in Moso */
			mosoService.addNote(sessionOutput, entity, note, freezeMembershipInput.getCorrelationId(), countryCode, accountingCode);

			/* Creating resolved case in Sales force */
			if (!StringUtils.isBlank(freezeMembershipInput.getSource()) && freezeMembershipInput.getSource().equalsIgnoreCase("Web")) {
				sfdcService.createCaseV2(freezeMembershipInput, eClubFacilityID, freezeMembershipInput.getFreezeFees().doubleValue(),true);
			}
		} else {
			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage(suspensionResponse.getMessage());
			message.setFriendlyMessage(suspensionResponse.getMessage());
			message.setMessageID(FreezeServiceExceptionEnum.FREEZE_MEMBERSHIP_FAILED.getCode());
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			output.setRequestReceivedSuccessfully(false);
		}
		return output;
	}

	@Override
	public FreezeExtensionOutput freezeExtensionV4(FreezeExtensionInputV2 freezeExtensionInput, MemberAgreementDetail memberAgreementDetail, 
			FreezeMembershipInputV2 freezeMembershipInput,String email) {
		logger.debug("Inside freezeExtension Impl");
		FreezeExtensionOutput output = new FreezeExtensionOutput();

//		/* Getting Member agreement detail from tenant database */
//		logger.debug("Calling DAO ");
//		if (memberAgreementDetail ==null) {
//			memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(freezeExtensionInput.getMosoId());
//		}
//		if (memberAgreementDetail == null) {
//			ExceptionMessage exceptionMessage = new ExceptionMessage();
//			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
//			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
//			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
//			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
//			output.setMessages(new ExceptionMessage[] { exceptionMessage });
//			return output;
//		}

		/* freeze extension is allowed only on active/Pending freeze agreements */
		if (memberAgreementDetail.getFreezeStatus().equalsIgnoreCase(FreezeMembershipConstants.FREEZE_ACTIVE_STATUS)
				|| memberAgreementDetail.getFreezeStatus().equalsIgnoreCase(FreezeMembershipConstants.FREEZE_PENDING_START)) {
			String freezeId = memberAgreementDetail.getFreezeId();
			logger.debug("FreezeId {} ", freezeId);
			String accountingCode = "";
			String suspensionName = "";
			int countryCode = 0;
			GetFacilityResponse facilityResponse = facilityService
					.getFacilityByIdV2(freezeExtensionInput.getFacilityId());
			if (facilityResponse != null && facilityResponse.getResult() != null) {
				accountingCode = facilityResponse.getResult().geteClubFacilityId();
				countryCode = facilityResponse.getResult().getSourceSystem();
				MosoTokenResponse mosoTokenResponse = mosoOptimizedSessionMediator.getSession(accountingCode, apiApiUserName, countryCode);
				if (Objects.isNull(mosoTokenResponse)) {
					logger.error("Fail Auth");
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
					message.setFriendlyMessage("Get Session failed");
					message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
					message.setMessageType("Exception");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
				/*
				 * logger.debug("AuthToken : {} " , sessionOutput.getAuthTokenValue());
				 * logger.debug("Cookie : {}" ,sessionOutput.getCookieValue());
				 * 
				 * HttpHeaders headers = new HttpHeaders();
				 * headers.setContentType(MediaType.APPLICATION_JSON); headers.set("Cookie",
				 * sessionOutput.getCookieValue());
				 */
				HttpEntity<HttpHeaders> entity = freezeHelper.setEntityV4(mosoTokenResponse);

				/* Calling moso API to extend freeze */
				SuspensionResponse freezeExtResponse = mosoService.freezeExtensionV4(freezeExtensionInput, freezeId, memberAgreementDetail.getFreezeEndTime(), countryCode, accountingCode);

				if (freezeExtResponse != null && StringUtils.isBlank(freezeExtResponse.getMessage())) {
					logger.debug("Success ");
					logger.debug(freezeExtResponse.getFreezeStatus());
					output.setFreezeFeeAmount(String.valueOf(freezeExtResponse.getFreezeFeeAmount()));
					output.setFreezeExtensionEndDate(freezeExtResponse.getFreezeEndDate());
					output.setFreezeExtensionStartDate(freezeExtResponse.getFreezeStartDate());
					output.setFreezeFeeItem(freezeExtResponse.getFreezeFeeItem());
					output.setFreezeFeeStartDate(freezeExtResponse.getFreezeFeeStartDate());
					output.setFreezeStatus(freezeExtResponse.getFreezeStatus());
					output.setMemberFirstName(freezeExtResponse.getMemberFirstName());
					output.setMemberLastName(freezeExtResponse.getMemberLastName());
					output.setSuspendedAgreementName(freezeExtResponse.getSuspendedAgreementName());
					output.setSuspensionId(freezeExtResponse.getSuspensionId());

					if(freezeMembershipInput.isInObligationFlag() == false) {
						/*
						 * If freeze fee wave off is false, obtain freeze fee and charge member on
						 * account
						 */
						if (!freezeExtensionInput.isWaiveOffExtensionFee()
								&& freezeExtensionInput.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {

							/* Calling moso API to obtain free fee */
							Item item = mosoService.itemSearchV4("Freeze Fee - Non Refundable", mosoTokenResponse, entity, 
									freezeExtensionInput.getMosoId(), freezeExtensionInput.getCorrelationId(), accountingCode);

							double itemPrice = 0.0;
							for (PriceDetails price : item.getPrices()) {
								if(!StringUtils.isEmpty(price.getBusinessUnitCode())){
									itemPrice = (double) price.getPrice();
								} else if (price.getSourceId() == 1 && countryCode == 1) {
									itemPrice = (double) price.getPrice();
								} else if (price.getSourceId() == 2 && countryCode == 5) {
									itemPrice = (double) price.getPrice();
								} else if (price.getSourceId() == 3 && countryCode == 6) {
									itemPrice = (double) price.getPrice();
								} else
									itemPrice = 0;
								if (itemPrice > 0) {
									break;
								}
							}
							logger.debug("Item price: {} ", itemPrice);
							String itemCode = item.getCode();

							/* Setting createAndFinalizeInvoice request */
							NewFinalizeInvoice newFinalizeInvoice = freezeHelper.setCreateAndFinalizeReqV2(itemPrice,
									freezeExtensionInput, itemCode, memberAgreementDetail.getClientAccountId(),
									accountingCode, countryCode);

							/* Calling moso API to finalize invoice */
							Invoice invoice = mosoService.createAndfinalizeInvoiceV4(newFinalizeInvoice, freezeExtensionInput.getCorrelationId(), countryCode, accountingCode);

							if (invoice != null && StringUtils.isBlank(invoice.getMessage())) {
								logger.debug("Success ");
								logger.debug("{}", invoice.getBalance());

							}
						}
					}
					
					/* Freeze suspension reasons */
					suspensionName = freezeMembershipInput.getFreezeReasonId();
					if(freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
						suspensionName = getSuspensionName(freezeMembershipInput, memberAgreementDetail);
					}
					
					// Add Note in Moso
					DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
					String content = "Flow Name : FreezeExtesnion - Freeze Reason : "
							+ suspensionName + "- Duration : "
							+ freezeExtensionInput.getExtensionDurationMonths() + " Months - Freeze Fee Waived : "
							+ freezeExtensionInput.isWaiveOffExtensionFee() + " -  Freeze End Date :  "
							+ dateFormat.format(freezeExtensionInput.getFreezeExtensionEndDate());
					Note note = freezeHelper.setnoteRequest(freezeExtensionInput.getMosoId(), content);
					/* Adding note in Moso */
					mosoService.addNoteV4(note, freezeExtensionInput.getCorrelationId(), countryCode, accountingCode);
				} else {
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(freezeExtResponse.getMessage());
					message.setFriendlyMessage(freezeExtResponse.getMessage());
					message.setMessageID(FreezeServiceExceptionEnum.FREEZE_EXTENSION_FAILED.getCode());
					message.setMessageType("ERROR");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
			} else {
				ExceptionMessage message = new ExceptionMessage();
				message.setErrorMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
				message.setFriendlyMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
				message.setMessageID(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.getCode());
				message.setMessageType("Exception");
				output.setMessages(new ExceptionMessage[] { message });
				return output;
			}
		} else {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("No Active/Pending Freeze request exist for member.");
			exceptionMessage.setFriendlyMessage("No Active/Pending Freeze request exist for member.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		CreateCaseResponse createCaseResponse = null;
		SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
		Date endDate = freezeMembershipInput.getEndDate();
		Date chargeDate = Calendar.getInstance().getTime();
		//Send Freeze Confirmation Email to PIF members if source is engage.
		if(freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) && (freezeMembershipInput.isPIF() || memberAgreementDetail.getAgreementTermId() == 1)) {
			logger.info("Sending Freeze confirmation email to {} for PIF member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
			boolean emailsent = emailService.sendFreezeConfirmationEmailToPIFMembers(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(), 
					memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), sdf.format(chargeDate),
					freezeMembershipInput.getFreezeFees(), freezeMembershipInput.getDurationMonths());
			createEmailTaskForFreezeConfirmation(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent,true);
			return output;
		}
		//Send Freeze Confirmation Email to member if Freeze Reason is Regular
		if(freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {				
			logger.info("Sending Freeze confirmation email to {} for member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
			boolean emailsent = emailService.sendRegularFreezeConfirmationEmailV4(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(), 
					memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), sdf.format(chargeDate),
					freezeMembershipInput.getFreezeFees(), freezeMembershipInput.getDurationMonths(), freezeMembershipInput.isInObligationFlag(),freezeMembershipInput.getSource(),freezeMembershipInput.isHoldBillingInObFlag());
			
			createEmailTaskForFreezeConfirmation(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent, false);
		} else {
//			// medical Request email
//			logger.info("Sending Freeze Requested email to {} for member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
//			emailService.sendMedicalAndPregnancyFreezeRequestEmail(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(), 
//					sdf.format(freezeMembershipInput.getStartDate()), freezeMembershipInput.getDurationMonths());
//
//			boolean isEmailDetailsStored = freezeServiceDAO.storeEmailDetailsForMedicalPreganancyFreezeV4(freezeMembershipInput.getMosoId(), memberAgreementDetail.getFirstName(), email, 
//					freezeMembershipInput.getStartDate(), freezeMembershipInput.getDurationMonths(), freezeMembershipInput.getFreezeReasonId(), 
//					LocalDate.now().plusDays(1).toString(), endDate);
//			logger.info("Is Email details stored successfully in DB dbo.Freeze_Confirmation_Email for member: {}, {}", freezeMembershipInput.getMosoId(), isEmailDetailsStored);
			
			logger.info("Sending medical freeze confirmation mail in extension flow for email to {} for member: {}, as freeze reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
			boolean emailsent = emailService.sendMedicalAndPregnancyFreezeConfirmationEmail(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), freezeMembershipInput.getDurationMonths());
			createEmailTaskForFreezeConfirmation(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent, false);
		}
		return output;
	}
	
	

	@Override
	public FreezeMemberResponse freezeMembershipV4(FreezeMembershipInputV2 freezeMembershipInput, boolean waveFreezeFee, int countryCode, 
			String email, BigDecimal freezeFees, MemberAgreementDetail memberAgreementDetail) {
		// Declarations
		FreezeMemberResponse output = new FreezeMemberResponse();
		String suspensionName = freezeMembershipInput.getFreezeReasonId();
		String accountingCode = "";
		logger.debug("Inside Service Impl");
		
		if (freezeMembershipInput.getEndDate() == null && freezeMembershipInput.getDurationMonths() > 0) {
			Date startDate = freezeMembershipInput.getStartDate();
			Calendar cal = Calendar.getInstance();
			cal.setTime(startDate);
			cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
			freezeMembershipInput.setEndDate(cal.getTime());
		}

//		/* Get Member agreement detail from tenant database */
//		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO
//				.getMemberAgreementDetail(freezeMembershipInput.getMosoId());
//		if (memberAgreementDetail == null) {
//			ExceptionMessage exceptionMessage = new ExceptionMessage();
//			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
//			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
//			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
//			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
//			output.setMessages(new ExceptionMessage[] { exceptionMessage });
//			return output;
//		}

		String eClubFacilityID = "";
		if (freezeMembershipInput.getFacilityId() != null && (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUBAPP)
			|| (freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_MEDICAL) || freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_PREGNANCY)))) {
			eClubFacilityID = facilityConversion.facilityConversion(freezeMembershipInput.getFacilityId());
		} else {
			eClubFacilityID = facilityConversion.facilityConversion(memberAgreementDetail.getHomeFacilityId());
		}

		accountingCode = memberAgreementDetail.getHomeFacilityId();

		if(memberAgreementDetail.getStartDate() != null) {
			if(freezeMembershipInput.getStartDate().before(memberAgreementDetail.getStartDate())) {
				logger.error("Not eligible for freeze since the freeze start date cannot be before agreement start date");
				ExceptionMessage exceptionMessage = new ExceptionMessage();
				exceptionMessage.setErrorMessage("The FreezeStartDate cannot be before AgreementStartDate.");
				exceptionMessage.setFriendlyMessage("The FreezeStartDate cannot be before AgreementStartDate.");
				exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_STARTDATE_INVALID.getCode());
				exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				return output;
			}
		}
		MosoTokenResponse mosoTokenResponse = mosoOptimizedSessionMediator.getSession(accountingCode, apiApiUserName, countryCode);
		if (Objects.isNull(mosoTokenResponse)) {
			logger.error("Fail Auth");
			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
			message.setFriendlyMessage("Get Session failed");
			message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			return output;
		}
		HttpEntity<HttpHeaders> entity = freezeHelper.setEntityV4(mosoTokenResponse);

		if(freezeMembershipInput.getDurationMonths() > 0 && memberAgreementDetail.getAgreementTermId() == 1) {
			logger.info("Inside paid in full member for updating obligation expiration date: {}", freezeMembershipInput.getMosoId());
			DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
			Date startDate=null;
			if(memberAgreementDetail.getEndDate() != null) {
				startDate=memberAgreementDetail.getEndDate();
			}
			else {
				if( memberAgreementDetail.getObligationDate() != null) {
					startDate = memberAgreementDetail.getObligationDate();
				}
			}
			Calendar cal = Calendar.getInstance();
			cal.setTime(startDate);
			cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
			String obligationExpirationDate = dateFormat.format(cal.getTime()).replaceAll("-", "");
			MemberAgreement memberAgreement = mosoService.updateObligationExpirationDateV4(memberAgreementDetail.getMemberAgreementId(),
					obligationExpirationDate, mosoTokenResponse, freezeMembershipInput.getMosoId(), freezeMembershipInput.getCorrelationId(), countryCode, accountingCode);
			if (!StringUtils.isBlank(memberAgreement.getMessage())) {
				logger.debug("Failed to update obligation expiration date for paid in full member");
				ExceptionMessage message = new ExceptionMessage();
				message.setErrorMessage(memberAgreement.getMessage());
				message.setFriendlyMessage(memberAgreement.getMessage());
				message.setMessageID(FreezeServiceExceptionEnum.UPDATE_EXPIRYDATE_FAILED.getCode());
				message.setMessageType("Exception");
				output.setMessages(new ExceptionMessage[] { message });
				output.setRequestReceivedSuccessfully(false);
				return output;
			}
		}

		// check if university freeze fee flag is on
		if (freezeMembershipInput.isCancelFlow() && freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			String universityFreezeFacilities = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_UNIVERSITY_FREEZE_FEE);
			if (StringUtils.isNotBlank(universityFreezeFacilities)) {
				List<String> universityFreezeFacilitiesList = Arrays.asList(universityFreezeFacilities.split(","));
				if (universityFreezeFacilitiesList.contains(eClubFacilityID)) {
					freezeMembershipInput.setUniversityMember(freezeHelper.isUniversityMember(freezeMembershipInput.getMosoId()));
					if (freezeMembershipInput.isUniversityMember()) {
						freezeMembershipInput.setInObligationFlag(false);
					}
				}
			}
		}

		/* Freeze suspension reasons */
		if(freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			suspensionName = getSuspensionName(freezeMembershipInput,memberAgreementDetail);
		}

		// check for pilot medical and pregnancy freeze fee flag in redis for engage freeze submission
		if (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) && !freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)
			&& isMedicalFreezeFeeEnabled(eClubFacilityID)) {
			freezeMembershipInput.setPilotFreezeMedFee(true);
		}

		if (freezeMembershipInput.isPilotFreezeMedFee() &&
				!freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)
				&& !(freezeMembershipInput.isWaveFreezeFee() && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE))) {

			suspensionName = freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_MEDICAL)
					? FreezeMembershipConstants.MEDICAL_FEE
					: FreezeMembershipConstants.PREGNANCY_FEE;

			freezeMembershipInput.setFreezeFees(getFreezeFees(freezeMembershipInput.getDurationMonths(), memberAgreementDetail.getCountryCode()));
		} else if (!freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			freezeMembershipInput.setFreezeFees(BigDecimal.ZERO);
		}

		logger.info("Setting Freeze Suspension Name: {} for member: {}", suspensionName, freezeMembershipInput.getMosoId());

		/* Obtain freeze reason id from tenant */
		String freezeReasonId = freezeServiceDAO.getSuspensionReasonId(suspensionName);
		logger.info("Retrieved freezeReasonId: {} from Moso DB for member: {}", freezeReasonId, freezeMembershipInput.getMosoId());

		/* Freeze reason id is mandatory for freeze */
		if (StringUtils.isBlank(freezeReasonId)) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("Freeze reasonID is null.");
			exceptionMessage.setFriendlyMessage("Freeze reasonID is null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType("Error");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		SuspensionRequest suspensionRequest = freezeHelper.setSuspensionRequestV2(freezeReasonId, memberAgreementDetail,
				freezeMembershipInput);

		/* Calling moso API to freeze member agreement */
		SuspensionResponse suspensionResponse = mosoService.freezeAgreementV4(suspensionRequest, mosoTokenResponse, entity, 
				freezeMembershipInput.getCorrelationId(), accountingCode, countryCode);

		/* If success add note in Moso and create resolved case in sales force */
		if (suspensionResponse != null && StringUtils.isBlank(suspensionResponse.getMessage())) {
			output.setRequestReceivedSuccessfully(true);
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			String content = "Flow Name : Freeze - Freeze Reason : " + suspensionName
			+ "- Duration : " + freezeMembershipInput.getDurationMonths() + " Months - Freeze Fee Waived : "
			+ freezeMembershipInput.isWaveFreezeFee() + " Freeze Start Date :  "
			+ dateFormat.format(freezeMembershipInput.getStartDate()) + " -  Freeze End Date :  "
			+ dateFormat.format(freezeMembershipInput.getEndDate());

			// create and finalize invoice for freeze (medical and pregnancy)
			if (!freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)
					&& freezeMembershipInput.isPilotFreezeMedFee() && !(freezeMembershipInput.isWaveFreezeFee() && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE))) {
				createAndFinalizeInvoiceForFreeze(accountingCode, countryCode, freezeMembershipInput.getDurationMonths(), freezeMembershipInput.getMosoId(), memberAgreementDetail.getClientAccountId(), entity, eClubFacilityID, freezeMembershipInput.getCorrelationId());
			}

			/* Setting note request */
			Note note = freezeHelper.setnoteRequest(freezeMembershipInput.getMosoId(), content);
			/* Adding note in Moso */
			mosoService.addNoteV4(note, freezeMembershipInput.getCorrelationId(), countryCode, accountingCode);

			/* Creating resolved case in Sales force */
			CreateCaseResponse createCaseResponse = null;
			SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
			if(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK.equalsIgnoreCase(freezeMembershipInput.getSource()) 
					|| FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE.equalsIgnoreCase(freezeMembershipInput.getSource())
					|| !StringUtils.isEmpty(freezeMembershipInput.getCaseOrigin())) {
				createCaseResponse = sfdcService.createCaseV2(freezeMembershipInput, eClubFacilityID, freezeMembershipInput.getFreezeFees().doubleValue(),true);
			}
			if(Objects.nonNull(freezeMembershipInput.getFreezeFees()))
				output.setFreezeFeeAmount(freezeMembershipInput.getFreezeFees().toString());
			output.setFreezeFeeStartDate(sdf.format(freezeMembershipInput.getStartDate()));
			output.setFreezeStatus("Freeze");
			output.setMemberFirstName(memberAgreementDetail.getFirstName());
			output.setSuspendedAgreementName(memberAgreementDetail.getMembershipClass());
			output.setSuspensionId(Integer.valueOf(freezeReasonId));
			output.setSuspensionReasonName(suspensionName);
			
			Date endDate = freezeMembershipInput.getEndDate();
			Date chargeDate = Calendar.getInstance().getTime();

			//Send Freeze Confirmation Email to PIF members if source is engage.
			if(freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) && (freezeMembershipInput.isPIF() || memberAgreementDetail.getAgreementTermId() == 1)) {
				logger.info("Sending Freeze confirmation email to {} for PIF member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
				boolean emailsent = emailService.sendFreezeConfirmationEmailToPIFMembers(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(), 
						memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), sdf.format(chargeDate),
						freezeMembershipInput.getFreezeFees(), freezeMembershipInput.getDurationMonths());
				createEmailTaskForFreezeConfirmation(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent,true);
				return output;
			}
			
			//Send Freeze Confirmation Email to member if Freeze Reason is Regular
			if(freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {				
				logger.info("Sending Freeze confirmation email to {} for member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
				boolean emailsent = emailService.sendRegularFreezeConfirmationEmailV4(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(), 
						memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), sdf.format(chargeDate),
						freezeMembershipInput.getFreezeFees(), freezeMembershipInput.getDurationMonths(), freezeMembershipInput.isInObligationFlag(),freezeMembershipInput.getSource(),freezeMembershipInput.isHoldBillingInObFlag());
				
				createEmailTaskForFreezeConfirmation(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent, false);
			} else {
				if (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUBAPP) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE)) {
					// medical Request email
					logger.info("Sending medical freeze requested email to {} for member: {}, as freeze reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
					MedicalFreezeEmailInput medicalFreezeEmailInput = prepareMedicalFreezeEmailInput(freezeMembershipInput, memberAgreementDetail, sdf, endDate);
					emailService.sendMedicalAndPregnancyFreezeRequestEmailV2(medicalFreezeEmailInput);
					boolean isEmailDetailsStored = freezeServiceDAO.storeEmailDetailsForMedicalPreganancyFreezeV4(freezeMembershipInput.getMosoId(), memberAgreementDetail.getFirstName(), email, 
							freezeMembershipInput.getStartDate(), freezeMembershipInput.getDurationMonths(), freezeMembershipInput.getFreezeReasonId(), 
							LocalDate.now().plusDays(1).toString(), endDate, memberAgreementDetail.getCountryCode(), freezeMembershipInput.isPilotFreezeMedFee());
					logger.info("Is Email details stored successfully in DB dbo.Freeze_Confirmation_Email for member: {}, {}", freezeMembershipInput.getMosoId(), isEmailDetailsStored);
				}
				if (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE)) {
					//Medical confirmation email
					logger.info("Sending medical freeze confirmation email to {} for member: {}, as freeze reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
					MedicalFreezeEmailInput medicalFreezeEmailInput = prepareMedicalFreezeEmailInput(freezeMembershipInput, memberAgreementDetail, sdf, endDate);
					boolean emailsent = emailService.sendMedicalAndPregnancyFreezeConfirmationEmailV2(medicalFreezeEmailInput);
					createEmailTaskForFreezeConfirmation(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent, false);
				}				
				//Store the Freeze documents in Salesforce for 'Medical' & 'Pregnancy' type only if the Case is created successfully
				if(Objects.nonNull(createCaseResponse) && !StringUtils.isEmpty(createCaseResponse.getId()) && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE)) {
					sfdcService.storeFreezeDocumentinSF(freezeMembershipInput, memberAgreementDetail.getFirstName(), createCaseResponse.getId());
				}
			}
		} else {
			String errorMessage = prepareErrorMessage(suspensionResponse);
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			if (!StringUtils.isBlank(freezeMembershipInput.getSource()) && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK)) {
					exceptionMessage = errorMessageHandler.createExceptionMessage(
							FreezeServiceExceptionEnum.MOSO_ERROR_FRONT_DESK.name(), FreezeServiceExceptionEnum.MOSO_ERROR_FRONT_DESK.getCode(),
							ExceptionMessageEnum.ERROR.toString());
			} else if (!suspensionResponse.getMessage().contains("Message-null, Data-null")) {
					exceptionMessage.setErrorMessage(errorMessage);
					exceptionMessage.setFriendlyMessage(MOSO_ERROR);
					exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_MEMBERSHIP_FAILED.getCode());
					exceptionMessage.setMessageType("Exception");
			} else {
				exceptionMessage = errorMessageHandler.createCustomExceptionMessage(
						errorMessage, MOSO_ERROR, FreezeServiceExceptionEnum.MOSO_ERROR.getCode(),
						ExceptionMessageEnum.ERROR.toString());
			}
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			output.setRequestReceivedSuccessfully(false);

			// Create case in sales force with type exception 
			if (!StringUtils.isBlank(freezeMembershipInput.getSource()) && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE)) {
				sfdcService.createCaseV2(freezeMembershipInput, eClubFacilityID, freezeMembershipInput.getFreezeFees().doubleValue(),false);
			}
		}
		return output;
	}

	private String prepareErrorMessage(SuspensionResponse suspensionResponse) {
		ObjectMapper objectMapper = new ObjectMapper();
		String errorMessage = null;
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		try {
			Map<String, Object> responseMap = objectMapper.readValue(suspensionResponse.getMessage(), new TypeReference<Map<String, Object>>() {});
			errorMessage = (String) responseMap.get("Message");
		} catch (JsonProcessingException e) {
			logger.error("Error in processing JSON", e.getMessage());
		} catch (IOException e) {
			logger.error("I/O error occurred", e.getMessage());
		}

		if (errorMessage == null) {
			errorMessage = MOSO_ERROR;
		}
		return errorMessage;
	}

	private MedicalFreezeEmailInput prepareMedicalFreezeEmailInput(FreezeMembershipInputV2 freezeMembershipInput, MemberAgreementDetail memberAgreementDetail, SimpleDateFormat sdf, Date endDate) {
		MedicalFreezeEmailInput medicalFreezeEmailInput = new MedicalFreezeEmailInput();
		medicalFreezeEmailInput.setMemberId(freezeMembershipInput.getMosoId());
		medicalFreezeEmailInput.setEmailAddress(freezeMembershipInput.getEmailAddress());
		medicalFreezeEmailInput.setMemberFirstName(memberAgreementDetail.getFirstName());
		medicalFreezeEmailInput.setStartDate(sdf.format(freezeMembershipInput.getStartDate()));
		medicalFreezeEmailInput.setEndDate(sdf.format(endDate));
		medicalFreezeEmailInput.setDurationMonths(freezeMembershipInput.getDurationMonths());
		medicalFreezeEmailInput.setPilotFreezeMedFee(freezeMembershipInput.isPilotFreezeMedFee());
		medicalFreezeEmailInput.setFreezeFees(freezeMembershipInput.getFreezeFees());
		medicalFreezeEmailInput.setCountryCode(memberAgreementDetail.getCountryCode());
		return medicalFreezeEmailInput;
	}

	private MedicalFreezeEmailInput prepareMedicalFreezeEmailInput(FreezeMembershipInputV4 freezeMembershipInput, MemberAgreementDetail memberAgreementDetail, SimpleDateFormat sdf, Date endDate) {
		MedicalFreezeEmailInput medicalFreezeEmailInput = new MedicalFreezeEmailInput();
		medicalFreezeEmailInput.setMemberId(freezeMembershipInput.getMosoId());
		medicalFreezeEmailInput.setEmailAddress(freezeMembershipInput.getEmailAddress());
		medicalFreezeEmailInput.setMemberFirstName(memberAgreementDetail.getFirstName());
		medicalFreezeEmailInput.setStartDate(sdf.format(freezeMembershipInput.getStartDate()));
		medicalFreezeEmailInput.setEndDate(sdf.format(endDate));
		medicalFreezeEmailInput.setDurationMonths(freezeMembershipInput.getDurationMonths());
		medicalFreezeEmailInput.setPilotFreezeMedFee(freezeMembershipInput.isPilotFreezeMedFee());
		medicalFreezeEmailInput.setFreezeFees(freezeMembershipInput.getFreezeFees());
		medicalFreezeEmailInput.setCountryCode(memberAgreementDetail.getCountryCode());
		return medicalFreezeEmailInput;
	}

	@Override
	public FreezeMemberResponse freezeMembershipV3(FreezeMembershipInputV3 freezeMembershipInput) {
		// TODO Auto-generated method stub
		logger.debug("Inside freezeExtension Impl");
		FreezeMemberResponse output = new FreezeMemberResponse();
		String suspensionName = freezeMembershipInput.getFreezeReason();
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();
		String accountingCode = "";
		int countryCode = 0;

		Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		Date freezeEndDate = freezeMembershipInput.getFreezeEndDate() == null ? freezeMembershipInput.getClubOpenDate():freezeMembershipInput.getFreezeEndDate();
		logger.info("Freeze End Date ={}",freezeEndDate);
		logger.info("Calculating End Date");
		if(freezeMembershipInput.getFreezeEndDate() == null) {
			logger.info("Calculating End Date");
			cal.setTime(freezeEndDate);
			cal.add(Calendar.MONTH, 2);
			cal.set(Calendar.DAY_OF_MONTH, 1);
		}else {
			cal.setTime(freezeEndDate);
		}

		freezeEndDate = cal.getTime();
		freezeMembershipInput.setFreezeEndDate(freezeEndDate);

		if(freezeMembershipInput.getRequestedDate().after(freezeMembershipInput.getFreezeEndDate())) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage(FreezeServiceExceptionEnum.LINK_HAS_BEEN_EXPIRED.name());
			exceptionMessage.setFriendlyMessage(FreezeServiceExceptionEnum.LINK_HAS_BEEN_EXPIRED.name());
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.LINK_HAS_BEEN_EXPIRED.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		/* Get Member agreement detail from tenant database */
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO
				.getMemberAgreementDetail(freezeMembershipInput.getMosoId());
		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		accountingCode = memberAgreementDetail.getHomeFacilityId();
		String eClubFacilityID = facilityConversion.facilityConversion(accountingCode);

		CheckFreezeEligibilityInput checkFreezeEligibilityInput = freezeHelper
				.setCheckFreezeEligibilityInputV3(freezeMembershipInput, freezeMembershipInput.getFreezeEndDate());
		checkFreezeEligiblityOutput = checkFreezeEligibility(checkFreezeEligibilityInput);

		/* Member not eligible for freeze */
		if (!checkFreezeEligiblityOutput.isEligibleForFreeze()) {
			logger.debug("Not Eligible for Freeze");
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage(checkFreezeEligiblityOutput.getMessages()[0].getErrorMessage());
			exceptionMessage.setFriendlyMessage(checkFreezeEligiblityOutput.getMessages()[0].getFriendlyMessage());
			exceptionMessage.setMessageID(checkFreezeEligiblityOutput.getMessages()[0].getMessageID());
			exceptionMessage.setMessageType(checkFreezeEligiblityOutput.getMessages()[0].getMessageType());
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		if(memberAgreementDetail.getStartDate() != null) {
			if(freezeMembershipInput.getClubOpenDate().before(memberAgreementDetail.getStartDate())) {
				logger.debug("Not eligible for freeze since the freeze start date cannot be before agreement start date");
				ExceptionMessage exceptionMessage = new ExceptionMessage();
				exceptionMessage.setErrorMessage("The FreezeStartDate cannot be before AgreementStartDate.");
				exceptionMessage.setFriendlyMessage("The FreezeStartDate cannot be before AgreementStartDate.");
				exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_STARTDATE_INVALID.getCode());
				exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				return output;
			}
		}
		countryCode = 1;
		// Get Session
		GetSessionOutput sessionOutput = (GetSessionOutput) mosoSessionMediator.getSession(countryCode, accountingCode,
				freezeMembershipInput.getCorrelationId());
		if (sessionOutput == null) {
			logger.error("Fail Auth");
			ExceptionMessage message = new ExceptionMessage();
			message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
			message.setFriendlyMessage("Get Session failed");
			message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			return output;
		}
		HttpEntity<HttpHeaders> entity = freezeHelper.setEntity(sessionOutput);

		if(suspensionName.equalsIgnoreCase("Covid-Utilization")) {
			suspensionName = "Regular (In Ob Exception)";
		}

		logger.debug("Suspension Name {}", suspensionName);

		/* Obtain freeze reason id from tenant */
		String freezeReasonId = freezeServiceDAO.getSuspensionReasonId(suspensionName);

		logger.debug("freezeReasonId " + freezeReasonId);

		/* Freeze reason id is mandatory for freeze */
		if (StringUtils.isBlank(freezeReasonId)) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("Freeze reasonID is null.");
			exceptionMessage.setFriendlyMessage("Freeze reasonID is null.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType("Error");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		SuspensionRequest suspensionRequest = freezeHelper.setSuspensionRequestV3(freezeReasonId, memberAgreementDetail,
				freezeMembershipInput,freezeMembershipInput.getFreezeEndDate());

		SuspensionResponse suspensionResponse = mosoService.freezeAgreement(suspensionRequest, entity, 
				freezeMembershipInput.getCorrelationId(), accountingCode, countryCode);
		logger.info("suspensionResponse :: {}",Objects.nonNull(suspensionResponse) ? suspensionResponse.toString(): "");
		/* If success add note in Moso and create resolved case in sales force */
		if (suspensionResponse != null && StringUtils.isBlank(suspensionResponse.getMessage())) {
			output.setRequestReceivedSuccessfully(true);
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			String content = "Flow Name : Freeze - Freeze Reason : " + freezeMembershipInput.getFreezeReason()
			+ " Freeze Start Date :  "
			+ dateFormat.format(freezeMembershipInput.getClubOpenDate()) + " -  Freeze End Date :  "
			+ dateFormat.format(freezeMembershipInput.getFreezeEndDate());

			/* Setting note request */
			Note note = freezeHelper.setnoteRequest(freezeMembershipInput.getMosoId(), content);
			/* Adding note in Moso */
			mosoService.addNote(sessionOutput, entity, note, freezeMembershipInput.getCorrelationId(), countryCode, accountingCode);

			/* Creating resolved case in Sales force */
			if (!StringUtils.isBlank(freezeMembershipInput.getSource()) && freezeMembershipInput.getSource().equalsIgnoreCase("Web")) {
				double freezeFee = suspensionResponse.getFreezeFeeAmount() != null ? suspensionResponse.getFreezeFeeAmount().doubleValue() : 0.00;
				sfdcService.createCaseV3(freezeMembershipInput, eClubFacilityID, freezeFee,freezeMembershipInput.getFreezeEndDate());
			}
		} else {
			ExceptionMessage message = new ExceptionMessage();
			if (suspensionResponse != null && !StringUtils.isBlank(suspensionResponse.getMessage())) {
				message.setErrorMessage(suspensionResponse.getMessage());
				message.setFriendlyMessage(suspensionResponse.getMessage());
			}else {
				message.setErrorMessage("There is already a Pending suspension for this agreement.");
				message.setFriendlyMessage( "There is already a Pending suspension for this agreement.");
			}
			message.setMessageID(FreezeServiceExceptionEnum.FREEZE_MEMBERSHIP_FAILED.getCode());
			message.setMessageType("Exception");
			output.setMessages(new ExceptionMessage[] { message });
			output.setRequestReceivedSuccessfully(false);
		}

		return output;
	}

	@Override
	public FreezeEmailIOutput sendMemberFreezeEmail(FreezeEmailInput freezeEmailInput, String correlationId) {		
		logger.debug("Freeze memberId {}",freezeEmailInput.getMemberId());
		LocalDate memberSinceDate;
		Map<String, String> urlVariables = new HashMap<>();
		urlVariables.put("categories", "membership,profile");
		Member member = redisApiService.getMemberV2SpecificCategories(freezeEmailInput.getMemberId(), urlVariables);
		if(StringUtils.isEmpty(freezeEmailInput.getCountry())) {
			freezeEmailInput.setCountry(Objects.isNull(member) ? FreezeMembershipConstants.DEFAULT_COUNTRY_US : member.getMembership().getCountryCode().toUpperCase());
		}else {
			freezeEmailInput.setCountry(freezeEmailInput.getCountry().toUpperCase());			
		}	
		if(StringUtils.isEmpty(freezeEmailInput.getFacilityId())) {
			freezeEmailInput.setFacilityId(member.getMembership().getHomeFacilityId());
		}
		GetFacilityResponse facilityResponse = facilityService.getFacilityByIdV2(freezeEmailInput.getFacilityId());
		FreezeEmailIOutput output = new FreezeEmailIOutput();
		freezeEmailInput.setCountry(Objects.isNull(member.getMembership().getCountryCode().toUpperCase()) ? FreezeMembershipConstants.DEFAULT_COUNTRY_US : member.getMembership().getCountryCode().toUpperCase());
		freezeEmailInput.setMemberName(member.getProfile().getFirstName() +" "+ member.getProfile().getLastName());
		//freezeEmailInput.setFacilityId(member.getMembership().getHomeFacilityId());
		freezeEmailInput.setMemberSince(member.getMembership().getMemberSince().toString());
		setMemberSinceDetails(member, freezeEmailInput);
		/*Create and get the token*/
		String token = getTokenByData(freezeEmailInput, correlationId,"clubapp",facilityResponse);

		// skip email if source is not null and is not web_
		if(!freezeEmailInput.isSkipEmail()) {
			boolean value = emailService.sendMemberFreezeEmail(freezeEmailInput, token, member.getProfile().getFirstName(),"clubapp");
			if(value) {
				output.setSuccess(true);
				output.setMessage("Send freeze link to "+freezeEmailInput.getEmail()+ " is successful");
				//task creation on enage.
				sfdcService.createEmailTaskForSendFreezeEmailLink(freezeEmailInput,"clubapp",member.getProfile().getFirstName());

			}else {
				output.setSuccess(false);
				output.setMessage("Send freeze link to "+freezeEmailInput.getEmail()+ " is failed");
			}
		}
		output.setToken(token);
		return output;
	}

	private void setMemberSinceDetails(Member member, FreezeEmailInput freezeEmailInput) {
		if (member.getMembership().getMemberSince() != null) {
			LocalDate freezeStartDate = LocalDate.now();
			Calendar memberSince = member.getMembership().getMemberSince();
			LocalDate memberSinceDate = LocalDateTime.ofInstant(memberSince.toInstant(), memberSince.getTimeZone().toZoneId()).toLocalDate();
			freezeEmailInput.setMemberSince(memberSinceDate.toString());
			long daysDiff = ChronoUnit.DAYS.between(memberSinceDate, freezeStartDate);
			if (daysDiff < 365)
				freezeEmailInput.setInObligation(true);
			LocalDate contractChangeDate = LocalDate.parse(CONTRACT_DATE);
			if (memberSinceDate.isAfter(contractChangeDate) || memberSinceDate.isEqual(contractChangeDate)) {
				freezeEmailInput.setJoinedAfterContractChange(true);
			}
		}
	}

	private String getTokenByData(FreezeEmailInput freezeEmailInput, String correlationId, String source, GetFacilityResponse facilityResponse) {	
		String timezone;
		if(Objects.isNull(facilityResponse) || Objects.isNull(facilityResponse.getResult().getTimeZone())) {
			timezone=EST_TIMZONE_REGION;
		}else {
			timezone = Optional.ofNullable(DateUtils.TIMEZONE_MAPPINGS.get(facilityResponse.getResult().getTimeZone())).orElse(EST_TIMZONE_REGION);			
		}
		Calendar calendar = Calendar.getInstance();
		sourceFormat.setTimeZone(TimeZone.getTimeZone(timezone));
		String clubLocalTime = sourceFormat.format(calendar.getTime());
		logger.info("Requested clubId: {}, timezone: {}, and club_localtime is {}",freezeEmailInput.getFacilityId(), timezone, clubLocalTime);
		if(freezeEmailInput.getStartDate() == null) {
            Date date = new Date();
            freezeEmailInput.setStartDate(date);    
		}

		TokenRequest request = new TokenRequest();
		if (freezeEmailInput.getSource() != null && freezeEmailInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_WEB)) {
			source = FreezeMembershipConstants.SOURCE_WEB;
			request.setRequestor("Web");
			request.setTokenType("web-api-gatekeeper-freeze");
		} else if(source.equalsIgnoreCase("clubapp")) {
			request.setRequestor("clubapp");
			request.setTokenType("clubapp-api-gatekeeper-freeze");
		} else {
			request.setRequestor("engage");
			request.setTokenType("engage-api-gatekeeper-freeze");
		}
		request.setIssuedDate(clubLocalTime);
		TokenData tokenData = new TokenData();
		tokenData.setMemberId(freezeEmailInput.getMemberId());
		tokenData.setFacilityId(freezeEmailInput.getFacilityId());
		tokenData.setFreezeReason(freezeEmailInput.getFreezeReason());
		tokenData.setEmail(freezeEmailInput.getEmail());
		tokenData.setPhone(freezeEmailInput.getPhone());
		tokenData.setMemberName(freezeEmailInput.getMemberName());
		tokenData.setCountry(freezeEmailInput.getCountry());
		tokenData.setInObligation(freezeEmailInput.isInObligation());	
		tokenData.setStartDate(this.formattedDate(freezeEmailInput));
		tokenData.setMemberSince(freezeEmailInput.getMemberSince());
		tokenData.setEligibleForFreeze(freezeEmailInput.isEligibleForFreeze());
		tokenData.setJoinedAfterContractChange(freezeEmailInput.isJoinedAfterContractChange());
		Map<String, String> pilotKeys = tokenNoFreezeTemplate.getRedisHashOpsData("pilot.web.cancellation");
		if(Objects.nonNull(pilotKeys)) {
			String facilityAppointments = pilotKeys.get("freeze");
			List<String> noFreezeFacilitiesList = Arrays.asList(facilityAppointments.split(","));
			if (Objects.nonNull(noFreezeFacilitiesList) && noFreezeFacilitiesList.contains(freezeEmailInput.getFacilityId()))
				tokenData.setCanFreeze(false);
			else
				tokenData.setCanFreeze(true);
			
			String facilityChangeDuration = pilotKeys.get("duration");
			List<String> facilityChangeDurations = Arrays.asList(facilityChangeDuration.split(","));
			if (Objects.nonNull(facilityChangeDurations) && 
					facilityChangeDurations.contains(freezeEmailInput.getFacilityId()) &&
					!freezeEmailInput.getFreezeReason().contains("Medical") && 
					!freezeEmailInput.getFreezeReason().contains("Pregnancy"))
				tokenData.setFreezeDurationInMonths(1);
			else
				tokenData.setFreezeDurationInMonths(3);
		}
		else {
			tokenData.setCanFreeze(true);
			tokenData.setFreezeDurationInMonths(3);
		}

		if (!freezeEmailInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_MEDICAL) &&
				!freezeEmailInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_PREGNANCY)) {

			List<String> matchingKeys = new ArrayList<>();
			List<String> allOptionsMatchingKeys = new ArrayList<>();
			String facilityId = freezeEmailInput.getFacilityId();
			boolean isThirtyDays = false;
			boolean isFortyFiveDays = false;

			// Retrieve contract change dates once at the top
			String reducedFreezeContractDate30Str = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_FREEZE_REDUCED_CONTRACT_CHANGE_DATE_30);
			String reducedFreezeContractDate45Str = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_FREEZE_REDUCED_CONTRACT_CHANGE_DATE_45);
			
			if (source.equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUBAPP)) {
				Map<String, String> clubAppKeysMap = tokenNoFreezeTemplate.getRedisHashOpsData(FreezeMembershipConstants.PILOT_CLUB_APP_FACILITIES);
				if(Objects.nonNull(clubAppKeysMap) && !ObjectUtils.isEmpty(clubAppKeysMap)) {
					List<String> facilityDays = getClubAppFacilityDays(clubAppKeysMap, facilityId);
					if (StringUtils.isNotBlank(freezeEmailInput.getMemberSince())) {
						LocalDate memberSinceDate = LocalDate.parse(freezeEmailInput.getMemberSince());
						
						// Check 30 days logic
						if (StringUtils.isNotBlank(reducedFreezeContractDate30Str)) {
							LocalDate reducedFreezeContractDate30 = LocalDate.parse(reducedFreezeContractDate30Str);
							if (memberSinceDate.isAfter(reducedFreezeContractDate30) || memberSinceDate.isEqual(reducedFreezeContractDate30)) {
								isThirtyDays = isClubAppThirtyDaysPerYear(clubAppKeysMap, facilityId);
							}
						}
						
						// Check 45 days logic
						if (StringUtils.isNotBlank(reducedFreezeContractDate45Str)) {
							LocalDate reducedFreezeContractDate45 = LocalDate.parse(reducedFreezeContractDate45Str);
							if (memberSinceDate.isAfter(reducedFreezeContractDate45) || memberSinceDate.isEqual(reducedFreezeContractDate45)) {
								isFortyFiveDays = isClubAppFortyFiveDaysPerYear(clubAppKeysMap, facilityId);
							}
						}
					}

					if (!facilityDays.isEmpty()) {
						for (String days : facilityDays) {
							allOptionsMatchingKeys.add(days);// this is set in case we wanted to show all available options
							if (freezeEmailInput.isJoinedAfterContractChange() && days.equals("90")) {
								continue;
							}
							if ((isThirtyDays && Integer.parseInt(days) <= 30) || (isFortyFiveDays && Integer.parseInt(days) <= 45)) {
								matchingKeys.add(days);
							} else if (!isThirtyDays && !isFortyFiveDays) {
								matchingKeys.add(days);
							}
						}
					}
				}
			} else if (source.equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE) || freezeEmailInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_WEB)) {
				Map<String, String> pilotFreezeKeys = tokenNoFreezeTemplate.getRedisHashOpsData(FreezeMembershipConstants.PILOT_FREEZE_DURATION_DAYS);
				if(Objects.nonNull(pilotFreezeKeys) && !ObjectUtils.isEmpty(pilotFreezeKeys)) {
					if (StringUtils.isNotBlank(freezeEmailInput.getMemberSince())) {
						LocalDate memberSinceDate = LocalDate.parse(freezeEmailInput.getMemberSince());

						// Check 30 days logic
						if (isThirtyDays && StringUtils.isNotBlank(reducedFreezeContractDate30Str)) {
							LocalDate reducedFreezeContractDate30 = LocalDate.parse(reducedFreezeContractDate30Str);
							if (memberSinceDate.isAfter(reducedFreezeContractDate30) || memberSinceDate.isEqual(reducedFreezeContractDate30)) {
								// Check facility eligibility first
								isThirtyDays = checkThirtyDaysPerYear(facilityId);
								tokenData.setPilotFreezeDaysPerYear(true);
							}
						}
						
						// Check 45 days logic
						if (isFortyFiveDays && StringUtils.isNotBlank(reducedFreezeContractDate45Str)) {
							LocalDate reducedFreezeContractDate45 = LocalDate.parse(reducedFreezeContractDate45Str);
							if (memberSinceDate.isAfter(reducedFreezeContractDate45) || memberSinceDate.isEqual(reducedFreezeContractDate45)) {
								isFortyFiveDays = checkFortyFiveDaysPerYear(facilityId);
								tokenData.setPilotFreezeDaysPerYear(true);
							}
						}
					}

					for (Map.Entry<String, String> entry : pilotFreezeKeys.entrySet()) {
						if (entry.getValue().contains(facilityId)) {
							String keyValue = entry.getKey().replace("_days", "");
			
							allOptionsMatchingKeys.add(keyValue);// this is set in case we wanted to show all available options
							if (freezeEmailInput.isJoinedAfterContractChange() && keyValue.equals("90")) {
								continue;
							}

							// check for 30 or 45 days
							if ((isThirtyDays && Integer.parseInt(keyValue) <= 30) || (isFortyFiveDays && Integer.parseInt(keyValue) <= 45)) {
								matchingKeys.add(keyValue);
							} else if (!isThirtyDays && !isFortyFiveDays) {
								matchingKeys.add(keyValue);
							}
						}
					}
				}

			}

			Collections.sort(matchingKeys);
			Collections.sort(allOptionsMatchingKeys);

			if (!matchingKeys.isEmpty()) {
				String Days = String.join(",", matchingKeys);
				tokenData.setFreezeDurationInDays(Days);
				tokenData.setFreezeDurationPilot(true);

			} else {
				tokenData.setFreezeDurationInDays(null);
				tokenData.setFreezeDurationPilot(false);
			}

			if (!allOptionsMatchingKeys.isEmpty()) {
				String allOptionsDays = String.join(",", allOptionsMatchingKeys);
				tokenData.setFreezeDurationInDaysAllOptions(allOptionsDays);
			} else {
				tokenData.setFreezeDurationInDaysAllOptions(null);
			}
		}

		// set freeze monthly fee rate
		if (freezeEmailInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_REGULAR)){
			setMonthlyFeeRate(freezeEmailInput.getFacilityId(), tokenData);
		}

		if (freezeEmailInput.isSkipEmail()) {
			tokenData.setCancelFlow(true);
		}

		// check if university freeze fee flag is on
		if (freezeEmailInput.isSkipEmail() && freezeEmailInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_REGULAR)) {
			String universityFreezeFacilities = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_UNIVERSITY_FREEZE_FEE);
			if (StringUtils.isNotBlank(universityFreezeFacilities)) {
				List<String> universityFreezeFacilitiesList = Arrays.asList(universityFreezeFacilities.split(","));
				if (universityFreezeFacilitiesList.contains(freezeEmailInput.getFacilityId())) {
					tokenData.setUniversityMember(freezeHelper.isUniversityMember(freezeEmailInput.getMemberId()));
					if (tokenData.isUniversityMember()) {
						tokenData.setInObligation(false);
					}
				}
			}
		}

		if (!freezeEmailInput.isSkipEmail() && (freezeEmailInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_MEDICAL) ||
				freezeEmailInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_PREGNANCY))) {
			tokenData.setMonthlyFeeRate(0.0);
			String medicalFreezeFeeKeys = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_MEDICAL_FREEZE_FEE);
			if (StringUtils.isNotBlank(medicalFreezeFeeKeys)) {
				List<String> medicalFreezeFeeFacilities = Arrays.asList(medicalFreezeFeeKeys.split(","));
				if (medicalFreezeFeeFacilities.contains(freezeEmailInput.getFacilityId())) {
					tokenData.setPilotFreezeMedFee(true);
					tokenData.setMonthlyFeeRate((double) FreezeMembershipConstants.MED_PREGNANCY_FEE);
				}
			}
		}

		tokenData.setSource(source);
		tokenData.setCaseOrigin(freezeEmailInput.getCaseOrigin());
		tokenData.setCaseOrigin(freezeEmailInput.getCaseOrigin());
		request.setTokenData(tokenData);
		return freezeHelper.createToken(request, correlationId, clubLocalTime);
	}

	private boolean isClubAppThirtyDaysPerYear(Map<String, String> clubAppKeysMap, String facilityId) {
		String clubAppPilotThirtyDaysPerYear = clubAppKeysMap.get(FreezeMembershipConstants.CLUBAPP_PILOT_FREEZE_30_DAYS_PER_YEAR);
		List<String> thirtyDaysPerYearFacilities = clubAppPilotThirtyDaysPerYear != null && !clubAppPilotThirtyDaysPerYear.isEmpty() ? Arrays.asList(clubAppPilotThirtyDaysPerYear.split(",")) : null;
		return !CollectionUtils.isEmpty(thirtyDaysPerYearFacilities) && thirtyDaysPerYearFacilities.contains(facilityId);
	}

	private boolean isClubAppFortyFiveDaysPerYear(Map<String, String> clubAppKeysMap, String facilityId) {
		String clubAppPilotFortyFiveDaysPerYear = clubAppKeysMap.get(FreezeMembershipConstants.CLUBAPP_PILOT_FREEZE_45_DAYS_PER_YEAR);
		List<String> fortyFiveDaysPerYearFacilities = clubAppPilotFortyFiveDaysPerYear != null && !clubAppPilotFortyFiveDaysPerYear.isEmpty() ? Arrays.asList(clubAppPilotFortyFiveDaysPerYear.split(",")) : null;
		return !CollectionUtils.isEmpty(fortyFiveDaysPerYearFacilities) && fortyFiveDaysPerYearFacilities.contains(facilityId);
	}

	private List<String> getClubAppFacilityDays(Map<String, String> clubAppKeysMap, String facilityId) {
		List<String> facilityDays = new ArrayList<>();

		String clubAppPilotFifteenDays = clubAppKeysMap.get(FreezeMembershipConstants.CLUBAPP_PILOT_FREEZE_15_DAYS);
		String clubAppPilotThirtyDays = clubAppKeysMap.get(FreezeMembershipConstants.CLUBAPP_PILOT_FREEZE_30_DAYS);
		String clubAppPilotFortyFiveDays = clubAppKeysMap.get(FreezeMembershipConstants.CLUBAPP_PILOT_FREEZE_45_DAYS);
		String clubAppPilotSixtyDays = clubAppKeysMap.get(FreezeMembershipConstants.CLUBAPP_PILOT_FREEZE_60_DAYS);
		String clubAppPilotNinetyDays = clubAppKeysMap.get(FreezeMembershipConstants.CLUBAPP_PILOT_FREEZE_90_DAYS);

		List<String> fifteenDaysFacilities = clubAppPilotFifteenDays != null && !clubAppPilotFifteenDays.isEmpty() ? Arrays.asList(clubAppPilotFifteenDays.split(",")) : null;
		List<String> thirtyDaysFacilities = clubAppPilotThirtyDays != null && !clubAppPilotThirtyDays.isEmpty() ? Arrays.asList(clubAppPilotThirtyDays.split(",")) : null;
		List<String> fortyFiveDaysFacilities = clubAppPilotFortyFiveDays != null && !clubAppPilotFortyFiveDays.isEmpty() ? Arrays.asList(clubAppPilotFortyFiveDays.split(",")) : null;
		List<String> sixtyDaysFacilities = clubAppPilotSixtyDays != null && !clubAppPilotSixtyDays.isEmpty() ? Arrays.asList(clubAppPilotSixtyDays.split(",")) : null;
		List<String> ninetyDaysFacilities = clubAppPilotNinetyDays != null && !clubAppPilotNinetyDays.isEmpty() ? Arrays.asList(clubAppPilotNinetyDays.split(",")) : null;

		if (fifteenDaysFacilities != null && fifteenDaysFacilities.contains(facilityId)) {
			facilityDays.add("15");
		}
		if (thirtyDaysFacilities != null && thirtyDaysFacilities.contains(facilityId)) {
			facilityDays.add("30");
		}
		if (fortyFiveDaysFacilities != null && fortyFiveDaysFacilities.contains(facilityId)) {
			facilityDays.add("45");
		}
		if (sixtyDaysFacilities != null && sixtyDaysFacilities.contains(facilityId)) {
			facilityDays.add("60");
		}
		if (ninetyDaysFacilities != null && ninetyDaysFacilities.contains(facilityId)) {
			facilityDays.add("90");
		}
		return facilityDays;
	}

	private void setMonthlyFeeRate(String facilityId, TokenData tokenData) {
		Map<String, String> pilotFreezeFeeKeys = tokenNoFreezeTemplate.getRedisHashOpsData(FreezeMembershipConstants.PILOT_MONTHLY_FREEZE_FEE);
		if (Objects.nonNull(pilotFreezeFeeKeys) && !ObjectUtils.isEmpty(pilotFreezeFeeKeys)) {
			for (Map.Entry<String, String> entry : pilotFreezeFeeKeys.entrySet()) {
				if (entry.getValue().contains(facilityId)) {
					tokenData.setMonthlyFeeRate(Double.parseDouble(entry.getKey()));
					break;
				}
			}

			// set default 50 if no facility specific freeze fee found
			if (tokenData.getMonthlyFeeRate() == null) {
				tokenData.setMonthlyFeeRate(Double.valueOf(FreezeMembershipConstants.FREEZE_FEE_50));
			}
		} else {
			tokenData.setMonthlyFeeRate(Double.valueOf(FreezeMembershipConstants.FREEZE_FEE_50));
		}
	}
	
	private String formattedDate(FreezeEmailInput freezeEmailInput) {		
		String formattedDate = targetFormat.format(freezeEmailInput.getStartDate());
		return formattedDate;
	}	

	@Override
	public MemberFreezeCaseOutput createMemberFreezeCase(MemberFreezeCase memberFreezeCase) {
		MemberFreezeCaseOutput output = new MemberFreezeCaseOutput();
		String caseId=salesforceUtil.createMemberFreezeCase(memberFreezeCase);
		if(StringUtils.isEmpty(caseId)) {
			logger.error("Error occured at Submitting Member feedback in Salesforce");
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("error while creating the member freeze case in SF");
			exceptionMessage.setFriendlyMessage("error while creating the member freeze case in SF");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.NO_CODE.getCode());
			exceptionMessage.setMessageType("ERROR");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		output.setCaseId(caseId);
		if (caseId != null && (memberFreezeCase.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_MEDICAL) ||
				memberFreezeCase.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_PREGNANCY))) {
			FreezeMembershipInputV2 input = new FreezeMembershipInputV2();
			input.setMosoId(memberFreezeCase.getMosoId());
			input.setFacilityId(memberFreezeCase.getClubId());
			input.setFreezeDocuments(memberFreezeCase.getFreezeDocuments());
			sfdcService.storeFreezeDocumentinSF(input, memberFreezeCase.getMemberFirstName(), caseId);
		}
		return output;
	}

	private String getSuspensionName(FreezeMembershipInputV2 freezeMembershipInput, MemberAgreementDetail memberAgreementDetail) {
		String suspensionName = freezeMembershipInput.getFreezeReasonId();		
		if (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE)) {			  
			if (freezeMembershipInput.isInObligationFlag() && freezeMembershipInput.isHoldBillingInObFlag() && freezeMembershipInput.isWaveFreezeFee()) {
				suspensionName = FreezeMembershipConstants.REGULAR_OB_EXCEPTION;
				return suspensionName;
			}
			if (freezeMembershipInput.isHoldBillingInObFlag() == false && freezeMembershipInput.isWaveFreezeFee()) {
				suspensionName = FreezeMembershipConstants.REGULAR_FEE_WAIVED;
				return suspensionName;
			}
			if (freezeMembershipInput.isInObligationFlag() && freezeMembershipInput.isHoldBillingInObFlag() && freezeMembershipInput.isWaveFreezeFee() == false) {
				//Regular (Exception) 1 Month - New
				suspensionName = freezeMembershipInput.getFreezeReasonId()+FreezeMembershipConstants.EXCEPTION+" "+freezeMembershipInput.getDurationMonths()+" Month";
				return suspensionName;
			}
			if(memberAgreementDetail.getAgreementTermId() == 1 && freezeMembershipInput.isPIF()) {
				suspensionName = FreezeMembershipConstants.REGULAR_OB_EXCEPTION;
				return suspensionName;
			}
		} 
		if (freezeMembershipInput.isInObligationFlag()) {
			suspensionName = freezeMembershipInput.getFreezeReasonId()+" "+freezeMembershipInput.getDurationMonths()+" Month";
			return suspensionName;
		}
		if (freezeMembershipInput.isInObligationFlag() == false) {
			suspensionName = freezeMembershipInput.getFreezeReasonId()+" "+freezeMembershipInput.getDurationMonths()+" Month";
			return suspensionName;
		}
		return suspensionName;
	}
	
	private String getSuspensionNameV1(FreezeMembershipInputV4 freezeMembershipInput, MemberAgreementDetail memberAgreementDetail) {
		String suspensionName = freezeMembershipInput.getFreezeReasonId();

		logger.debug("Suspension name based on pilot monthly freeze fee");
		Map<String, String> pilotFreezeFeeKeys = tokenNoFreezeTemplate.getRedisHashOpsData("pilot.monthly.freeze.fee");
		if (Objects.nonNull(pilotFreezeFeeKeys) && !ObjectUtils.isEmpty(pilotFreezeFeeKeys) && !freezeMembershipInput.isWaveFreezeFee()) {
			for (Map.Entry<String, String> entry : pilotFreezeFeeKeys.entrySet()) {
				if (entry.getValue()!= null && !entry.getValue().isEmpty() && entry.getValue().contains(freezeMembershipInput.getFacilityId())) {
					BigDecimal monthlyFreezeFee = BigDecimal.valueOf(Long.parseLong(entry.getKey()));
					// if freeze is 75 and then suspension reason starts with Reg B and if it is 100 then it starts with Reg C
					if (monthlyFreezeFee.compareTo(BigDecimal.valueOf(FreezeMembershipConstants.FREEZE_FEE_75)) == 0) {
						if (freezeMembershipInput.isInObligationFlag() && !freezeMembershipInput.isHoldBillingInObFlag())
							suspensionName = FreezeMembershipConstants.REGULAR_B + " " + freezeMembershipInput.getDurationDays();
						else
							suspensionName = FreezeMembershipConstants.REGULAR_B + " " + freezeMembershipInput.getDurationDays() + " " + FreezeMembershipConstants.FREEZE_FEE_EXCEPTION;
						return suspensionName;
					} else if (monthlyFreezeFee.compareTo(BigDecimal.valueOf(FreezeMembershipConstants.FREEZE_FEE_100)) == 0) {
						if (freezeMembershipInput.isInObligationFlag() && !freezeMembershipInput.isHoldBillingInObFlag())
							suspensionName = FreezeMembershipConstants.REGULAR_C + " " + freezeMembershipInput.getDurationDays();
						else
							suspensionName = FreezeMembershipConstants.REGULAR_C + " " + freezeMembershipInput.getDurationDays() + " " + FreezeMembershipConstants.FREEZE_FEE_EXCEPTION;
						return suspensionName;
					}
				}
			}
		}

		if (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE)) {
			if (freezeMembershipInput.isInObligationFlag()) {
				if (freezeMembershipInput.isHoldBillingInObFlag() && freezeMembershipInput.isWaveFreezeFee()) {
					suspensionName = FreezeMembershipConstants.REGULAR_FEE_WAIVED_EXCEPTION;
					return suspensionName;
				}

				if (!freezeMembershipInput.isHoldBillingInObFlag() && freezeMembershipInput.isWaveFreezeFee()) {
					suspensionName = FreezeMembershipConstants.REGULAR_FEE_WAIVED;
					return suspensionName;
				}

				if (freezeMembershipInput.isHoldBillingInObFlag() && !freezeMembershipInput.isWaveFreezeFee()) {
					// Check if the freeze duration is in months or days
					if (freezeMembershipInput.getDurationDays() > 0) {
						// Regular (Exception) 1 Month - New
						int durationInMonths = getMonthDuration(freezeMembershipInput.getDurationDays());
						if (durationInMonths > 0) {
							suspensionName = freezeMembershipInput.getFreezeReasonId() + FreezeMembershipConstants.EXCEPTION + " " + durationInMonths + " Month";
						} else {
							suspensionName = freezeMembershipInput.getFreezeReasonId() + FreezeMembershipConstants.EXCEPTION + " " + freezeMembershipInput.getDurationDays() + " Days";
						}
					} else if (freezeMembershipInput.getDurationMonths() > 0) {
						// Regular (Exception) X Days - New (when duration is in days)
						suspensionName = freezeMembershipInput.getFreezeReasonId() + FreezeMembershipConstants.EXCEPTION + " " + freezeMembershipInput.getDurationMonths() + " Month";
					}
					return suspensionName;
				}
				if (memberAgreementDetail.getAgreementTermId() == 1 && freezeMembershipInput.isPIF()) {
					suspensionName = FreezeMembershipConstants.REGULAR_OB_EXCEPTION;
					return suspensionName;
				}
			} else if (freezeMembershipInput.isWaveFreezeFee()) {
				suspensionName = FreezeMembershipConstants.REGULAR_FEE_WAIVED_EXCEPTION;
				return suspensionName;
			}
		} 
		if (freezeMembershipInput.isInObligationFlag()) {
			if (freezeMembershipInput.getDurationDays() > 0) {
				int durationInMonths = getMonthDuration(freezeMembershipInput.getDurationDays());
				if (durationInMonths > 0) {
					suspensionName = freezeMembershipInput.getFreezeReasonId() + " " + durationInMonths + " Month";
				} else {
					suspensionName = freezeMembershipInput.getDurationDays() + " Days" + " " + freezeMembershipInput.getFreezeReasonId();
				}
			} else if (freezeMembershipInput.getDurationMonths() > 0) {
				// Regular (Exception) X Days - New (when duration is in days)
				if (freezeMembershipInput.isCancelFlow())
					suspensionName = freezeMembershipInput.getFreezeReasonId() + FreezeMembershipConstants.EXCEPTION + " " + freezeMembershipInput.getDurationMonths() + " Month";
				else
					suspensionName = freezeMembershipInput.getFreezeReasonId() + " " + freezeMembershipInput.getDurationMonths() + " Month";
			}
			return suspensionName;
		}
		if (freezeMembershipInput.isInObligationFlag() == false) {
			if (freezeMembershipInput.getDurationDays() > 0) {
				// Regular (Exception) 1 Month - New
				//if member since is before contract date() its a regular else Exception
				if(StringUtils.isNotEmpty(freezeMembershipInput.getMemberSince())
						&& Objects.nonNull(freezeMembershipInput.getStartDate())) {
					String membersince = freezeMembershipInput.getMemberSince();
					LocalDateTime membersinceDateTime = LocalDateTime.parse(membersince+ "T00:00:00", DateTimeFormatter.ISO_LOCAL_DATE_TIME);
					LocalDateTime localDateTime = freezeMembershipInput.getStartDate().toInstant()
							 .atZone(ZoneId.systemDefault())
							 .toLocalDateTime();
					if(membersinceDateTime.isBefore(localDateTime)) {
						int durationInMonths = getMonthDuration(freezeMembershipInput.getDurationDays());
						if (durationInMonths > 0) {
							suspensionName = freezeMembershipInput.getFreezeReasonId() + FreezeMembershipConstants.EXCEPTION + " " + durationInMonths + " Month";
						} else {
							suspensionName = freezeMembershipInput.getDurationDays() + " Days" + " " + FreezeMembershipConstants.IN_OB_EXCEPTION ;
						}
					}
				}
				else {
					int durationInMonths = getMonthDuration(freezeMembershipInput.getDurationDays());
					if (durationInMonths > 0) {
						suspensionName = freezeMembershipInput.getFreezeReasonId() + FreezeMembershipConstants.EXCEPTION + " " + durationInMonths + " Month";
					} else {
						suspensionName =   freezeMembershipInput.getDurationDays() + " Days" + " " + FreezeMembershipConstants.IN_OB_EXCEPTION ;
					}
				}
				
			} else if (freezeMembershipInput.getDurationMonths() > 0) {
				// Regular (Exception) X Days - New (when duration is in days)
				if (freezeMembershipInput.isCancelFlow())
					suspensionName = freezeMembershipInput.getFreezeReasonId() + FreezeMembershipConstants.EXCEPTION + " " + freezeMembershipInput.getDurationMonths() + " Month";
				else
					suspensionName = freezeMembershipInput.getFreezeReasonId() + " " + freezeMembershipInput.getDurationMonths() + " Month";
			}
			return suspensionName;
		}
		return suspensionName;
	}

	private int getMonthDuration(int durationDays) {
		if (durationDays == 30 || durationDays == 60 || durationDays == 90) {
			return durationDays/30;
		}
		return 0;
	}

	@Override
	public EngageFreezeEmailIOutput sendFreezeEmailLink(FreezeEmailInput freezeEmailInput, String correlationId) {
		logger.debug("Freeze memberId {}",freezeEmailInput.getMemberId());
		EngageFreezeEmailIOutput output = new EngageFreezeEmailIOutput();
		LocalDate memberSinceDate;
		Map<String, String> urlVariables = new HashMap<>();
		urlVariables.put("categories", "membership,profile");
		Member member = redisApiService.getMemberV2SpecificCategories(freezeEmailInput.getMemberId(), urlVariables);
		if (Objects.isNull(member)) {
			ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.name(), FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.getCode(),
					ExceptionMessageEnum.ERROR.name());
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;			
		}
		GetFacilityResponse facilityResponse = facilityService.getFacilityByIdV2(member.getMembership().getHomeFacilityId());
		freezeEmailInput.setCountry(Objects.isNull(member.getMembership().getCountryCode().toUpperCase()) ? FreezeMembershipConstants.DEFAULT_COUNTRY_US : member.getMembership().getCountryCode().toUpperCase());
		freezeEmailInput.setMemberName(member.getProfile().getFirstName() +" "+ member.getProfile().getLastName());
		freezeEmailInput.setFacilityId(member.getMembership().getHomeFacilityId());		
		setMemberSinceDetails(member, freezeEmailInput);

		/*Create and get the token*/
		String token = getTokenByData(freezeEmailInput, correlationId,"concierge", facilityResponse);

		// skip email if skipEmail flag is true
		if (!freezeEmailInput.isSkipEmail())
			sendFreezeEmail(freezeEmailInput, token, member, output);
		output.setToken(token);
		return output;
	}

	private void sendFreezeEmail(FreezeEmailInput freezeEmailInput, String token, Member member, EngageFreezeEmailIOutput output) {
		boolean value = emailService.sendMemberFreezeEmail(freezeEmailInput, token, freezeEmailInput.getMemberName(),"engage");
		if (value) {
			output.setSuccess(true);
			output.setMessage("Send freeze link to " + freezeEmailInput.getEmail() + " is successful");
			sfdcService.createEmailTaskForSendFreezeEmailLink(freezeEmailInput, "engage", member.getProfile().getFirstName());
		} else {
			output.setSuccess(false);
			output.setMessage("Send freeze link to " + freezeEmailInput.getEmail() + " is failed");
		}
	}
	
	private void createEmailTaskForFreezeConfirmation(CreateCaseResponse createCaseResponse,
			FreezeMembershipInputV2 freezeMembershipInput, MemberAgreementDetail memberAgreementDetail, boolean emailsent, boolean isPIF) {
		SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
		Date endDate = freezeMembershipInput.getEndDate();
		sfdcService.createEmailTaskForFreezeConfirmation(freezeMembershipInput.getMosoId(), memberAgreementDetail.getFirstName(), 
				memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), 
				freezeMembershipInput.getFreezeFees(), freezeMembershipInput.getDurationMonths(),freezeMembershipInput.isInObligationFlag(),
				createCaseResponse, emailsent,freezeMembershipInput.getSource(),freezeMembershipInput.isHoldBillingInObFlag(),isPIF,freezeMembershipInput.getFreezeReasonId());
	}
	
	private void createEmailTaskForFreezeConfirmationV1(CreateCaseResponse createCaseResponse,
			FreezeMembershipInputV4 freezeMembershipInput, MemberAgreementDetail memberAgreementDetail, boolean emailsent, boolean isPIF) {
		SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
		Date endDate = freezeMembershipInput.getEndDate();
		sfdcService.createEmailTaskForFreezeConfirmationV1(freezeMembershipInput.getMosoId(), memberAgreementDetail.getFirstName(),
				memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), 
				freezeMembershipInput.getFreezeFees(), freezeMembershipInput.getDurationDays(),freezeMembershipInput.isInObligationFlag(),
				createCaseResponse, emailsent,freezeMembershipInput.getSource(),freezeMembershipInput.isHoldBillingInObFlag(),isPIF,freezeMembershipInput.getFreezeReasonId());
	}

	@Override
	public FreezeExtensionOutput freezeExtensionV5(FreezeExtensionInputV3 freezeExtensionInput, MemberAgreementDetail memberAgreementDetail, 
			FreezeMembershipInputV4 freezeMembershipInput,String email) {
		logger.debug("Inside freezeExtension Impl");
		FreezeExtensionOutput output = new FreezeExtensionOutput();

//		/* Getting Member agreement detail from tenant database */
//		logger.debug("Calling DAO ");
//		if (memberAgreementDetail ==null) {
//			memberAgreementDetail = freezeServiceDAO.getMemberAgreementDetail(freezeExtensionInput.getMosoId());
//		}
//		if (memberAgreementDetail == null) {
//			ExceptionMessage exceptionMessage = new ExceptionMessage();
//			exceptionMessage.setErrorMessage("MemberAgreementDetail should not be null.");
//			exceptionMessage.setFriendlyMessage("MemberAgreementDetail should not be null.");
//			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
//			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
//			output.setMessages(new ExceptionMessage[] { exceptionMessage });
//			return output;
//		}

		/* freeze extension is allowed only on active/Pending freeze agreements */
		if (memberAgreementDetail.getFreezeStatus().equalsIgnoreCase(FreezeMembershipConstants.FREEZE_ACTIVE_STATUS)
				|| memberAgreementDetail.getFreezeStatus().equalsIgnoreCase(FreezeMembershipConstants.FREEZE_PENDING_START)) {
			String freezeId = memberAgreementDetail.getFreezeId();
			logger.debug("FreezeId {} ", freezeId);
			String accountingCode = "";
			String suspensionName = "";
			int countryCode = 0;
			GetFacilityResponse facilityResponse = facilityService
					.getFacilityByIdV2(freezeExtensionInput.getFacilityId());
			if (facilityResponse != null && facilityResponse.getResult() != null) {
				accountingCode = facilityResponse.getResult().geteClubFacilityId();
				countryCode = facilityResponse.getResult().getSourceSystem();
				MosoTokenResponse mosoTokenResponse = mosoOptimizedSessionMediator.getSession(accountingCode, apiApiUserName, countryCode);
				if (Objects.isNull(mosoTokenResponse)) {
					logger.error("Fail Auth");
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
					message.setFriendlyMessage("Get Session failed");
					message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
					message.setMessageType("Exception");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
				/*
				 * logger.debug("AuthToken : {} " , sessionOutput.getAuthTokenValue());
				 * logger.debug("Cookie : {}" ,sessionOutput.getCookieValue());
				 * 
				 * HttpHeaders headers = new HttpHeaders();
				 * headers.setContentType(MediaType.APPLICATION_JSON); headers.set("Cookie",
				 * sessionOutput.getCookieValue());
				 */
				HttpEntity<HttpHeaders> entity = freezeHelper.setEntityV4(mosoTokenResponse);

				/* Calling moso API to extend freeze */
				SuspensionResponse freezeExtResponse = mosoService.freezeExtensionV5(freezeExtensionInput, freezeId, memberAgreementDetail.getFreezeEndTime(), countryCode, accountingCode);

				if (freezeExtResponse != null && StringUtils.isBlank(freezeExtResponse.getMessage())) {
					logger.debug("Success ");
					logger.debug(freezeExtResponse.getFreezeStatus());
					output.setFreezeFeeAmount(String.valueOf(freezeExtResponse.getFreezeFeeAmount()));
					output.setFreezeExtensionEndDate(freezeExtResponse.getFreezeEndDate());
					output.setFreezeExtensionStartDate(freezeExtResponse.getFreezeStartDate());
					output.setFreezeFeeItem(freezeExtResponse.getFreezeFeeItem());
					output.setFreezeFeeStartDate(freezeExtResponse.getFreezeFeeStartDate());
					output.setFreezeStatus(freezeExtResponse.getFreezeStatus());
					output.setMemberFirstName(freezeExtResponse.getMemberFirstName());
					output.setMemberLastName(freezeExtResponse.getMemberLastName());
					output.setSuspendedAgreementName(freezeExtResponse.getSuspendedAgreementName());
					output.setSuspensionId(freezeExtResponse.getSuspensionId());

					if(freezeMembershipInput.isInObligationFlag() == false) {
						/*
						 * If freeze fee wave off is false, obtain freeze fee and charge member on
						 * account
						 */
						if (!freezeExtensionInput.isWaiveOffExtensionFee()
								&& freezeExtensionInput.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {

							/* Calling moso API to obtain free fee */
							Item item = mosoService.itemSearchV4("Freeze Fee - Non Refundable", mosoTokenResponse, entity, 
									freezeExtensionInput.getMosoId(), freezeExtensionInput.getCorrelationId(), accountingCode);

							double itemPrice = 0.0;
							for (PriceDetails price : item.getPrices()) {
								if(!StringUtils.isEmpty(price.getBusinessUnitCode())){
									itemPrice = (double) price.getPrice();
								} else if (price.getSourceId() == 1 && countryCode == 1) {
									itemPrice = (double) price.getPrice();
								} else if (price.getSourceId() == 2 && countryCode == 5) {
									itemPrice = (double) price.getPrice();
								} else if (price.getSourceId() == 3 && countryCode == 6) {
									itemPrice = (double) price.getPrice();
								} else
									itemPrice = 0;
								if (itemPrice > 0) {
									break;
								}
							}
							logger.debug("Item price: {} ", itemPrice);
							String itemCode = item.getCode();

							/* Setting createAndFinalizeInvoice request */
							NewFinalizeInvoice newFinalizeInvoice = freezeHelper.setCreateAndFinalizeReqV3(itemPrice,
									freezeExtensionInput, itemCode, memberAgreementDetail.getClientAccountId(),
									accountingCode, countryCode);

							/* Calling moso API to finalize invoice */
							Invoice invoice = mosoService.createAndfinalizeInvoiceV4(newFinalizeInvoice, freezeExtensionInput.getCorrelationId(), countryCode, accountingCode);

							if (invoice != null && StringUtils.isBlank(invoice.getMessage())) {
								logger.debug("Success ");
								logger.debug("{}", invoice.getBalance());

							}
						}
					}
					
					/* Freeze suspension reasons */
					suspensionName = freezeMembershipInput.getFreezeReasonId();
					if(freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
						suspensionName = getSuspensionNameV1(freezeMembershipInput, memberAgreementDetail);
					}
					
					// Add Note in Moso
					DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
					String content = "Flow Name : FreezeExtesnion - Freeze Reason : "
							+ suspensionName + "- Duration : "
							+ freezeExtensionInput.getExtensionDurationMonths() + " Months - Freeze Fee Waived : "
							+ freezeExtensionInput.isWaiveOffExtensionFee() + " -  Freeze End Date :  "
							+ dateFormat.format(freezeExtensionInput.getFreezeExtensionEndDate());
					Note note = freezeHelper.setnoteRequest(freezeExtensionInput.getMosoId(), content);
					/* Adding note in Moso */
					mosoService.addNoteV4(note, freezeExtensionInput.getCorrelationId(), countryCode, accountingCode);
				} else {
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(freezeExtResponse.getMessage());
					message.setFriendlyMessage(freezeExtResponse.getMessage());
					message.setMessageID(FreezeServiceExceptionEnum.FREEZE_EXTENSION_FAILED.getCode());
					message.setMessageType("ERROR");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
			} else {
				ExceptionMessage message = new ExceptionMessage();
				message.setErrorMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
				message.setFriendlyMessage(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.name());
				message.setMessageID(FreezeServiceExceptionEnum.FACILITY_SERVICE_FAILED.getCode());
				message.setMessageType("Exception");
				output.setMessages(new ExceptionMessage[] { message });
				return output;
			}
		} else {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			exceptionMessage.setErrorMessage("No Active/Pending Freeze request exist for member.");
			exceptionMessage.setFriendlyMessage("No Active/Pending Freeze request exist for member.");
			exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
			exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		CreateCaseResponse createCaseResponse = null;
		SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
		Date endDate = freezeMembershipInput.getEndDate();
		Date chargeDate = Calendar.getInstance().getTime();
		
		int duration = 0;
		boolean isDurationInDays = false;
		if (freezeMembershipInput.getDurationMonths() > 0) {
			duration = freezeMembershipInput.getDurationMonths();
		} else {
			duration = freezeMembershipInput.getDurationDays();
			isDurationInDays = true;
		}
		//Send Freeze Confirmation Email to PIF members if source is engage.
		if(freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) && (freezeMembershipInput.isPIF() || memberAgreementDetail.getAgreementTermId() == 1)) {
			logger.info("Sending Freeze confirmation email to {} for PIF member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
			boolean emailsent = emailService.sendFreezeConfirmationEmailToPIFMembersV1(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(),
					memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), sdf.format(chargeDate),
					freezeMembershipInput.getFreezeFees(), duration, isDurationInDays);
			createEmailTaskForFreezeConfirmationV1(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent,true);
			return output;
		}
		//Send Freeze Confirmation Email to member if Freeze Reason is Regular
		if(freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {				
			logger.info("Sending Freeze confirmation email to {} for member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
			boolean emailsent = emailService.sendRegularFreezeConfirmationEmailV5(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(),
					memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), sdf.format(chargeDate),
					freezeMembershipInput.getFreezeFees(), duration, freezeMembershipInput.isInObligationFlag(),freezeMembershipInput.getSource(),freezeMembershipInput.isHoldBillingInObFlag(), isDurationInDays);
			
			createEmailTaskForFreezeConfirmationV1(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent, false);
		} else {
//			// medical Request email
//			logger.info("Sending Freeze Requested email to {} for member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
//			emailService.sendMedicalAndPregnancyFreezeRequestEmail(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(), 
//					sdf.format(freezeMembershipInput.getStartDate()), freezeMembershipInput.getDurationMonths());
//
//			boolean isEmailDetailsStored = freezeServiceDAO.storeEmailDetailsForMedicalPreganancyFreezeV4(freezeMembershipInput.getMosoId(), memberAgreementDetail.getFirstName(), email, 
//					freezeMembershipInput.getStartDate(), freezeMembershipInput.getDurationMonths(), freezeMembershipInput.getFreezeReasonId(), 
//					LocalDate.now().plusDays(1).toString(), endDate);
//			logger.info("Is Email details stored successfully in DB dbo.Freeze_Confirmation_Email for member: {}, {}", freezeMembershipInput.getMosoId(), isEmailDetailsStored);
			
			logger.info("Sending medical freeze confirmation mail in extension flow for email to {} for member: {}, as freeze reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
			boolean emailsent = emailService.sendMedicalAndPregnancyFreezeConfirmationEmail(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(), 
					sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), freezeMembershipInput.getDurationMonths());
			createEmailTaskForFreezeConfirmationV1(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent, false);
		}
		return output;
	}

	@Override
	public FreezeMemberResponse freezeMembershipV5(FreezeMembershipInputV4 freezeMembershipInput, boolean waveFreezeFee,
			int countryCode, String email, BigDecimal freezeFees, MemberAgreementDetail memberAgreementDetail) {
		// Declarations
				FreezeMemberResponse output = new FreezeMemberResponse();
				String suspensionName = freezeMembershipInput.getFreezeReasonId();
				String accountingCode = "";
				logger.debug("Inside Service Impl");
				
				if (freezeMembershipInput.getEndDate() == null) {
				    Date startDate = freezeMembershipInput.getStartDate();
				    Calendar cal = Calendar.getInstance();
				    cal.setTime(startDate);

				    // Check if duration in months is provided
					if (freezeMembershipInput.getDurationDays() > 0) {
						cal.add(Calendar.DAY_OF_MONTH, freezeMembershipInput.getDurationDays());
					} else if (freezeMembershipInput.getDurationMonths() > 0) {
				        cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
				    }
				    freezeMembershipInput.setEndDate(cal.getTime());
				}

				if (freezeMembershipInput.getFacilityId() != null && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUBAPP)) {
					accountingCode = freezeMembershipInput.getFacilityId();
				} else {
					accountingCode = memberAgreementDetail.getHomeFacilityId();
				}

				String eClubFacilityID = facilityConversion.facilityConversion(accountingCode);
				
				if(memberAgreementDetail.getStartDate() != null) {
					if(freezeMembershipInput.getStartDate().before(memberAgreementDetail.getStartDate())) {
						logger.error("Not eligible for freeze since the freeze start date cannot be before agreement start date");
						ExceptionMessage exceptionMessage = new ExceptionMessage();
						exceptionMessage.setErrorMessage("The FreezeStartDate cannot be before AgreementStartDate.");
						exceptionMessage.setFriendlyMessage("The FreezeStartDate cannot be before AgreementStartDate.");
						exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_STARTDATE_INVALID.getCode());
						exceptionMessage.setMessageType(FreezeMembershipConstants.VALIDATION);
						output.setMessages(new ExceptionMessage[] { exceptionMessage });
						return output;
					}
				}
				MosoTokenResponse mosoTokenResponse = mosoOptimizedSessionMediator.getSession(accountingCode, apiApiUserName, countryCode);
				if (Objects.isNull(mosoTokenResponse)) {
					logger.error("Fail Auth");
					ExceptionMessage message = new ExceptionMessage();
					message.setErrorMessage(FreezeServiceExceptionEnum.GETSESSION_FAILED.name());
					message.setFriendlyMessage("Get Session failed");
					message.setMessageID(FreezeServiceExceptionEnum.GETSESSION_FAILED.getCode());
					message.setMessageType("Exception");
					output.setMessages(new ExceptionMessage[] { message });
					return output;
				}
				HttpEntity<HttpHeaders> entity = freezeHelper.setEntityV4(mosoTokenResponse);

				if ((freezeMembershipInput.getDurationMonths() > 0 || freezeMembershipInput.getDurationDays() > 0) 
					    && memberAgreementDetail.getAgreementTermId() == 1) {

					    logger.info("Inside paid in full member for updating obligation expiration date: {}", freezeMembershipInput.getMosoId());
					    DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
					    Date startDate = (memberAgreementDetail.getEndDate() != null) ? 
					                        memberAgreementDetail.getEndDate() : 
					                        memberAgreementDetail.getObligationDate();

					    Calendar cal = Calendar.getInstance();
					    cal.setTime(startDate);

					    // Add months or days based on the input
						if (freezeMembershipInput.getDurationDays() > 0){
							cal.add(Calendar.DAY_OF_MONTH, freezeMembershipInput.getDurationDays());
						} else if (freezeMembershipInput.getDurationMonths() > 0) {
					        cal.add(Calendar.MONTH, freezeMembershipInput.getDurationMonths());
					    }

					    String obligationExpirationDate = dateFormat.format(cal.getTime()).replaceAll("-", "");
					    MemberAgreement memberAgreement = mosoService.updateObligationExpirationDateV4(
					            memberAgreementDetail.getMemberAgreementId(),
					            obligationExpirationDate, 
					            mosoTokenResponse, 
					            freezeMembershipInput.getMosoId(), 
					            freezeMembershipInput.getCorrelationId(), 
					            countryCode, 
					            accountingCode
					    );

					    if (!StringUtils.isBlank(memberAgreement.getMessage())) {
					        logger.debug("Failed to update obligation expiration date for paid in full member");
					        ExceptionMessage message = new ExceptionMessage();
					        message.setErrorMessage(memberAgreement.getMessage());
					        message.setFriendlyMessage(memberAgreement.getMessage());
					        message.setMessageID(FreezeServiceExceptionEnum.UPDATE_EXPIRYDATE_FAILED.getCode());
					        message.setMessageType("Exception");
					        output.setMessages(new ExceptionMessage[] { message });
					        output.setRequestReceivedSuccessfully(false);
					        return output;
					    }
				}

				// check if university freeze fee flag is on
				if (freezeMembershipInput.isCancelFlow()) {
					String universityFreezeFacilities = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_UNIVERSITY_FREEZE_FEE);
					if (StringUtils.isNotBlank(universityFreezeFacilities)) {
						List<String> universityFreezeFacilitiesList = Arrays.asList(universityFreezeFacilities.split(","));
						if (universityFreezeFacilitiesList.contains(eClubFacilityID)) {
							freezeMembershipInput.setUniversityMember(freezeHelper.isUniversityMember(freezeMembershipInput.getMosoId()));
							if (freezeMembershipInput.isUniversityMember()) {
								freezeMembershipInput.setInObligationFlag(false);
							}
						}
					}
				}

				/* Freeze suspension reasons */
				if(freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
					if (freezeMembershipInput.getFacilityId() == null)
						freezeMembershipInput.setFacilityId(eClubFacilityID);
					suspensionName = getSuspensionNameV1(freezeMembershipInput,memberAgreementDetail);
				}

				// check for pilot medical and pregnancy freeze fee flag in redis for engage freeze submission
				if (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) && !freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)
						&& isMedicalFreezeFeeEnabled(eClubFacilityID)) {
					freezeMembershipInput.setPilotFreezeMedFee(true);
				}

				if (freezeMembershipInput.isPilotFreezeMedFee() && !freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)
						&& !(freezeMembershipInput.isWaveFreezeFee() && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE))) {

					suspensionName = freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_MEDICAL)
							? FreezeMembershipConstants.MEDICAL_FEE : FreezeMembershipConstants.PREGNANCY_FEE;
					freezeMembershipInput.setFreezeFees(getFreezeFees(freezeMembershipInput.getDurationMonths(), memberAgreementDetail.getCountryCode()));
				} else if (!freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
					freezeMembershipInput.setFreezeFees(BigDecimal.valueOf(0.0));
				}

				logger.info("Setting Freeze Suspension Name: {} for member: {}", suspensionName, freezeMembershipInput.getMosoId());

				/* Obtain freeze reason id from tenant */
				String freezeReasonId = freezeServiceDAO.getSuspensionReasonId(suspensionName);
				logger.info("Retrieved freezeReasonId: {} from Moso DB for member: {}", freezeReasonId, freezeMembershipInput.getMosoId());

				/* Freeze reason id is mandatory for freeze */
				if (StringUtils.isBlank(freezeReasonId)) {
					ExceptionMessage exceptionMessage = new ExceptionMessage();
					exceptionMessage.setErrorMessage("Freeze reasonID is null.");
					exceptionMessage.setFriendlyMessage("Freeze reasonID is null.");
					exceptionMessage.setMessageID(FreezeServiceExceptionEnum.MEMBER_DETAIL_MISSING.getCode());
					exceptionMessage.setMessageType("Error");
					output.setMessages(new ExceptionMessage[]{exceptionMessage});
					return output;
				}

				SuspensionRequest suspensionRequest = freezeHelper.setSuspensionRequestV3(freezeReasonId, memberAgreementDetail,
						freezeMembershipInput);

				/* Calling moso API to freeze member agreement */
				SuspensionResponse suspensionResponse = mosoService.freezeAgreementV4(suspensionRequest, mosoTokenResponse, entity, 
						freezeMembershipInput.getCorrelationId(), accountingCode, countryCode);

				/* If success add note in Moso and create resolved case in sales force */
				if (suspensionResponse != null && StringUtils.isBlank(suspensionResponse.getMessage())) {
					output.setRequestReceivedSuccessfully(true);
					DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
					String freezeDuration = null;
					if (freezeMembershipInput.getDurationDays() > 0) {
						freezeDuration = freezeMembershipInput.getDurationDays() + " Days";
					} else if (freezeMembershipInput.getDurationMonths() > 0) {
						freezeDuration = freezeMembershipInput.getDurationMonths() + " Months";
					}

					if (!freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR) &&
							freezeMembershipInput.isPilotFreezeMedFee() && !(freezeMembershipInput.isWaveFreezeFee() && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE))) {
						createAndFinalizeInvoiceForFreeze(accountingCode, countryCode, freezeMembershipInput.getDurationMonths(), freezeMembershipInput.getMosoId(),
								memberAgreementDetail.getClientAccountId(), entity,eClubFacilityID, freezeMembershipInput.getCorrelationId());
					}
					
					String content = "Flow Name : Freeze - Freeze Reason : " + suspensionName
					+ "- Duration : " + freezeDuration + " - Freeze Fee Waived : "
					+ freezeMembershipInput.isWaveFreezeFee() + " Freeze Start Date :  "
					+ dateFormat.format(freezeMembershipInput.getStartDate()) + " -  Freeze End Date :  "
					+ dateFormat.format(freezeMembershipInput.getEndDate());

					/* Setting note request */
					Note note = freezeHelper.setnoteRequest(freezeMembershipInput.getMosoId(), content);
					/* Adding note in Moso */
					mosoService.addNoteV4(note, freezeMembershipInput.getCorrelationId(), countryCode, accountingCode);

					/* Creating resolved case in Sales force */
					CreateCaseResponse createCaseResponse = null;
					SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
					if(freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK) ||
							freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE) ||
							(freezeMembershipInput.isCancelFlow() && (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUBAPP) ||
							freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) ||
							freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_WEB) ||
									freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_APP)))) {
						createCaseResponse = sfdcService.createCaseV4(freezeMembershipInput, eClubFacilityID, freezeMembershipInput.getFreezeFees().doubleValue(),true);
					}
					if(Objects.nonNull(freezeMembershipInput.getFreezeFees()))
						output.setFreezeFeeAmount(freezeMembershipInput.getFreezeFees().toString());
					output.setFreezeFeeStartDate(sdf.format(freezeMembershipInput.getStartDate()));
					output.setFreezeStatus("Freeze");
					output.setMemberFirstName(memberAgreementDetail.getFirstName());
					output.setSuspendedAgreementName(memberAgreementDetail.getMembershipClass());
					output.setSuspensionId(Integer.valueOf(freezeReasonId));
					output.setSuspensionReasonName(suspensionName);
					
					Date endDate = freezeMembershipInput.getEndDate();
					Date chargeDate = Calendar.getInstance().getTime();
					
					int duration = 0;
					boolean isDurationInDays = false;
					if (freezeMembershipInput.getDurationMonths() > 0) {
						duration = freezeMembershipInput.getDurationMonths();
					} else {
						duration = freezeMembershipInput.getDurationDays();
						isDurationInDays = true;
					}
					
					//Send Freeze Confirmation Email to PIF members if source is engage.
					if(freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) && (freezeMembershipInput.isPIF() || memberAgreementDetail.getAgreementTermId() == 1)) {
						logger.info("Sending Freeze confirmation email to {} for PIF member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
						boolean emailsent = emailService.sendFreezeConfirmationEmailToPIFMembersV1(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(),
								memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), sdf.format(chargeDate),
								freezeMembershipInput.getFreezeFees(), duration, isDurationInDays);
						createEmailTaskForFreezeConfirmationV1(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent,true);
						return output;
					}
					
					//Send Freeze Confirmation Email to member if Freeze Reason is Regular
					if(freezeMembershipInput.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {				
						logger.info("Sending Freeze confirmation email to {} for member: {}, as Freeze Reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
						boolean emailsent = emailService.sendRegularFreezeConfirmationEmailV5(freezeMembershipInput.getMosoId(), email, memberAgreementDetail.getFirstName(),
								memberAgreementDetail.getCountryCode(), sdf.format(freezeMembershipInput.getStartDate()), sdf.format(endDate), sdf.format(chargeDate),
								freezeMembershipInput.getFreezeFees(), duration, freezeMembershipInput.isInObligationFlag(),freezeMembershipInput.getSource(),freezeMembershipInput.isHoldBillingInObFlag(), isDurationInDays);
						
						createEmailTaskForFreezeConfirmationV1(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent, false);
					} else {
						if (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUBAPP) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE)) {
							// medical Request email
							logger.info("Sending medical freeze requested email to {} for member: {}, as freeze reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
							MedicalFreezeEmailInput medicalFreezeEmailInput = prepareMedicalFreezeEmailInput(freezeMembershipInput, memberAgreementDetail, sdf, endDate);
							emailService.sendMedicalAndPregnancyFreezeRequestEmailV2(medicalFreezeEmailInput);
							
							boolean isEmailDetailsStored = freezeServiceDAO.storeEmailDetailsForMedicalPreganancyFreezeV4(freezeMembershipInput.getMosoId(), memberAgreementDetail.getFirstName(), email, 
									freezeMembershipInput.getStartDate(), freezeMembershipInput.getDurationMonths(), freezeMembershipInput.getFreezeReasonId(), 
									LocalDate.now().plusDays(1).toString(), endDate, memberAgreementDetail.getCountryCode(), freezeMembershipInput.isPilotFreezeMedFee());
							logger.info("Is Email details stored successfully in DB dbo.Freeze_Confirmation_Email for member: {}, {}", freezeMembershipInput.getMosoId(), isEmailDetailsStored);
						}
						if (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE)) {
							//Medical confirmation email
							logger.info("Sending medical freeze confirmation email to {} for member: {}, as freeze reason is : {}", email, freezeMembershipInput.getMosoId(), freezeMembershipInput.getFreezeReasonId());
							MedicalFreezeEmailInput medicalFreezeEmailInput = prepareMedicalFreezeEmailInput(freezeMembershipInput, memberAgreementDetail, sdf, endDate);
							boolean emailsent = emailService.sendMedicalAndPregnancyFreezeConfirmationEmailV2(medicalFreezeEmailInput);
							createEmailTaskForFreezeConfirmationV1(createCaseResponse, freezeMembershipInput, memberAgreementDetail, emailsent, false);
						}				
						//Store the Freeze documents in Salesforce for 'Medical' & 'Pregnancy' type only if the Case is created successfully
						if(Objects.nonNull(createCaseResponse) && !StringUtils.isEmpty(createCaseResponse.getId()) && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE)) {
							sfdcService.storeFreezeDocumentinSFV4(freezeMembershipInput, memberAgreementDetail.getFirstName(), createCaseResponse.getId());
						}
					}
				} else {
					String errorMessage = prepareErrorMessage(suspensionResponse);
					ExceptionMessage exceptionMessage = new ExceptionMessage();
					if (!StringUtils.isBlank(freezeMembershipInput.getSource()) && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK)) {
							exceptionMessage = errorMessageHandler.createExceptionMessage(
									FreezeServiceExceptionEnum.MOSO_ERROR_FRONT_DESK.name(), FreezeServiceExceptionEnum.MOSO_ERROR_FRONT_DESK.getCode(),
									ExceptionMessageEnum.ERROR.toString());
					} else if (!suspensionResponse.getMessage().contains("Message-null, Data-null")) {
							exceptionMessage.setErrorMessage(errorMessage);
							exceptionMessage.setFriendlyMessage(MOSO_ERROR);
							exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_MEMBERSHIP_FAILED.getCode());
							exceptionMessage.setMessageType("Exception");
					} else {
						exceptionMessage = errorMessageHandler.createCustomExceptionMessage(
								errorMessage, MOSO_ERROR, FreezeServiceExceptionEnum.MOSO_ERROR.getCode(),
								ExceptionMessageEnum.ERROR.toString());
					}
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
					output.setRequestReceivedSuccessfully(false);

					// Create case in sales force with type exception 
					if (!StringUtils.isBlank(freezeMembershipInput.getSource()) && freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_FRONT_DESK) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SEND_FREEZE_LINK_FROM_ENGAGE)
							|| (freezeMembershipInput.isCancelFlow() && (freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUBAPP)
							|| freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE) || freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_WEB) ||
								freezeMembershipInput.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_CLUB_APP))) ) {
						sfdcService.createCaseV4(freezeMembershipInput, eClubFacilityID, freezeMembershipInput.getFreezeFees().doubleValue(),false);
					}
				}
				return output;
			}

	@Override
	public CheckFreezeEligiblityOutput checkFreezeEligibilityV3(CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput) {
		CheckFreezeEligiblityOutput output = new CheckFreezeEligiblityOutput();
		Boolean hasCOF = false;
		/* Getting Member agreement detail from tenant database */
		MemberAgreementDetail memberAgreementDetail = freezeServiceDAO
				.getMemberAgreementDetail(checkFreezeEligibilityInput.getMosoId());
		if (memberAgreementDetail == null) {
			ExceptionMessage exceptionMessage = new ExceptionMessage();
			if(checkFreezeEligibilityInput.getSource().equalsIgnoreCase("clubapp")) {
				exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
			}else {
				exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
			}
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			output.setStatus(FreezeMembershipConstants.STATUS_ERROR);
			output.setEligibleForFreeze(false);
			return output;
		}
		FundingSource fundingSource = cardOnFileDao.getFundingSource(checkFreezeEligibilityInput.getMosoId());
		if(fundingSource != null && fundingSource.getCreditCardToken() != null) {
			hasCOF = true;
		}
		/*Getting corp agreement details from tenant database*/
		CorpAgreementDetail corpAgreementDetail = freezeServiceDAO
				.getCorpAgreementDetail(checkFreezeEligibilityInput.getMosoId());
		/*newly added code for https://equinoxfitness.atlassian.net/browse/SPACES-2845*/
		List<MemberAgreementDetail> memberAgreementDetailList = freezeServiceDAO
				.getMemberAgreementDetailList(checkFreezeEligibilityInput.getMosoId());
		BigDecimal balance=new BigDecimal(0);
		//commented below code as per https://equinoxfitness.atlassian.net/browse/SPACES-3065
		//BigDecimal balance =  freezeServiceDAO.getMosoMemberAccountBalance(checkFreezeEligibilityInput.getMosoId());
		/* Calling method that contains rules to verify eligibility of member */
		String facilityId = facilityConversion.facilityConversion(memberAgreementDetail.getHomeFacilityId());
		if (checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR))
			output = freezeEligibilityRule.eligibilityRuleCheckV3(memberAgreementDetail, checkFreezeEligibilityInput, hasCOF,corpAgreementDetail,memberAgreementDetailList,balance, facilityId);
		/* adding facility id*/
		output.setEligibleForOneMonthFreeze(eligibleForOneMonthFreeze(facilityId));
		if (!checkFreezeEligibilityInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_MEDICAL) &&
				!checkFreezeEligibilityInput.getFreezeReason().contains(FreezeMembershipConstants.REASON_PREGNANCY)) {
			checkDurationInDaysForEligibilityV2(facilityId, output, checkFreezeEligibilityInput.getSource());
		} else {
			// check for medical and pregnancy fee flag
			output.setMonthlyFeeRate(0.0);
			String medicalPregnancyFeeFlag = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_MEDICAL_FREEZE_FEE);
			if (StringUtils.isNotBlank(medicalPregnancyFeeFlag)) {
				List<String> medicalPregnancyFeeFacilities = Arrays.asList(medicalPregnancyFeeFlag.split(","));
				if (medicalPregnancyFeeFacilities.contains(facilityId)) {
					output.setMonthlyFeeRate(FreezeMembershipConstants.MED_PREGNANCY_FEE);
				}
			}
		}
		return output;
	}

	private void createAndFinalizeInvoiceForFreeze(String accountingCode, Integer countryCode, int durationMonths, String mosoId,
			int clientAccountId, HttpEntity<HttpHeaders> entity, String facilityId, String correlationId) {
		logger.info("Inside createAndFinalizeInvoiceForFreeze for member: {}", mosoId);
		// Get Session
		MosoTokenResponse mosoTokenResponse = mosoOptimizedSessionMediator.getSession(accountingCode, apiApiUserName, countryCode);

		/* Calling moso API to obtain free fee */
		Item item = mosoService.itemSearch("Admin Fee - Non Refundable", mosoTokenResponse, entity, accountingCode, mosoId, correlationId);
		logger.info("Item search results : {}", item);
		
		if (item != null && item.getMessage() == null) {
			double itemPrice = 0.0;
			for (PriceDetails price : item.getPrices()) {
				if (price.getSourceId() == 1 && countryCode == 1) {
					itemPrice = (double) price.getPrice();
				} else if (price.getSourceId() == 2 && countryCode == 5) {
					itemPrice = (double) price.getPrice();
				} else if (price.getSourceId() == 3 && countryCode == 6) {
					itemPrice = (double) price.getPrice();
				} else
					itemPrice = 0;
				if (itemPrice > 0) {
					break;
				}
			}

			logger.debug("Item price {} ", itemPrice);
			String itemCode = item.getCode();
			NewFinalizeInvoice newFinalizeInvoice = createAndFinalizeRequest(itemPrice, durationMonths, mosoId, itemCode, clientAccountId,
					accountingCode, countryCode);
			createAndFinalizeInvoice(newFinalizeInvoice, null, entity, countryCode, accountingCode);
		}

		
	}

	private NewFinalizeInvoice createAndFinalizeRequest(double itemPrice, int durationMonths, String mosoId,
			String itemCode, int clientAccountId, String accountingCode, int countryCode) {
		/* Setting createAndFinalizeInvoice request */
		return freezeHelper.setCreateAndFinalizeReqV5(itemPrice,
				durationMonths, mosoId, itemCode, clientAccountId,
				accountingCode, countryCode);
	}

	private void createAndFinalizeInvoice(NewFinalizeInvoice newFinalizeInvoice, GetSessionOutput sessionOutput, HttpEntity<HttpHeaders> entity,
			int countryCode, String accountingCode) {

		/* Calling moso API to finalize invoice */
		Invoice invoice = mosoService.createAndfinalizeInvoice(newFinalizeInvoice, sessionOutput, entity, null, countryCode, accountingCode);

		if (invoice != null && StringUtils.isBlank(invoice.getMessage())) {
			logger.debug("Success ");
			logger.debug("{}", invoice.getBalance());

		}
	}

	private BigDecimal getFreezeFees(int duration, String countryCode) {
		BigDecimal monthlyFeeRate = BigDecimal.valueOf(FreezeMembershipConstants.MED_PREGNANCY_FEE);
		if (countryCode.equalsIgnoreCase(FreezeMembershipConstants.COUNTRY_UK)) {
			BigDecimal freezeFee = monthlyFeeRate.multiply(BigDecimal.valueOf(0.8)).multiply(BigDecimal.valueOf(duration));
			// do not add any decimal
			return freezeFee.setScale(0, RoundingMode.DOWN);
		}
		return monthlyFeeRate.multiply(BigDecimal.valueOf(duration));
	}

	private boolean isMedicalFreezeFeeEnabled(String facilityId) {
		String medicalFreezeFeeKeys = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_MEDICAL_FREEZE_FEE);
		if (StringUtils.isNotBlank(medicalFreezeFeeKeys)) {
			List<String> medicalFreezeFeeFacilities = Arrays.asList(medicalFreezeFeeKeys.split(","));
			if (medicalFreezeFeeFacilities.contains(facilityId)) {
				return true;
			}
		}
		return false;
	}

}
