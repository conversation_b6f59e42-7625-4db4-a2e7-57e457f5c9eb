package com.equinoxfitness.freezeservice.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.equinoxfitness.freezeservice.contract.MedicalFreezeEmailInput;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.equinoxfitness.emailcommunication.model.EmailCommunicationRequest;
import com.equinoxfitness.emailcommunication.model.EmailCommunicationResponse;
import com.equinoxfitness.emailcommunication.service.SendEmailService;
import com.equinoxfitness.freezeservice.contract.FreezeEmailInput;
import com.equinoxfitness.freezeservice.service.EmailService;
import com.equinoxfitness.freezeservice.utils.FreezeMembershipConstants;

@Service
public class EmailServiceImpl implements EmailService {
	private static final Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);

	@Value("${app.name}")
	private String appName;

	@Value("${freeze.regular.confirmation.email}")
	private String regularFreezeConfirmationEmailTemplate;

	@Value("${freeze.medical.pregnancy.request.email}")
	private String medicalAndPregnancyFreezeRequestEmailTemplate;
	
	@Value("${freeze.medical.pregnancy.confirmation.email}")
	private String medicalAndPregnancyFreezeConfirmationEmailTemplate;

	@Value("${medical.freeze.request.email}")
	private String newMedicalAndPregnancyFreezeRequestEmailTemplate;

	@Value("${medical.freeze.confirmation.email}")
	private String newMedicalAndPregnancyFreezeConfirmationEmailTemplate;

	@Autowired
	private SendEmailService sendEmailService;

	@Value("${member.freeze.verification.template}")
	private String freezeLinkToMemberFromClubapp;

	@Value("${website.baseURL}")
	private String websiteBaseURL;
	
	@Value("#{${country.currency.map}}")
	private Map<String, String> countryCurrencyMap;
	
	@Value("${freeze.regular.confirmation.email.inOb}")
	private String regularFreezeConfirmationInObEmailTemplate;	
	
	@Value("${freeze.regular.confirmation.email.outOfOb}")
	private String regularFreezeConfirmationOutOfObEmailTemplate;	
	
	@Value("${freeze.confirmation.email.to.pif.members}")
	private String freezeConfirmationPIFMembersEmailTemplate;	
	
	@Value("${freeze.confirmation.to.member.for.regular.out.of.ob.days.flow}")
	private String freezeConfirmationToMemberForRegularOutOfObDaysFlowEmailTemplate;

	@Value("${freeze.confirmation.mail.to.pif.members.days.flow}")
	private String freezeConfirmationMailToPIFMembersDaysFlowEmailTemplate;

	@Value("${freeze.confirmation.to.member.for.regular.in.ob.days.flow}")
	private String freezeConfirmationToMemberForRegularInObDaysFlowEmailTemplate;


	@Override
	public boolean sendRegularFreezeConfirmationEmail(String memberId, String emailAddress, String memberFirstName, String countryCode, String startDate, BigDecimal freezeFees, int duration, String chargeDate) {
		if(emailAddress != null) {
			List<String> toEmailAddressList = new ArrayList<>();
			freezeFees = Objects.isNull(freezeFees) ? BigDecimal.ZERO : freezeFees;
			String currencyCode = StringUtils.isEmpty(countryCode) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(countryCode);
			
			toEmailAddressList.add(emailAddress);

			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", memberFirstName);
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(duration));
			placeHolderMap.put("%%FREEZE FEE%%", freezeFees.toString());
			placeHolderMap.put("%%CHARGE DATE%%", chargeDate);
			placeHolderMap.put("%%CURRENCY%%", currencyCode);

			return getContentAndSendEmail(toEmailAddressList, regularFreezeConfirmationEmailTemplate, placeHolderMap, memberId);
		}
		return false;
	}

	@Override
	public boolean sendMedicalAndPregnancyFreezeRequestEmail(String memberId, String emailAddress, String memberFirstName, String startDate, int duration) {
		if(emailAddress != null) {
			List<String> toEmailAddressList = new ArrayList<>();
			toEmailAddressList.add(emailAddress);

			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", memberFirstName);
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(duration));

			return getContentAndSendEmail(toEmailAddressList, medicalAndPregnancyFreezeRequestEmailTemplate, placeHolderMap, memberId);

		}
		return false;
	}

	private boolean getContentAndSendEmail(List<String> toEmailAddressList, String template,
			Map<String, String> placeHolder, String memberId) {
		boolean emailSent = false;
		EmailCommunicationRequest emailCommunicationRequest = new EmailCommunicationRequest();
		emailCommunicationRequest.setTemplate(template);
		if (memberId != null)
			emailCommunicationRequest.setMemberID(memberId);
		emailCommunicationRequest.setData(placeHolder);
		emailCommunicationRequest.setSplitRecipients(false);
		emailCommunicationRequest.setSourceApp(appName);

		String recipients = String.join(",", toEmailAddressList);
		emailCommunicationRequest.setRecipients(recipients);
		EmailCommunicationResponse emailCommunicationResponse = null;
		try {
			emailCommunicationResponse = sendEmailService.sendEmail(emailCommunicationRequest);
		} catch (Exception e) {
			logger.error("Failed to send Email from sendEmailService for Email: {}, Member: {}, error: {}", toEmailAddressList, memberId, e.getMessage());
		}
		if (Objects.nonNull(emailCommunicationResponse) && emailCommunicationResponse.getMessages() == null)
			emailSent = true;
		logger.debug("Send email to {} is {} for Member: {}", toEmailAddressList, emailSent ? "successful" : "failed", memberId);
		return emailSent;
	}
	@Override
	public boolean sendMemberFreezeEmail(FreezeEmailInput freezeEmailInput, String token, String firstName,String source) {
		if(freezeEmailInput.getEmail() != null) {
			List<String> toEmailAddressList = new ArrayList<>();			
			toEmailAddressList.add(freezeEmailInput.getEmail());			
			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%First Name%%", firstName);
			placeHolderMap.put("%%token%%", token);
			placeHolderMap.put("%%URL%%", websiteBaseURL);			
			return getContentAndSendFreezeEmail(toEmailAddressList, freezeLinkToMemberFromClubapp, placeHolderMap, freezeEmailInput.getMemberId(),source);
		}
		return false;
	}

	private boolean getContentAndSendFreezeEmail(List<String> toEmailAddressList, String template,
			Map<String, String> placeHolderMap, String memberId,String source) {
		boolean emailSent = false;
		EmailCommunicationRequest emailCommunicationRequest = new EmailCommunicationRequest();
		emailCommunicationRequest.setTemplate(template);
		if (memberId != null)
			emailCommunicationRequest.setMemberID(memberId);
		emailCommunicationRequest.setData(placeHolderMap);
		emailCommunicationRequest.setSplitRecipients(false);
		if(source.equalsIgnoreCase("clubapp"))			
			source="ClubApp";
		else 
			source="Engage";
		emailCommunicationRequest.setSourceApp(source);

		String recipients = String.join(",", toEmailAddressList);
		emailCommunicationRequest.setRecipients(recipients);
		EmailCommunicationResponse emailCommunicationResponse = null;
		try {
			emailCommunicationResponse = sendEmailService.sendEmail(emailCommunicationRequest);
		} catch (Exception e) {
			logger.error("Failed to send Email from sendEmailService!!!!", e);
		}
		if (emailCommunicationResponse.getMessages() == null)
			emailSent = true;
		logger.debug("Send email to {} is {}", toEmailAddressList, emailSent ? "successful" : "failed");
		return emailSent;
	}

	@Override
	public boolean sendRegularFreezeConfirmationEmailV4(String memberId, String emailAddress, String memberFirstName, String countryCode, String startDate, 
			String endDate, String chargeDate, BigDecimal freezeFees, int duration, boolean isInObligationFlag,String source, boolean isHoldBillingInObFlag) {
		if(emailAddress != null) {
			List<String> toEmailAddressList = new ArrayList<>();
			freezeFees = Objects.isNull(freezeFees) ? BigDecimal.ZERO : freezeFees;
			String currencyCode = StringUtils.isEmpty(countryCode) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(countryCode);
			
			toEmailAddressList.add(emailAddress);

			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", memberFirstName);
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE ENDDATE%%", endDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(duration));
			placeHolderMap.put("%%FREEZE FEE%%", freezeFees.toString());
			placeHolderMap.put("%%CURRENCY%%", currencyCode);
			if(source.equalsIgnoreCase("Engage") && isHoldBillingInObFlag && isInObligationFlag) {
	            return getContentAndSendEmail(toEmailAddressList, regularFreezeConfirmationOutOfObEmailTemplate, placeHolderMap, memberId);
			}
			if(isInObligationFlag) {
			return getContentAndSendEmail(toEmailAddressList, regularFreezeConfirmationInObEmailTemplate, placeHolderMap, memberId);
			}
			return getContentAndSendEmail(toEmailAddressList, regularFreezeConfirmationOutOfObEmailTemplate, placeHolderMap, memberId);
		}
		return false;
	}

	@Override
	public boolean sendRegularFreezeConfirmationEmailV5(String memberId, String emailAddress, String memberFirstName, String countryCode, String startDate, String endDate, String chargeDate, BigDecimal freezeFees, int duration, boolean isInObligationFlag,String source, boolean isHoldBillingInObFlag, boolean isDurationInDays) {
		if(emailAddress != null) {
			List<String> toEmailAddressList = new ArrayList<>();
			freezeFees = Objects.isNull(freezeFees) ? BigDecimal.ZERO : freezeFees;
			String currencyCode = StringUtils.isEmpty(countryCode) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(countryCode);

			toEmailAddressList.add(emailAddress);

			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", memberFirstName);
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE ENDDATE%%", endDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(duration));
			placeHolderMap.put("%%FREEZE FEE%%", freezeFees.toString());
			placeHolderMap.put("%%CURRENCY%%", currencyCode);
			if(source.equalsIgnoreCase("Engage") && isHoldBillingInObFlag && isInObligationFlag) {
				String template = isDurationInDays ? freezeConfirmationToMemberForRegularOutOfObDaysFlowEmailTemplate : regularFreezeConfirmationOutOfObEmailTemplate;
				return getContentAndSendEmail(toEmailAddressList, template, placeHolderMap, memberId);
			}
			if(isInObligationFlag) {
				String template = isDurationInDays ? freezeConfirmationToMemberForRegularInObDaysFlowEmailTemplate : regularFreezeConfirmationInObEmailTemplate;
				return getContentAndSendEmail(toEmailAddressList, template, placeHolderMap, memberId);
			}
			String template = isDurationInDays ? freezeConfirmationToMemberForRegularOutOfObDaysFlowEmailTemplate : regularFreezeConfirmationOutOfObEmailTemplate;
			return getContentAndSendEmail(toEmailAddressList, template, placeHolderMap, memberId);
		}
		return false;
	}

	@Override
	public boolean sendFreezeConfirmationEmailToPIFMembers(String memberId, String emailAddress, String memberFirstName, String countryCode, String startDate, String endDate, String chargeDate, BigDecimal freezeFees, int durationInMonths) {
		if(emailAddress != null) {
			List<String> toEmailAddressList = new ArrayList<>();
			freezeFees = Objects.isNull(freezeFees) ? BigDecimal.ZERO : freezeFees;
			String currencyCode = StringUtils.isEmpty(countryCode) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(countryCode);
			toEmailAddressList.add(emailAddress);
			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", memberFirstName);
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE ENDDATE%%", endDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(durationInMonths));
			placeHolderMap.put("%%FREEZE FEE%%", freezeFees.toString());
			placeHolderMap.put("%%CURRENCY%%", currencyCode);
			return getContentAndSendEmail(toEmailAddressList, freezeConfirmationPIFMembersEmailTemplate, placeHolderMap, memberId);
		}
		return false;
	}
	@Override
	public boolean sendFreezeConfirmationEmailToPIFMembersV1(String memberId, String emailAddress, String memberFirstName,
			String countryCode, String startDate, String endDate, String chargeDate, BigDecimal freezeFees,
			int duration, boolean isDurationInDays) {
		if(emailAddress != null) {
			List<String> toEmailAddressList = new ArrayList<>();
			freezeFees = Objects.isNull(freezeFees) ? BigDecimal.ZERO : freezeFees;
			String currencyCode = StringUtils.isEmpty(countryCode) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(countryCode);
			toEmailAddressList.add(emailAddress);
			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", memberFirstName);
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE ENDDATE%%", endDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(duration));
			placeHolderMap.put("%%FREEZE FEE%%", freezeFees.toString());
			placeHolderMap.put("%%CURRENCY%%", currencyCode);
			String template = isDurationInDays ? freezeConfirmationMailToPIFMembersDaysFlowEmailTemplate : freezeConfirmationPIFMembersEmailTemplate;
			return getContentAndSendEmail(toEmailAddressList, template, placeHolderMap, memberId);
		}
		return false;
	}

	@Override
	public boolean sendMedicalAndPregnancyFreezeConfirmationEmail(String memberId, String emailAddress, String memberFirstName,
			String startDate, String endDate, int durationMonths) {
		if(emailAddress != null) {
			List<String> toEmailAddressList = new ArrayList<>();
			toEmailAddressList.add(emailAddress);
			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", memberFirstName);
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(durationMonths));
			placeHolderMap.put("%%FREEZE ENDDATE%%", endDate);
			return getContentAndSendEmail(toEmailAddressList, medicalAndPregnancyFreezeConfirmationEmailTemplate, placeHolderMap, memberId);
		}
		return false;
	}

	@Override
	public boolean sendMedicalAndPregnancyFreezeConfirmationEmailV2(MedicalFreezeEmailInput medicalFreezeEmailInput) {
		if(medicalFreezeEmailInput.getEmailAddress() != null) {
			BigDecimal freezeFees = medicalFreezeEmailInput.getFreezeFees();
			List<String> toEmailAddressList = new ArrayList<>();
			freezeFees = Objects.isNull(freezeFees) ? BigDecimal.ZERO : freezeFees;
			String currencyCode = StringUtils.isEmpty(medicalFreezeEmailInput.getCountryCode()) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(medicalFreezeEmailInput.getCountryCode());
			toEmailAddressList.add(medicalFreezeEmailInput.getEmailAddress());

			String startDate = formatFreezeDate(medicalFreezeEmailInput.getStartDate(), medicalFreezeEmailInput.getCountryCode());
			String endDate = formatFreezeDate(medicalFreezeEmailInput.getEndDate(), medicalFreezeEmailInput.getCountryCode());

			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", medicalFreezeEmailInput.getMemberFirstName());
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(medicalFreezeEmailInput.getDurationMonths()));
			placeHolderMap.put("%%FREEZE ENDDATE%%", endDate);

			if(medicalFreezeEmailInput.isPilotFreezeMedFee()) {
				placeHolderMap.put("%%FREEZE FEE%%", freezeFees.toString());
				placeHolderMap.put("%%CURRENCY%%", currencyCode);
				return getContentAndSendEmail(toEmailAddressList, newMedicalAndPregnancyFreezeConfirmationEmailTemplate, placeHolderMap, medicalFreezeEmailInput.getMemberId());
			} else {
				return getContentAndSendEmail(toEmailAddressList, medicalAndPregnancyFreezeConfirmationEmailTemplate, placeHolderMap, medicalFreezeEmailInput.getMemberId());
			}

		}
		return false;
	}

	@Override
	public boolean sendMedicalAndPregnancyFreezeRequestEmailV2(MedicalFreezeEmailInput medicalFreezeEmailInput) {
		if(medicalFreezeEmailInput.getEmailAddress() != null) {
			List<String> toEmailAddressList = new ArrayList<>();
			BigDecimal freezeFees = medicalFreezeEmailInput.getFreezeFees();
			freezeFees = Objects.isNull(freezeFees) ? BigDecimal.ZERO : freezeFees;
			String currencyCode = StringUtils.isEmpty(medicalFreezeEmailInput.getCountryCode()) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(medicalFreezeEmailInput.getCountryCode());
			toEmailAddressList.add(medicalFreezeEmailInput.getEmailAddress());

			String startDate = formatFreezeDate(medicalFreezeEmailInput.getStartDate(), medicalFreezeEmailInput.getCountryCode());

			Map<String,String> placeHolderMap = new HashMap<>();
			placeHolderMap.put("%%FIRST NAME%%", medicalFreezeEmailInput.getMemberFirstName());
			placeHolderMap.put("%%FREEZE STARTDATE%%", startDate);
			placeHolderMap.put("%%FREEZE DURATION%%", String.valueOf(medicalFreezeEmailInput.getDurationMonths()));

			if (medicalFreezeEmailInput.isPilotFreezeMedFee()) {
				placeHolderMap.put("%%FREEZE FEE%%", freezeFees.toString());
				placeHolderMap.put("%%CURRENCY%%", currencyCode);
				return getContentAndSendEmail(toEmailAddressList, newMedicalAndPregnancyFreezeRequestEmailTemplate, placeHolderMap, medicalFreezeEmailInput.getMemberId());
			} else {
				return getContentAndSendEmail(toEmailAddressList, medicalAndPregnancyFreezeRequestEmailTemplate, placeHolderMap, medicalFreezeEmailInput.getMemberId());
			}

		}
		return false;
	}

	private String formatFreezeDate(String inputDate, String countryCode) {
		if (countryCode.equalsIgnoreCase(FreezeMembershipConstants.COUNTRY_UK)) {
			// consider date format DD/MM/YYYY
	        SimpleDateFormat inputFormat = new SimpleDateFormat("MM/dd/yyyy");
			try {
				Date date = inputFormat.parse(inputDate);
		        SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
		        return outputFormat.format(date);
			} catch (ParseException e) {
				logger.error("Error parsing start date: {} - {}", inputDate, e.getMessage());
			}
		}
		return inputDate;
	}

}
