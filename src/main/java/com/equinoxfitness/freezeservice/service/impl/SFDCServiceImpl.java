/**
 * 
 */
package com.equinoxfitness.freezeservice.service.impl;

import java.io.DataOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.equinoxfitness.commons.service.EventsService;
import com.equinoxfitness.commons.utils.DynamoPK;
import com.equinoxfitness.freezeservice.contract.FreezeDocuments;
import com.equinoxfitness.freezeservice.contract.FreezeEmailInput;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV3;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV4;
import com.equinoxfitness.freezeservice.contract.sfdc.AuthTokenData;
import com.equinoxfitness.freezeservice.contract.sfdc.Case;
import com.equinoxfitness.freezeservice.contract.sfdc.ClubData;
import com.equinoxfitness.freezeservice.contract.sfdc.ContactData;
import com.equinoxfitness.freezeservice.contract.sfdc.ContentVersionSFRequest;
import com.equinoxfitness.freezeservice.contract.sfdc.CreateCaseResponse;
import com.equinoxfitness.freezeservice.contract.sfdc.Task;
import com.equinoxfitness.freezeservice.contract.sfdc.TaskOutput;
import com.equinoxfitness.freezeservice.contract.sfdc.UnFreezeCaseInput;
import com.equinoxfitness.freezeservice.service.SFDCService;
import com.equinoxfitness.freezeservice.utils.FreezeMembershipConstants;
import com.equinoxfitness.freezeservice.utils.FreezeServiceHelper;
import com.equinoxfitness.salesforce.contract.Wrapper;
import com.equinoxfitness.salesforce.service.SalesforceApiService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 *
 */
@Service
public class SFDCServiceImpl implements SFDCService {

	@Value("${sfdc.authtoken.pass}")
	private String authTokenPass;
	
	@Value("${sfdc.authtoken.security.token}")
	private String authTokenSecurityToken;
	
	@Value("${sfdc.authtoken.user.name}")
	private String authTokenUserName;
	
	@Value("${sfdc.authtoken.loginurl}")
	private String authTokenLoginUrl;
	
	@Value("${sfdc.authtoken.client.id}")
	private String authTokenClientId;
	
	@Value("${sfdc.authtoken.client.secret}")
	private String authTokenClientSecret;
	
	@Value("${salesforce.contentversion.freeze.folder.id}")
	private String contentversionFreezeFolderId;
	
	@Value("#{${country.currency.map}}")
	private Map<String, String> countryCurrencyMap;

	@Autowired
	private EventsService eventService;
	
	@Autowired
	private SalesforceApiService salesforceApiService;
	
	@Autowired
	FreezeServiceHelper freezeHelper;
	
	@Autowired
	MessageSource messageSource;
	
	Logger logger = LoggerFactory.getLogger(SFDCServiceImpl.class);
	
	private static final String EMAIL_NOTE_TITLE = "Membership Freeze Confirmed";
	
	private static final String SEND_FREEZE_MAIL_LINK_NOTE_TITLE = "Freeze Your Equinox Membership email sent";
	
	private static final String SFDC_ERROR = "SFDC_ERROR";
	
	private static final String FREEZE_CNF_EMAIL_INOB = "FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_IN_OB";

	private static final String FREEZE_CNF_EMAIL_INOB_DAYS_FLOW = "FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_IN_OB_DAYS_FLOW";
	
	private static final String FREEZE_CNF_EMAIL_OUTOFOB = "FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_OUT_OF_OB";

	private static final String FREEZE_CNF_EMAIL_OUTOFOB_DAYS_FLOW = "FREEZE_CONFIRMATION_TO_MEMBER_FOR_REGULAR_OUT_OF_OB_DAYS_FLOW";
	
	private static final String FREEZE_CNF_EMAIL_TO_PIF = "FREEZE_CONFIRMATION_MAIL_TO_PIF_MEMBERS";

	private static final String FREEZE_CNF_EMAIL_TO_PIF_DAYS_FLOW = "FREEZE_CONFIRMATION_MAIL_TO_PIF_MEMBERS_DAYS_FLOW";

	private static final String FREEZE_EMAIL_LINK_TO_MEMBER = "FREEZE_LINK_TO_MEMBER_FROM_CLUBAPP";
	
	private static final String MEDICAL_PREGNANCY_FREEZE_CNF_EMAIL_TO_MEMBER = "FREEZE_CONFIRMATION_TO_MEMBER_FOR_MEDICAL_PREGNANCY_REASON";

	private static final String MEDICAL_PREGNANCY_FREEZE_CNF_EMAIL_TO_MEMBER_WITH_FEE = "FREEZE_CONFIRMATION_TO_MEMBER_FOR_MEDICAL_PREGNANCY_REASON_WITH_FEE";
	
	private static final String ENGAGE_FREEZE_CASE_DESCRIPTION = "Freeze successfully completed.";
	
	private static final String ENGAGE_FREEZE_CASE_SUBJECT = "Membership Freeze";
	
	private static final String COMPLETED = "Completed";
	
	private static final String EMAIL = "Email";
	
	private static final String PRIORITY = "Normal";
	
	private static final String SOURCE_ENGAGE = "Engage";
	
	private static final String SOURCE_CONCIERGE = "concierge";

	private static final String WEB_ = "web_";

	private static final String MEDICAL_FREEZE_REQUEST_FEE = "MEDICAL_FREEZE_REQUEST_FEE";

	private static final String MEDICAL_FREEZE_CONFIRMATION_FEE = "MEDICAL_FREEZE_CONFIRMATION_FEE";

	public AuthTokenData getSfdcSecurityToken() {

		AuthTokenData token = null;
		RestTemplate restTemplate = new RestTemplate();

		String loginURL = authTokenLoginUrl + "&client_id=" + authTokenClientId + "&client_secret="
				+ authTokenClientSecret + "&username=" + authTokenUserName + "&password=" + authTokenPass
				+ authTokenSecurityToken;
		logger.debug("sfdc auth url {}", loginURL);
		ResponseEntity<AuthTokenData> response = null;
		try {
			response = restTemplate.postForEntity(loginURL, null, AuthTokenData.class);
		} catch (RestClientException e) {
			logger.error("API error {}", e.getMessage());
			return token;
		}
		if (response != null && response.getBody() != null) {
			token = response.getBody();
		}
		return token;

	}

	@Override
	public String getContactId(String mosoMemberId) {

		String token = null;
		String contactId = "";
		AuthTokenData tokenData = getSfdcSecurityToken();
		logger.debug("auth data from sfdc {} ", tokenData);
		if (tokenData == null) {
			return contactId;
		}
		String getContactIdUrl = tokenData.getInstanceUrl()
				+ "/services/data/v43.0/query/?q=select+id+FROM+Contact+where+moso_id__c+='"
				+ mosoMemberId + "'";
		logger.debug("getOpportunityUrl url {} ", getContactIdUrl);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		ContactData data = null;

		try {
			URL object = new URL(getContactIdUrl);

			HttpURLConnection connection = (HttpURLConnection) object.openConnection();
			// int timeOut = connection.getReadTimeout();
			connection.setReadTimeout(60 * 100);
			// connection.setConnectTimeout(60 * 1000);
			connection.setConnectTimeout(60 * 1000);
			connection.setRequestProperty("Authorization", authorization);
			connection.setRequestProperty("Content-Type", "application/json");

			// logger.debug(this.read(connection.getInputStream()));
			ObjectMapper objectMapper = new ObjectMapper();
			data = objectMapper.readValue(connection.getInputStream(), ContactData.class);

			if (data != null && data.getRecords() != null && data.getRecords().size() > 0) {
				logger.debug("Record Size {}", data.getRecords().size());

				contactId = data.getRecords().get(0).getId();
				logger.debug("ContactId {} ", contactId);
			}
			return contactId;
		} catch (IOException e) {

			logger.error("error while interacting with sfdc {}", token);
			logger.error(e.getMessage());
		}
		return contactId;
	}

	@Override
	public String getClubId(String homeFacilityId) {
		String token = null;
		String clubId = "";
		AuthTokenData tokenData = getSfdcSecurityToken();
		logger.debug("auth data from sfdc {} ", tokenData);
		if (tokenData == null) {
			return clubId;
		}
		String getClubIdUrl = tokenData.getInstanceUrl()
				+ "/services/data/v43.0/query/?q=select+Id+FROM+Club__C+where+ClubID__c+='" + homeFacilityId + "'";
		logger.debug("getOpportunityUrl url {} ", getClubIdUrl);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		ClubData data = null;

		try {
			URL object = new URL(getClubIdUrl);

			HttpURLConnection connection = (HttpURLConnection) object.openConnection();
			// int timeOut = connection.getReadTimeout();
			connection.setReadTimeout(60 * 100);
			// connection.setConnectTimeout(60 * 1000);
			connection.setConnectTimeout(60 * 1000);
			connection.setRequestProperty("Authorization", authorization);
			connection.setRequestProperty("Content-Type", "application/json");

			// logger.debug(this.read(connection.getInputStream()));
			ObjectMapper objectMapper = new ObjectMapper();
			data = objectMapper.readValue(connection.getInputStream(), ClubData.class);

			if (data != null && data.getRecords() != null && data.getRecords().size() > 0) {
				logger.debug("Record Size {}", data.getRecords().size());

				clubId = data.getRecords().get(0).getId();
				logger.debug("ClubID {} ", clubId);
			}
			return clubId;
		} catch (IOException e) {

			logger.error("error while interacting with sfdc {}", token);
			logger.error(e.getMessage());
		}
		return clubId;
	}

	@Override
	public CreateCaseResponse createCase(FreezeMembershipInput input, String eClubId, Double freezeFee) {
		logger.info("Creating Case in Salesforce for member: {}", input.getMemberId());
		CreateCaseResponse caseOutput = null;
		if (!StringUtils.isBlank(input.getSource()) && (input.getSource().equalsIgnoreCase("Web") || input.getSource().equalsIgnoreCase("Website"))) {

			AuthTokenData tokenData = getSfdcSecurityToken();
			if (tokenData == null) {
				logger.error("API error :: failed to get SfdcSecurityToken");
				return caseOutput;
			}
			logger.debug("auth data from sfdc {} ", tokenData);
			String authorization = "OAuth" + " " + tokenData.getAccessToken();
			logger.debug("authorization {} ", authorization);
			String noteUrl = tokenData.getInstanceUrl() + "/services/data/v43.0/sobjects/case";
			//Date date = Calendar.getInstance().getTime();
			String status = "";
			String recordTypeId = "";
			Case caseInput = new Case();
			caseInput.setId(null);
			String clubId = getClubId(eClubId);
			logger.debug("Club :{}", clubId);
			String contactId = getContactId(input.getMemberId());
			caseInput.setClub(clubId);
			caseInput.setContactId(contactId);
			if (input.getSource().equalsIgnoreCase("Web") && (input.getFreezeReasonId().equalsIgnoreCase("Medical")
					|| input.getFreezeReasonId().equalsIgnoreCase("Pregnancy"))) {
				status = "Action";
				recordTypeId = "012120000012dEk";
				caseInput.setIsException(true);
			} else {
				status = "Resolved";
				recordTypeId = "012120000012dEl";
				caseInput.setIsException(false);
				caseInput.setFreezeFee(freezeFee);
			}
			caseInput.setStatus(status);
			caseInput.setDescription(ENGAGE_FREEZE_CASE_DESCRIPTION);
			caseInput.setDoNotContact(true);
			caseInput.setDuration(String.valueOf(input.getDurationMonths()));
			caseInput.setSubject("Membership Freeze");
			caseInput.setRecordTypeId(recordTypeId);
			// caseInput.setFeedbackSource(feedbackSource);
			caseInput.setFreezeEndDate(input.getEndDate());
			if (input.getFreezeFees() != null && input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
				caseInput.setFreezeFee(input.getFreezeFees().doubleValue());
			}
			caseInput.setFreezeReason(input.getFreezeReasonId());
			caseInput.setFreezeStartDate(input.getStartDate());
			caseInput.setMemberId(input.getMemberId());
			caseInput.setOrigin("Equinox.com");
			logger.info("Contact Id :{} for memberId: {}", contactId, input.getMemberId());
			try {
				logger.info("noteUrl: {}", noteUrl);
				URL obj = new URL(noteUrl);
				HttpURLConnection con = (HttpURLConnection) obj.openConnection();
				ObjectMapper objectMapper = new ObjectMapper();

				// Setting basic post request
				con.setRequestMethod("POST");
				con.setReadTimeout(60 * 1000);
				con.setConnectTimeout(60 * 1000);
				con.setRequestProperty("Authorization", authorization);
				con.setRequestProperty("Content-Type", "application/json");

				String postJsonData = objectMapper.writeValueAsString(caseInput);
				logger.info("postJsonData :{}", postJsonData);

				// Send post request
				con.setDoOutput(true);
				DataOutputStream wr = new DataOutputStream(con.getOutputStream());
				wr.writeBytes(postJsonData);
				wr.flush();
				wr.close();
				caseOutput = objectMapper.readValue(con.getInputStream(), CreateCaseResponse.class);
				if (caseOutput != null) {
					String id = caseOutput.getId();
					caseOutput.setId(id);
					logger.info("Case created succesfully, Case Id from Salesforce {}: for member: {}", id, input.getMemberId());
				}
			} catch (Exception e) {
				logger.error("Error received while creating Case in Salesforce for member: {}, error: {}", input.getMemberId(), e.getMessage());
			}
		}
		return caseOutput;
	}
	
	@Override
	public CreateCaseResponse createCaseV2(FreezeMembershipInputV2 input, String eClubId, Double freezeFee, boolean createCaseFlag) {
		logger.info("Creating case in Engage for member: {}, source: {}", input.getMosoId(), input.getSource());
		CreateCaseResponse caseOutput = null;
		AuthTokenData tokenData = getSfdcSecurityToken();
		if (tokenData == null) {
			logger.error("API error :: failed to get SfdcSecurityToken {}");
			return caseOutput;
		}
		logger.debug("auth data from sfdc {} ", tokenData);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		String noteUrl = tokenData.getInstanceUrl() + "/services/data/v43.0/sobjects/case";
		//Date date = Calendar.getInstance().getTime();
		String status = "";
		String recordTypeId = "";
		Case caseInput = new Case();
		caseInput.setId(null);
		String clubId = getClubId(eClubId);
		logger.debug("Club :{}", clubId);
		String contactId = getContactId(input.getMosoId());
		caseInput.setClub(clubId);
		caseInput.setContactId(contactId);
		if (createCaseFlag == false) {
			status = "Action";
			recordTypeId = "012120000012dEk";
			//caseInput.setIsException(false);
			caseInput.setDescription("FreezeMembership case creation");
		} else {
			status = "Resolved";
			recordTypeId = "012120000012dEl";
			//caseInput.setIsException(false);
			caseInput.setFreezeFee(freezeFee);
			caseInput.setDescription(ENGAGE_FREEZE_CASE_DESCRIPTION);
		}
		caseInput.setIsException(false);
		caseInput.setStatus(status);
		caseInput.setDoNotContact(true);
		caseInput.setDuration(String.valueOf(input.getDurationMonths()));
		caseInput.setSubject(ENGAGE_FREEZE_CASE_SUBJECT);
		caseInput.setRecordTypeId(recordTypeId);
		// caseInput.setFeedbackSource(feedbackSource);
		caseInput.setFreezeEndDate(input.getEndDate());
		if (input.getFreezeFees() != null) {
			caseInput.setFreezeFee(input.getFreezeFees().doubleValue());
		}
		caseInput.setFreezeReason(input.getFreezeReasonId());
		caseInput.setFreezeStartDate(input.getStartDate());
		caseInput.setMemberId(input.getMosoId());
		if(input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
			caseInput.setOrigin("Equinox.com");
		}else {
			caseInput.setOrigin("Club (Front Desk)");
		}
		
		//overriding the 'origin=source' with 'origin=caseOrigin' for chatbot, JIRA# WEB-4183, NMNM-3151
		if(!StringUtils.isEmpty(input.getCaseOrigin())) {
			caseInput.setOrigin(input.getCaseOrigin());
		}

		if (input.isCancelFlow() && input.isUniversityMember() && input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			caseInput.setAlternateReason(FreezeMembershipConstants.UNIVERSITY_FREEZE);
		}

		if (!input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)
			&& input.isWaveFreezeFee() && input.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE)) {
			caseInput.setIsException(true);
		}

		logger.debug("Contact Id :{}", contactId);
		long startTime = System.currentTimeMillis();
		try {
			logger.debug("noteUrl" + noteUrl);
			URL obj = new URL(noteUrl);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			ObjectMapper objectMapper = new ObjectMapper();

			// Setting basic post request
			con.setRequestMethod("POST");
			con.setReadTimeout(60 * 1000);
			con.setConnectTimeout(60 * 1000);
			con.setRequestProperty("Authorization", authorization);
			con.setRequestProperty("Content-Type", "application/json");

			String postJsonData = objectMapper.writeValueAsString(caseInput);
			logger.debug("postJsonData :{}", postJsonData);

			// Send post request
			con.setDoOutput(true);
			DataOutputStream wr = new DataOutputStream(con.getOutputStream());
			wr.writeBytes(postJsonData);
			wr.flush();
			wr.close();
			caseOutput = objectMapper.readValue(con.getInputStream(), CreateCaseResponse.class);
			if (caseOutput != null) {
				String id = caseOutput.getId();
				logger.info("Successfully created Case in Engage for member: {}, Case Status: {}, Case Id: {}", input.getMosoId(), status, id);
				eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+input.getMosoId(), "salesforce.createcase."+input.getMosoId(), "", 
						input.getCorrelationId(), caseInput, caseOutput, HttpStatus.OK, System.currentTimeMillis()-startTime);
			}
		} catch (Exception e) {
			logger.info("Failed to created Case in Engage for member: {}, Case Status: {}, error: {}", input.getMosoId(), status, e.getMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+input.getMosoId(), "salesforce.createcase."+input.getMosoId(), "", 
					input.getCorrelationId(), caseInput, e.getMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
		}
		return caseOutput;
	}

	@Override
	public CreateCaseResponse createCaseV3(FreezeMembershipInputV3 input, String eClubId, Double freezeFee,Date endDate) {
		// TODO Auto-generated method stub
		CreateCaseResponse caseOutput = null;
		if (!StringUtils.isBlank(input.getSource()) && input.getSource().equalsIgnoreCase("Web")) {

			AuthTokenData tokenData = getSfdcSecurityToken();
			if (tokenData == null) {
				logger.error("API error :: failed to get SfdcSecurityToken {}");
				return caseOutput;
			}
			logger.debug("auth data from sfdc {} ", tokenData);
			String authorization = "OAuth" + " " + tokenData.getAccessToken();
			logger.debug("authorization {} ", authorization);
			String noteUrl = tokenData.getInstanceUrl() + "/services/data/v43.0/sobjects/case";
			//Date date = Calendar.getInstance().getTime();
			String status = "";
			String recordTypeId = "";
			Case caseInput = new Case();
			caseInput.setId(null);
			String clubId = getClubId(eClubId);
			logger.debug("Club :{}", clubId);
			String contactId = getContactId(input.getMosoId());
			caseInput.setClub(clubId);
			caseInput.setContactId(contactId);
			if (input.getSource().equalsIgnoreCase("Web") && (input.getFreezeReason().equalsIgnoreCase("Medical")
					|| input.getFreezeReason().equalsIgnoreCase("Pregnancy"))) {
				status = "Action";
				recordTypeId = "012120000012dEk";
				caseInput.setIsException(true);
			} else {
				status = "Resolved";
				recordTypeId = "012120000012dEl";
				caseInput.setIsException(false);
				caseInput.setFreezeFee(freezeFee);
			}
			caseInput.setStatus(status);
			caseInput.setDescription(ENGAGE_FREEZE_CASE_DESCRIPTION);
			caseInput.setDoNotContact(true);
			caseInput.setDuration("1");
			caseInput.setSubject("Membership Freeze");
			caseInput.setRecordTypeId(recordTypeId);
			// caseInput.setFeedbackSource(feedbackSource);
			caseInput.setFreezeEndDate(endDate);
			caseInput.setFreezeReason(input.getFreezeReason());
			caseInput.setFreezeStartDate(input.getClubOpenDate());
			caseInput.setMemberId(input.getMosoId());
			caseInput.setOrigin("Equinox.com");

			logger.debug("Contact Id :{}", contactId);
			long startTime = System.currentTimeMillis();
			try {
				logger.debug("noteUrl" + noteUrl);
				URL obj = new URL(noteUrl);
				HttpURLConnection con = (HttpURLConnection) obj.openConnection();
				ObjectMapper objectMapper = new ObjectMapper();

				// Setting basic post request
				con.setRequestMethod("POST");
				con.setReadTimeout(60 * 1000);
				con.setConnectTimeout(60 * 1000);
				con.setRequestProperty("Authorization", authorization);
				con.setRequestProperty("Content-Type", "application/json");

				String postJsonData = objectMapper.writeValueAsString(caseInput);
				logger.debug("postJsonData :{}", postJsonData);

				// Send post request
				con.setDoOutput(true);
				DataOutputStream wr = new DataOutputStream(con.getOutputStream());
				wr.writeBytes(postJsonData);
				wr.flush();
				wr.close();
				caseOutput = objectMapper.readValue(con.getInputStream(), CreateCaseResponse.class);
				if (caseOutput != null) {
					String id = caseOutput.getId();
					logger.debug("token get from sfdc {}", id);
					eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+input.getMosoId(), "salesforce.createcase."+input.getMosoId(), "", 
							input.getCorrelationId(), caseInput, caseOutput, HttpStatus.OK, System.currentTimeMillis()-startTime);
				}
			} catch (Exception e) {
				logger.error("Error {}", e.getMessage());
				eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+input.getMosoId(), "salesforce.createcase."+input.getMosoId(), "", 
						input.getCorrelationId(), caseInput, e.getMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
			}
		}
		return caseOutput;
	}
	
	@Override
	public CreateCaseResponse createCaseForUnfreeze(String clubId, String contactId, String memberId)  {
		CreateCaseResponse caseOutput = null;
		
		AuthTokenData tokenData = getSfdcSecurityToken();
		if (tokenData == null) {
			logger.error("API error :: failed to get SfdcSecurityToken {}");
			return caseOutput;
		}
		logger.debug("auth data from sfdc {} ", tokenData);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		String noteUrl = tokenData.getInstanceUrl() + "/services/data/v43.0/sobjects/case";
		
		UnFreezeCaseInput caseInput = new UnFreezeCaseInput();
		
		caseInput.setId(null);
		caseInput.setContactId(contactId);
		caseInput.setClubId(getClubId(clubId));
		caseInput.setMemberId(memberId);
		caseInput.setRecordTypeId(FreezeMembershipConstants.UNFREEZE_RECORD_TYPE_ID);
		caseInput.setStatus(FreezeMembershipConstants.UNFREEZE_CASE_STATUS);
		caseInput.setCancellationType(FreezeMembershipConstants.UNFREEZE_CANCELLATION_TYPE);
		caseInput.setOrigin(FreezeMembershipConstants.UNFREEZE_CASE_ORIGIN);
		caseInput.setSubject(FreezeMembershipConstants.UNFREEZE_CASE_SUBJECT);
		caseInput.setDescription(FreezeMembershipConstants.UNFREEZE_CASE_DESCRIPTION);
		caseInput.setCategories(FreezeMembershipConstants.UNFREEZE_CASE_CATEGORIES);
		caseInput.setSubCategories(FreezeMembershipConstants.UNFREEZE_CASE_SUB_CATEGORIES);
		caseInput.setDateOfIncident(ZonedDateTime.now(ZoneId.of("UTC")).toString());
		
		long startTime = System.currentTimeMillis();
		
		try {
			logger.debug("noteUrl {}",noteUrl);
			URL obj = new URL(noteUrl);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			ObjectMapper objectMapper = new ObjectMapper();

			// Setting basic post request
			con.setRequestMethod("POST");
			con.setReadTimeout(60 * 1000);
			con.setConnectTimeout(60 * 1000);
			con.setRequestProperty("Authorization", authorization);
			con.setRequestProperty("Content-Type", "application/json");

			String postJsonData = objectMapper.writeValueAsString(caseInput);
			logger.debug("postJsonData :{}", postJsonData);

			// Send post request
			con.setDoOutput(true);
			DataOutputStream wr = new DataOutputStream(con.getOutputStream());
			wr.writeBytes(postJsonData);
			wr.flush();
			wr.close();
			caseOutput = objectMapper.readValue(con.getInputStream(), CreateCaseResponse.class);
			if (caseOutput != null) {
				String id = caseOutput.getId();
				logger.debug("token get from sfdc {}", id);
				eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+memberId, "salesforce.createcase."+memberId, "", 
						"", caseInput, caseOutput, HttpStatus.OK, System.currentTimeMillis()-startTime);
			}
		} catch (Exception e) {
			logger.error("Error in creating unfreeze case {}", e.getMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+memberId, "salesforce.createcase."+memberId, "", 
					"", caseInput, e.getMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
		}
		
		return caseOutput;
	}

	@Override
	public String getMemberId(String salesForceId) {
		String token = null;
		String memberId = "";
		AuthTokenData tokenData = getSfdcSecurityToken();
		logger.debug("auth data from sfdc {} ", tokenData);
		if (tokenData == null) {
			return memberId;
		}
		String getContactIdUrl = tokenData.getInstanceUrl()
				+ "/services/data/v43.0/query/?q=select+moso_id__c+FROM+Contact+where+id+='"
				+ salesForceId + "'";
		logger.debug("getOpportunityUrl url {} ", getContactIdUrl);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		ContactData data = null;

		try {
			URL object = new URL(getContactIdUrl);

			HttpURLConnection connection = (HttpURLConnection) object.openConnection();
			// int timeOut = connection.getReadTimeout();
			connection.setReadTimeout(60 * 100);
			// connection.setConnectTimeout(60 * 1000);
			connection.setConnectTimeout(60 * 1000);
			connection.setRequestProperty("Authorization", authorization);
			connection.setRequestProperty("Content-Type", "application/json");

			// logger.debug(this.read(connection.getInputStream()));
			ObjectMapper objectMapper = new ObjectMapper();
			data = objectMapper.readValue(connection.getInputStream(), ContactData.class);

			if (data != null && data.getRecords() != null && data.getRecords().size() > 0) {
				logger.debug("Record Size {}", data.getRecords().size());

				memberId = data.getRecords().get(0).getMosoId();
				logger.debug("MemberId {} ", memberId);
			}
			return memberId;
		} catch (IOException e) {

			logger.error("error while interacting with sfdc {}", token);
			logger.error(e.getMessage());
		}
		return memberId;
	}

	/* (non-Javadoc)
	 * To Link the Freeze Medical Document in Salesforce, store the data in 'ContentVersion' -> /services/data/v43.0/sobjects/ContentVersion
	 */
	@Override
	public CreateCaseResponse storeFreezeDocumentinSF(FreezeMembershipInput freezeMembershipInput, String memberName, String caseId) {
		logger.info("Storing Freeze document data in Salesforce for Member: {}", freezeMembershipInput.getMemberId());
		CreateCaseResponse contentVersionResponse = new CreateCaseResponse();
		OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
		String currentDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		
		if(Objects.nonNull(freezeMembershipInput.getFreezeDocuments()) && !freezeMembershipInput.getFreezeDocuments().isEmpty()) {
			for(FreezeDocuments freezeDocument : freezeMembershipInput.getFreezeDocuments()) {
				String documentType = StringUtils.substringAfter(freezeDocument.getType(), "_"); //Expected format from payload is 'base64_pdf' or 'base64_png'
				String title = MessageFormat.format("{0}_{1}#{2}.{3}", memberName, freezeMembershipInput.getMemberId(), currentDate, documentType);
				String documentData = freezeDocument.getData();
				
				ContentVersionSFRequest contentVersionSFRequest = prepareContentVersionSFData(title, documentData, freezeMembershipInput.getFacilityId(), caseId);
				
				Wrapper<CreateCaseResponse> contentVersionSFResponse = salesforceApiService.post(DynamoPK.MEMBER_ID+freezeMembershipInput.getMemberId(), "ContentVersion", contentVersionSFRequest, CreateCaseResponse.class);
				
				if(Objects.nonNull(contentVersionSFResponse)) {
					if(Objects.nonNull(contentVersionSFResponse.getData()) && !StringUtils.isEmpty(contentVersionSFResponse.getData().getId())) {
						String documentId = contentVersionSFResponse.getData().getId();
						logger.info("Freeze document data successfully saved in Salesforce for Member: {}, Content version Id: {}", freezeMembershipInput.getMemberId(), documentId);
						
						contentVersionResponse.setId(documentId);
						contentVersionResponse.setSuccess(true);
					}
					if(Objects.nonNull(contentVersionSFResponse.getMessage())) {
						logger.error("Unable to save the Freeze document data in Salesforce for Member: {}, error: {}", freezeMembershipInput.getMemberId(), contentVersionSFResponse.getMessage());
						contentVersionResponse.setSuccess(false);
					}
				}else {
					logger.error("Unable to save the Freeze document data in Salesforce for Member: {}, empty response received from Salesforce", freezeMembershipInput.getMemberId());
					contentVersionResponse.setSuccess(false);
				}
			}
		}else {
			contentVersionResponse.setSuccess(false);
		}
		
		return contentVersionResponse;
	}
	
	private ContentVersionSFRequest prepareContentVersionSFData(String title, String documentData, String facilityId, String caseId) {
		ContentVersionSFRequest contentVersionSFRequest = new ContentVersionSFRequest();
		
		contentVersionSFRequest.setFirstPublishLocationId(contentversionFreezeFolderId);
		contentVersionSFRequest.setPathOnClient(title);
		contentVersionSFRequest.setTitle(title);
		contentVersionSFRequest.setVersionData(documentData);
		contentVersionSFRequest.setCaseId(caseId);
		
		return contentVersionSFRequest;
	}

	@Override
	public CreateCaseResponse storeFreezeDocumentinSF(FreezeMembershipInputV2 freezeMembershipInput, String memberName, String caseId) {
		logger.info("Storing Freeze document data in Salesforce for Member: {}", freezeMembershipInput.getMosoId());
		CreateCaseResponse contentVersionResponse = new CreateCaseResponse();
		OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
		String currentDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		
		if(Objects.nonNull(freezeMembershipInput.getFreezeDocuments()) && !freezeMembershipInput.getFreezeDocuments().isEmpty()) {
			for(FreezeDocuments freezeDocument : freezeMembershipInput.getFreezeDocuments()) {
				String documentType = StringUtils.substringAfter(freezeDocument.getType(), "_"); //Expected format from payload is 'base64_pdf' or 'base64_png'
				String title = MessageFormat.format("{0}_{1}#{2}.{3}", memberName, freezeMembershipInput.getMosoId(), currentDate, documentType);
				String documentData = freezeDocument.getData();
				
				ContentVersionSFRequest contentVersionSFRequest = prepareContentVersionSFData(title, documentData, freezeMembershipInput.getFacilityId(), caseId);
				
				Wrapper<CreateCaseResponse> contentVersionSFResponse = salesforceApiService.post(DynamoPK.MEMBER_ID+freezeMembershipInput.getMosoId(), "ContentVersion", contentVersionSFRequest, CreateCaseResponse.class);
				
				if(Objects.nonNull(contentVersionSFResponse)) {
					if(Objects.nonNull(contentVersionSFResponse.getData()) && !StringUtils.isEmpty(contentVersionSFResponse.getData().getId())) {
						String documentId = contentVersionSFResponse.getData().getId();
						logger.info("Freeze document data successfully saved in Salesforce for Member: {}, Content version Id: {}", freezeMembershipInput.getMosoId(), documentId);
						
						contentVersionResponse.setId(documentId);
						contentVersionResponse.setSuccess(true);
					}
					if(Objects.nonNull(contentVersionSFResponse.getMessage())) {
						logger.error("Unable to save the Freeze document data in Salesforce for Member: {}, error: {}", freezeMembershipInput.getMosoId(), contentVersionSFResponse.getMessage());
						contentVersionResponse.setSuccess(false);
					}
				}else {
					logger.error("Unable to save the Freeze document data in Salesforce for Member: {}, empty response received from Salesforce", freezeMembershipInput.getMosoId());
					contentVersionResponse.setSuccess(false);
				}
			}
		}else {
			contentVersionResponse.setSuccess(false);
		}
		
		return contentVersionResponse;
	}
	

	@Override
	public TaskOutput createEmailTaskForFreezeConfirmation(String mosoId, String firstName, String countryCode,
			String startDate, String endDate, BigDecimal freezeFees, int duration, boolean isInObligationFlag, CreateCaseResponse createCaseResponse, boolean emailsent, String source, boolean isHoldBillingInObFlag, boolean isPIF, String freezeReason) {
		logger.info("Creating Email Task For Freeze in Engage for member: {}", mosoId);
		String contactId = getContactId(mosoId);
		String emailBody = getEmailBody(mosoId, firstName, countryCode, startDate, endDate, freezeFees.toString(), String.valueOf(duration), isInObligationFlag, source, isHoldBillingInObFlag, isPIF, freezeReason);
		// String sfdcResponse = freezeHelper.createNoteInEngage(mosoId, firstName, contactId, notes, EMAIL_NOTE_TITLE);
		
		TaskOutput taskResponse = null;
		AuthTokenData tokenData = getSfdcSecurityToken();
		if (tokenData == null) {
			logger.error("API error :: failed to get SfdcSecurityToken {}");
			return taskResponse;
		}
		
		Wrapper<ContactData> contactData = null;
		if(StringUtils.isEmpty(firstName)) {
			logger.info("Entering Salesforce common-lib to perform get() call for the first name for memberId: "+mosoId);
			contactData = salesforceApiService.get(DynamoPK.MEMBER_ID+mosoId,"Select firstname from Contact WHERE id='"+contactId+"'", ContactData.class);
			
			if(contactData == null || contactData.getData()==null || contactData.getData().getTotalSize() == 0) {
				logger.info("Unable to fecth first name for the give contact: "+contactId);
				return null;
			}
			if(contactData.getData().getRecords().get(0).getFirstName() != null) {
				firstName = contactData.getData().getRecords().get(0).getFirstName();
				logger.info("Got the FirstName from Salesforce/Engage: "+firstName);
			}
		}
		
		logger.debug("auth data from sfdc {} ", tokenData);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		String taskUrl = tokenData.getInstanceUrl() + "/services/data/v50.0/sobjects/Task";
		String status = "";
		String emailSubject = EMAIL_NOTE_TITLE + " " + firstName;
		if(emailsent)
			status = COMPLETED;
		
		Task taskInput = new Task();
		taskInput.setContactId(contactId);
		if(Objects.isNull(createCaseResponse))
			taskInput.setCaseId(null);
		taskInput.setEmailSubject(emailSubject);
		taskInput.setEmailBody(emailBody);
		taskInput.setStatus(status);
		taskInput.setActivityDate(new Date());
		taskInput.setActivityType(EMAIL);
		taskInput.setPriority(PRIORITY);
		long startTime = System.currentTimeMillis();
		String correlationId = UUID.randomUUID().toString();
		try {
			logger.debug("noteUrl" + taskUrl);
			URL obj = new URL(taskUrl);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			ObjectMapper objectMapper = new ObjectMapper();

			// Setting basic post request
			con.setRequestMethod("POST");
			con.setReadTimeout(60 * 1000);
			con.setConnectTimeout(60 * 1000);
			con.setRequestProperty("Authorization", authorization);
			con.setRequestProperty("Content-Type", "application/json");

			String postJsonData = objectMapper.writeValueAsString(taskInput);
			logger.debug("postJsonData :{}", postJsonData);

			// Send post request
			con.setDoOutput(true);
			DataOutputStream wr = new DataOutputStream(con.getOutputStream());
			wr.writeBytes(postJsonData);
			wr.flush();
			wr.close();
			taskResponse = objectMapper.readValue(con.getInputStream(), TaskOutput.class);
			if (taskResponse != null) {
				String taskId = taskResponse.getTaskId();
				logger.info("Successfully created Task in Engage for member: {}, Case Status: {}, Task Id: {}", mosoId, status, taskId);
				eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "salesforce.createTask."+mosoId, "", 
						correlationId, taskInput, taskResponse, HttpStatus.OK, System.currentTimeMillis()-startTime);
			}
		} catch (Exception e) {
			logger.info("Failed to created Task in Engage for member: {}, Task Status: {}, error: {}", mosoId, status, e.getMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "salesforce.createTask."+mosoId, "", 
					correlationId, taskInput, e.getMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
		}
		return taskResponse;
	}

	@Override
	public TaskOutput createEmailTaskForFreezeConfirmationV1(String mosoId, String firstName, String countryCode,
															 String startDate, String endDate, BigDecimal freezeFees, int duration, boolean isInObligationFlag, CreateCaseResponse createCaseResponse, boolean emailsent, String source, boolean isHoldBillingInObFlag, boolean isPIF, String freezeReason) {
		logger.info("Creating Email Task For Freeze in Engage for member: {}", mosoId);
		String contactId = getContactId(mosoId);
		// convert freeze fees to 2 decimal places
		freezeFees = freezeFees.setScale(2, RoundingMode.HALF_UP);
		String currencyCode = StringUtils.isEmpty(countryCode) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(countryCode);
		String emailBody = getEmailBodyV1(mosoId, firstName, currencyCode, startDate, endDate, freezeFees.toString(), String.valueOf(duration), isInObligationFlag, source, isHoldBillingInObFlag, isPIF, freezeReason);
		// String sfdcResponse = freezeHelper.createNoteInEngage(mosoId, firstName, contactId, notes, EMAIL_NOTE_TITLE);

		TaskOutput taskResponse = null;
		AuthTokenData tokenData = getSfdcSecurityToken();
		if (tokenData == null) {
			logger.error("API error :: failed to get SfdcSecurityToken {}");
			return taskResponse;
		}

		Wrapper<ContactData> contactData = null;
		if(StringUtils.isEmpty(firstName)) {
			logger.info("Entering Salesforce common-lib to perform get() call for the first name for memberId: "+mosoId);
			contactData = salesforceApiService.get(DynamoPK.MEMBER_ID+mosoId,"Select firstname from Contact WHERE id='"+contactId+"'", ContactData.class);

			if(contactData == null || contactData.getData()==null || contactData.getData().getTotalSize() == 0) {
				logger.info("Unable to fecth first name for the give contact: "+contactId);
				return null;
			}
			if(contactData.getData().getRecords().get(0).getFirstName() != null) {
				firstName = contactData.getData().getRecords().get(0).getFirstName();
				logger.info("Got the FirstName from Salesforce/Engage: "+firstName);
			}
		}

		logger.debug("auth data from sfdc {} ", tokenData);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		String taskUrl = tokenData.getInstanceUrl() + "/services/data/v50.0/sobjects/Task";
		String status = "";
		String emailSubject = EMAIL_NOTE_TITLE + " " + firstName;
		if(emailsent)
			status = COMPLETED;

		Task taskInput = new Task();
		taskInput.setContactId(contactId);
		if(Objects.isNull(createCaseResponse))
			taskInput.setCaseId(null);
		taskInput.setEmailSubject(emailSubject);
		taskInput.setEmailBody(emailBody);
		taskInput.setStatus(status);
		taskInput.setActivityDate(new Date());
		taskInput.setActivityType(EMAIL);
		taskInput.setPriority(PRIORITY);
		long startTime = System.currentTimeMillis();
		String correlationId = UUID.randomUUID().toString();
		try {
			logger.debug("noteUrl" + taskUrl);
			URL obj = new URL(taskUrl);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			ObjectMapper objectMapper = new ObjectMapper();

			// Setting basic post request
			con.setRequestMethod("POST");
			con.setReadTimeout(60 * 1000);
			con.setConnectTimeout(60 * 1000);
			con.setRequestProperty("Authorization", authorization);
			con.setRequestProperty("Content-Type", "application/json");

			String postJsonData = objectMapper.writeValueAsString(taskInput);
			logger.debug("postJsonData :{}", postJsonData);

			// Send post request
			con.setDoOutput(true);
			DataOutputStream wr = new DataOutputStream(con.getOutputStream());
			wr.writeBytes(postJsonData);
			wr.flush();
			wr.close();
			taskResponse = objectMapper.readValue(con.getInputStream(), TaskOutput.class);
			if (taskResponse != null) {
				String taskId = taskResponse.getTaskId();
				logger.info("Successfully created Task in Engage for member: {}, Case Status: {}, Task Id: {}", mosoId, status, taskId);
				eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "salesforce.createTask."+mosoId, "",
						correlationId, taskInput, taskResponse, HttpStatus.OK, System.currentTimeMillis()-startTime);
			}
		} catch (Exception e) {
			logger.info("Failed to created Task in Engage for member: {}, Task Status: {}, error: {}", mosoId, status, e.getMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "salesforce.createTask."+mosoId, "",
					correlationId, taskInput, e.getMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
		}
		return taskResponse;
	}

	private String getEmailBody(String mosoId, String firstName, String countryCode, String startDate, String endDate,
			String freezeFees, String duration, boolean isInObligationFlag, String source, boolean isHoldBillingInObFlag, boolean isPIF, String freezeReason) {
		String emailNote = "";
		String currencyCode = StringUtils.isEmpty(countryCode) ? FreezeMembershipConstants.COUNTRY_US : countryCurrencyMap.get(countryCode);
		if(source.equalsIgnoreCase("Engage") && (freezeReason.equalsIgnoreCase("Medical") || freezeReason.equalsIgnoreCase("Pregnancy"))) {
			// format data for UK club
			String formattedStartDate = formatFreezeDate(startDate, countryCode);
			String formattedEndDate = formatFreezeDate(endDate, countryCode);
			if (freezeFees !=null && Double.parseDouble(freezeFees) > 0) {
				emailNote = getMessage(MEDICAL_PREGNANCY_FREEZE_CNF_EMAIL_TO_MEMBER_WITH_FEE, firstName, formattedStartDate, formattedStartDate, duration, formattedEndDate, currencyCode, freezeFees);
			} else {
				emailNote = getMessage(MEDICAL_PREGNANCY_FREEZE_CNF_EMAIL_TO_MEMBER, firstName, formattedStartDate, formattedStartDate, duration, formattedEndDate);
			}
			return emailNote;
		}
		if(source.equalsIgnoreCase("Engage") && isPIF) {
			emailNote = getMessage(FREEZE_CNF_EMAIL_TO_PIF, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
			return emailNote;
		}
		if(source.equalsIgnoreCase("Engage") && isHoldBillingInObFlag && isInObligationFlag) {
			emailNote = getMessage(FREEZE_CNF_EMAIL_OUTOFOB, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
			return emailNote;
		}

		if(isInObligationFlag) {
			emailNote = getMessage(FREEZE_CNF_EMAIL_INOB, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
		} else {
			emailNote = getMessage(FREEZE_CNF_EMAIL_OUTOFOB, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
		}
		logger.info("email body {}", emailNote);
		return emailNote;
	}

	private String formatFreezeDate(String freezeDate, String countryCode) {
		if (countryCode != null && countryCode.equalsIgnoreCase(FreezeMembershipConstants.COUNTRY_UK)) {
			// consider date format DD/MM/YYYY
			SimpleDateFormat inputFormat = new SimpleDateFormat("MM/dd/yyyy");
			try {
				Date date = inputFormat.parse(freezeDate);
				SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
				return outputFormat.format(date);
			} catch (ParseException e) {
				logger.error("Error parsing start date: {} - {}", freezeDate, e.getMessage());
			}
		}
		return freezeDate;
	}

	private String getEmailBodyV1(String mosoId, String firstName, String currencyCode, String startDate, String endDate,
								String freezeFees, String duration, boolean isInObligationFlag, String source, boolean isHoldBillingInObFlag, boolean isPIF, String freezeReason) {
		String emailNote = "";
		if(source.equalsIgnoreCase("Engage") && (freezeReason.equalsIgnoreCase("Medical") || freezeReason.equalsIgnoreCase("Pregnancy"))) {
			if (freezeFees !=null && Double.parseDouble(freezeFees) > 0) {
				emailNote = getMessage(MEDICAL_PREGNANCY_FREEZE_CNF_EMAIL_TO_MEMBER_WITH_FEE, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
			} else {
				emailNote = getMessage(MEDICAL_PREGNANCY_FREEZE_CNF_EMAIL_TO_MEMBER, firstName, startDate, startDate, duration, endDate);
			}
			return emailNote;
		}
		if(source.equalsIgnoreCase("Engage") && isPIF) {
			emailNote = getMessage(FREEZE_CNF_EMAIL_TO_PIF_DAYS_FLOW, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
			return emailNote;
		}
		if(source.equalsIgnoreCase("Engage") && isHoldBillingInObFlag && isInObligationFlag) {
			emailNote = getMessage(FREEZE_CNF_EMAIL_OUTOFOB_DAYS_FLOW, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
			return emailNote;
		}
		if(isInObligationFlag) {
			emailNote = getMessage(FREEZE_CNF_EMAIL_INOB_DAYS_FLOW, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
		} else {
			emailNote = getMessage(FREEZE_CNF_EMAIL_OUTOFOB_DAYS_FLOW, firstName, startDate, startDate, duration, endDate, currencyCode, freezeFees);
		}
		logger.info("email body {}", emailNote);
		return emailNote;
	}
	
	public String getMessage(String code, Object... args) {
		return messageSource.getMessage(code, args, LocaleContextHolder.getLocale());
	}

	@Override
	public TaskOutput createEmailTaskForSendFreezeEmailLink(FreezeEmailInput freezeEmailInput, String source, String firstName) {
		String mosoId = freezeEmailInput.getMemberId();
		String contactId = getContactId(mosoId);
		if(source.equalsIgnoreCase("clubapp"))			
			source="Club App";
		else 
			source="Engage";
		String emailBody = getEmailBody(source, firstName);
		TaskOutput taskResponse = null;
		AuthTokenData tokenData = getSfdcSecurityToken();
		if (tokenData == null) {
			logger.error("API error :: failed to get SfdcSecurityToken {}");
			return taskResponse;
		}		
		Wrapper<ContactData> contactData = null;
		if(StringUtils.isEmpty(firstName)) {
			logger.info("Entering Salesforce common-lib to perform get() call for the first name for memberId: "+mosoId);
			contactData = salesforceApiService.get(DynamoPK.MEMBER_ID+mosoId,"Select firstname from Contact WHERE id='"+contactId+"'", ContactData.class);

			if(contactData == null || contactData.getData()==null || contactData.getData().getTotalSize() == 0) {
				logger.info("Unable to fecth first name for the give contact: "+contactId);
				return null;
			}
			if(contactData.getData().getRecords().get(0).getFirstName() != null) {
				firstName = contactData.getData().getRecords().get(0).getFirstName();
				logger.info("Got the FirstName from Salesforce/Engage: "+firstName);
			}
		}

		logger.debug("auth data from sfdc {} ", tokenData);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		String taskUrl = tokenData.getInstanceUrl() + "/services/data/v50.0/sobjects/Task";
		String status = COMPLETED;
		String emailSubject = SEND_FREEZE_MAIL_LINK_NOTE_TITLE + " " + firstName;		
		Task taskInput = new Task();
		taskInput.setContactId(contactId);		
		taskInput.setCaseId(null);
		taskInput.setEmailSubject(emailSubject);
		taskInput.setEmailBody(emailBody);
		taskInput.setStatus(status);
		taskInput.setActivityDate(new Date());
		taskInput.setActivityType(EMAIL);
		taskInput.setPriority(PRIORITY);
		
		//NMNM-3105: Add 'ownerId' to Send Freeze Link API payload for Chatbot
		if(StringUtils.isNotBlank(freezeEmailInput.getOwnerId()))
			taskInput.setOwnerId(freezeEmailInput.getOwnerId());
		
		long startTime = System.currentTimeMillis();
		String correlationId = UUID.randomUUID().toString();
		try {
			logger.debug("task Url" + taskUrl);
			URL obj = new URL(taskUrl);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			ObjectMapper objectMapper = new ObjectMapper();

			// Setting basic post request
			con.setRequestMethod("POST");
			con.setReadTimeout(60 * 1000);
			con.setConnectTimeout(60 * 1000);
			con.setRequestProperty("Authorization", authorization);
			con.setRequestProperty("Content-Type", "application/json");

			String postJsonData = objectMapper.writeValueAsString(taskInput);
			logger.debug("postJsonData :{}", postJsonData);

			// Send post request
			con.setDoOutput(true);
			DataOutputStream wr = new DataOutputStream(con.getOutputStream());
			wr.writeBytes(postJsonData);
			wr.flush();
			wr.close();
			taskResponse = objectMapper.readValue(con.getInputStream(), TaskOutput.class);
			if (taskResponse != null) {
				String taskId = taskResponse.getTaskId();
				logger.info("Successfully created Task in Engage for member: {}, Case Status: {}, Task Id: {}", mosoId, status, taskId);
				eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "salesforce.createTask."+mosoId, "", 
						correlationId, taskInput, taskResponse, HttpStatus.OK, System.currentTimeMillis()-startTime);
			}
		} catch (Exception e) {
			logger.info("Failed to created Task in Engage for member: {}, Task Status: {}, error: {}", mosoId, status, e.getMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+mosoId, "salesforce.createTask."+mosoId, "", 
					correlationId, taskInput, e.getMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
		}
		return taskResponse;

	}

	private String getEmailBody(String source, String firstName) {
		String emailNote = getMessage(FREEZE_EMAIL_LINK_TO_MEMBER, source, firstName);
		logger.info("send freeze email link body {}", emailNote);
		return emailNote;
	}

	@Override
	public CreateCaseResponse createCaseV4(FreezeMembershipInputV4 input, String eClubId, Double freezeFee,
			boolean createCaseFlag) {
		logger.info("Creating case in Engage for member: {}, source: {}", input.getMosoId(), input.getSource());
		CreateCaseResponse caseOutput = null;
		AuthTokenData tokenData = getSfdcSecurityToken();
		if (tokenData == null) {
			logger.error("API error :: failed to get SfdcSecurityToken {}");
			return caseOutput;
		}
		logger.debug("auth data from sfdc {} ", tokenData);
		String authorization = "OAuth" + " " + tokenData.getAccessToken();
		logger.debug("authorization {} ", authorization);
		String noteUrl = tokenData.getInstanceUrl() + "/services/data/v43.0/sobjects/case";
		//Date date = Calendar.getInstance().getTime();
		String status = "";
		String recordTypeId = "";
		Case caseInput = new Case();
		caseInput.setId(null);
		String clubId = getClubId(eClubId);
		logger.debug("Club :{}", clubId);
		String contactId = getContactId(input.getMosoId());
		caseInput.setClub(clubId);
		caseInput.setContactId(contactId);
		if (createCaseFlag == false) {
			status = "Action";
			recordTypeId = "012120000012dEk";
			//caseInput.setIsException(false);
			caseInput.setDescription("FreezeMembership case creation");
		} else {
			status = "Resolved";
			recordTypeId = "012120000012dEl";
			//caseInput.setIsException(false);
			caseInput.setFreezeFee(freezeFee);
			caseInput.setDescription(ENGAGE_FREEZE_CASE_DESCRIPTION);
		}
		caseInput.setIsException(false);
		caseInput.setStatus(status);
		caseInput.setDoNotContact(true);
		if (input.getDurationDays() > 0) {
			caseInput.setFreezeDurationDays(String.valueOf(input.getDurationDays()));
		} else if (input.getDurationMonths() > 0) {
			caseInput.setDuration(String.valueOf(input.getDurationMonths()));
		}
		caseInput.setSubject(ENGAGE_FREEZE_CASE_SUBJECT);
		caseInput.setRecordTypeId(recordTypeId);
		// caseInput.setFeedbackSource(feedbackSource);
		caseInput.setFreezeEndDate(input.getEndDate());
		if (input.getFreezeFees() != null) {
			caseInput.setFreezeFee(input.getFreezeFees().doubleValue());
		}
		caseInput.setFreezeReason(input.getFreezeReasonId());
		caseInput.setFreezeStartDate(input.getStartDate());
		caseInput.setMemberId(input.getMosoId());
		if(!input.isCancelFlow() && input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
			caseInput.setOrigin(FreezeMembershipConstants.EQUINOX);
		} else if (input.isCancelFlow()) {
			if(input.getSource().equalsIgnoreCase(WEB_)){
				caseInput.setOrigin(FreezeMembershipConstants.ACCOUNT_EQUINOX);
			} else {
				caseInput.setOrigin(FreezeMembershipConstants.WEBSITE);
			}
			if (!createCaseFlag) {
				caseInput.setDescription(FreezeMembershipConstants.API_ERROR);
			}
		} else {
			caseInput.setOrigin(FreezeMembershipConstants.CLUB_FRONT_DESK);
			if (!createCaseFlag) {
				caseInput.setDescription(FreezeMembershipConstants.API_ERROR);
			}
		}
		
		//overriding the 'origin=source' with 'origin=caseOrigin' for chatbot, JIRA# WEB-4183, NMNM-3151
		if(!StringUtils.isBlank(input.getCaseOrigin()))
			caseInput.setOrigin(input.getCaseOrigin());

		// check if university freeze fee flag is on
		if (input.isCancelFlow() && input.isUniversityMember() && input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			caseInput.setAlternateReason(FreezeMembershipConstants.UNIVERSITY_FREEZE);
		}

		if (!input.getFreezeReasonId().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)
				&& input.isWaveFreezeFee() && input.getSource().equalsIgnoreCase(FreezeMembershipConstants.SOURCE_ENGAGE)) {
			caseInput.setIsException(true);
		}

		logger.debug("Contact Id :{}", contactId);
		long startTime = System.currentTimeMillis();
		try {
			logger.debug("noteUrl" + noteUrl);
			URL obj = new URL(noteUrl);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			ObjectMapper objectMapper = new ObjectMapper();

			// Setting basic post request
			con.setRequestMethod("POST");
			con.setReadTimeout(60 * 1000);
			con.setConnectTimeout(60 * 1000);
			con.setRequestProperty("Authorization", authorization);
			con.setRequestProperty("Content-Type", "application/json");

			String postJsonData = objectMapper.writeValueAsString(caseInput);
			logger.debug("postJsonData :{}", postJsonData);

			// Send post request
			con.setDoOutput(true);
			DataOutputStream wr = new DataOutputStream(con.getOutputStream());
			wr.writeBytes(postJsonData);
			wr.flush();
			wr.close();
			caseOutput = objectMapper.readValue(con.getInputStream(), CreateCaseResponse.class);
			if (caseOutput != null) {
				String id = caseOutput.getId();
				logger.info("Successfully created Case in Engage for member: {}, Case Status: {}, Case Id: {}", input.getMosoId(), status, id);
				eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+input.getMosoId(), "salesforce.createcase."+input.getMosoId(), "", 
						input.getCorrelationId(), caseInput, caseOutput, HttpStatus.OK, System.currentTimeMillis()-startTime);
			}
		} catch (Exception e) {
			logger.info("Failed to created Case in Engage for member: {}, Case Status: {}, error: {}", input.getMosoId(), status, e.getMessage());
			eventService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+input.getMosoId(), "salesforce.createcase."+input.getMosoId(), "", 
					input.getCorrelationId(), caseInput, e.getMessage(), HttpStatus.BAD_REQUEST, System.currentTimeMillis()-startTime);
		}
		return caseOutput;
	}

	@Override
	public CreateCaseResponse storeFreezeDocumentinSFV4(FreezeMembershipInputV4 freezeMembershipInput, String memberName,
			String caseId) {
		logger.info("Storing Freeze document data in Salesforce for Member: {}", freezeMembershipInput.getMosoId());
		CreateCaseResponse contentVersionResponse = new CreateCaseResponse();
		OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
		String currentDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		
		if(Objects.nonNull(freezeMembershipInput.getFreezeDocuments()) && !freezeMembershipInput.getFreezeDocuments().isEmpty()) {
			for(FreezeDocuments freezeDocument : freezeMembershipInput.getFreezeDocuments()) {
				String documentType = StringUtils.substringAfter(freezeDocument.getType(), "_"); //Expected format from payload is 'base64_pdf' or 'base64_png'
				String title = MessageFormat.format("{0}_{1}#{2}.{3}", memberName, freezeMembershipInput.getMosoId(), currentDate, documentType);
				String documentData = freezeDocument.getData();
				
				ContentVersionSFRequest contentVersionSFRequest = prepareContentVersionSFData(title, documentData, freezeMembershipInput.getFacilityId(), caseId);
				
				Wrapper<CreateCaseResponse> contentVersionSFResponse = salesforceApiService.post(DynamoPK.MEMBER_ID+freezeMembershipInput.getMosoId(), "ContentVersion", contentVersionSFRequest, CreateCaseResponse.class);
				
				if(Objects.nonNull(contentVersionSFResponse)) {
					if(Objects.nonNull(contentVersionSFResponse.getData()) && !StringUtils.isEmpty(contentVersionSFResponse.getData().getId())) {
						String documentId = contentVersionSFResponse.getData().getId();
						logger.info("Freeze document data successfully saved in Salesforce for Member: {}, Content version Id: {}", freezeMembershipInput.getMosoId(), documentId);
						
						contentVersionResponse.setId(documentId);
						contentVersionResponse.setSuccess(true);
					}
					if(Objects.nonNull(contentVersionSFResponse.getMessage())) {
						logger.error("Unable to save the Freeze document data in Salesforce for Member: {}, error: {}", freezeMembershipInput.getMosoId(), contentVersionSFResponse.getMessage());
						contentVersionResponse.setSuccess(false);
					}
				}else {
					logger.error("Unable to save the Freeze document data in Salesforce for Member: {}, empty response received from Salesforce", freezeMembershipInput.getMosoId());
					contentVersionResponse.setSuccess(false);
				}
			}
		}else {
			contentVersionResponse.setSuccess(false);
		}
		
		return contentVersionResponse;
	}

}
