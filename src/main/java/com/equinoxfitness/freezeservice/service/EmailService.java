package com.equinoxfitness.freezeservice.service;

import java.math.BigDecimal;

import com.equinoxfitness.freezeservice.contract.FreezeEmailInput;
import com.equinoxfitness.freezeservice.contract.MedicalFreezeEmailInput;

public interface EmailService {

	public boolean sendRegularFreezeConfirmationEmail(String memberId, String emailAddress, String memberFirstName, String countryCode, String startDate, BigDecimal freezeFees, int duration, String chargeDate);
	public boolean sendMedicalAndPregnancyFreezeRequestEmail(String memberId, String emailAddress, String memberFirstName, String startDate, int duration);
	public boolean sendMemberFreezeEmail(FreezeEmailInput freezeEmailInput, String token, String firstName,String source);
	public boolean sendRegularFreezeConfirmationEmailV4(String mosoId, String email, String firstName, String countryCode,String startDate, String endDate, String chargeDate, BigDecimal freezeFees, int durationMonths, boolean isJoinedAfterContractChange, String source, boolean isHoldBillingInObFlag);
	public boolean sendRegularFreezeConfirmationEmailV5(String mosoId, String email, String firstName, String countryCode,String startDate, String endDate, String chargeDate, BigDecimal freezeFees, int durationMonths, boolean isJoinedAfterContractChange, String source, boolean isHoldBillingInObFlag, boolean isDurationInDays);
	public boolean sendFreezeConfirmationEmailToPIFMembers(String mosoId, String email, String firstName, String countryCode,String startDate, String endDate, String chargeDate, BigDecimal freezeFees, int durationMonths);
	public boolean sendFreezeConfirmationEmailToPIFMembersV1(String mosoId, String email, String firstName, String countryCode,String startDate, String endDate, String chargeDate, BigDecimal freezeFees, int duration, boolean isDurationInDays);
	public boolean sendMedicalAndPregnancyFreezeConfirmationEmail(String mosoId, String email, String firstName, String startDate, String endDate, int durationMonths);

	public boolean sendMedicalAndPregnancyFreezeConfirmationEmailV2(MedicalFreezeEmailInput medicalFreezeEmailInput);

	public boolean sendMedicalAndPregnancyFreezeRequestEmailV2(MedicalFreezeEmailInput medicalFreezeEmailInput);


}