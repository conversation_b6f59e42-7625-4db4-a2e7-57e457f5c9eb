/**
 * 
 */
package com.equinoxfitness.freezeservice.service;

import java.math.BigDecimal;
import java.util.Date;

import com.equinoxfitness.freezeservice.contract.FreezeEmailInput;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV3;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV4;
import com.equinoxfitness.freezeservice.contract.sfdc.CreateCaseResponse;
import com.equinoxfitness.freezeservice.contract.sfdc.TaskOutput;

/**
 * <AUTHOR>
 *
 */
public interface SFDCService {

	public String getContactId(String mosoMemberId);

	public String getClubId(String homeFacilityId);

	public CreateCaseResponse createCase(FreezeMembershipInput input,String eClubId, Double freezeFee);

	public CreateCaseResponse createCaseV2(FreezeMembershipInputV2 input, String eClubId, Double freezeFee, boolean createCaseFlag);
	
	public CreateCaseResponse createCaseV3(FreezeMembershipInputV3 input, String eClubId, Double freezeFee, Date endDate);

	public CreateCaseResponse createCaseForUnfreeze(String clubId, String contactId, String memberId);
	
	public String getMemberId(String salesForceId);
	
	public CreateCaseResponse storeFreezeDocumentinSF(FreezeMembershipInput input, String memberName, String caseId);

	public CreateCaseResponse storeFreezeDocumentinSF(FreezeMembershipInputV2 input, String memberName, String caseId);

	public TaskOutput createEmailTaskForFreezeConfirmation(String mosoId, String firstName, String countryCode, String startDate,
			String endDate, BigDecimal freezeFees, int durationMonths, boolean isInObligationFlag, CreateCaseResponse createCaseResponse, boolean emailsent, String source, boolean isHoldBillingInObFlag, boolean isPIF, String freezeReason);

	TaskOutput createEmailTaskForFreezeConfirmationV1(String mosoId, String firstName, String countryCode,
													  String startDate, String endDate, BigDecimal freezeFees, int duration, boolean isInObligationFlag, CreateCaseResponse createCaseResponse, boolean emailsent, String source, boolean isHoldBillingInObFlag, boolean isPIF, String freezeReason);

	public TaskOutput createEmailTaskForSendFreezeEmailLink(FreezeEmailInput freezeEmailInput, String source, String firstName);
	
	public CreateCaseResponse createCaseV4(FreezeMembershipInputV4 input, String eClubId, Double freezeFee, boolean createCaseFlag);
	
	public CreateCaseResponse storeFreezeDocumentinSFV4(FreezeMembershipInputV4 input, String memberName, String caseId);
	
}
