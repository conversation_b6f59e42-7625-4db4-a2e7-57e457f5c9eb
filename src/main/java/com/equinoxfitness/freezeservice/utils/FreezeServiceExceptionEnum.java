/**
 * 
 */
package com.equinoxfitness.freezeservice.utils;

/**
 * <AUTHOR>
 *
 */
public enum FreezeServiceExceptionEnum {
	NO_CODE(0),
	FREEZE_REASON_BLANK(6001),
	START_DATE_EMPTY(6002),
	E<PERSON>_DATE_AND_MONTH_DURATION_BLANK(6003),
	MEMBER_DETAIL_MISSING(6004),
	CORPORATE_MEMBER(6005),
	MEMBER_STATUS_RULE(6006),
	MEMBER_CLASS_RULE(6007),
	CONTRACT_EXPIRATION_RULE(6008),
	EQUINOX_FAMILY_RULE(6009),
	FREEZE_DURATION_RULE(6010),
	ONE_FREEZE_PER_YEAR_RULE(6011),
	CONTRACT_CANCELLATION_RULE(6012),
	MEMBER_ID_REQUIRED(6013),
	FREEZE_DURATION_REQUIRED(6014),
	GETSESSION_FAILED(6015),
	FREEZE_EXTENSION_FAILED(6017),
	FREEZE_EXTENSION_MONTH_VALIDATION(6018),
	FREEZE_EXTENSION_ENDDATE_VALIDATION(6019),
	FACILITYID_REQUIRED(6020),
	SOURCE_REQUIRED(6021),
	NO_ONE_TIME_FEE_ITEM(6022),
	FACILITY_SERVICE_FAILED(6023),
	FREEZE_MEMBERSHIP_FAILED(6024),
	ALREADY_REQUESTED_FREEZE(6025),
	PAYROLL_DEDUCTED_MEMBERS(6026),
	CONTRACTUAL_RULE(6027),
	UPDATE_EXPIRYDATE_FAILED(6028),
	MOSO_ID_REQUIRED(6029),
	FREEZE_STARTDATE_INVALID(6030),
	CLUB_OPEN_DATE_IS_REQUIRED(6031),
	REQUESTED_DATE_IS_REQUIRED(6032),
	LINK_HAS_BEEN_EXPIRED(6033),
	MEMBER_NOT_FOUND(6034),
	FACILITY_ID_NOT_FOUND(6035),
	NO_AGREEMENTS_FOUND(6036),
	NO_ACTIVE_FREEZE_FOUND(6037),
	CONTACT_CONCIERGE(6038),
	FAILED_TO_GET_MOSO_SESSION(6039),
	FAILED_TO_UNFREEZE(6040),
	FREEZE_NOT_ELIGIBLE(6041),
	IP_ADDRESS_REQUIRED(6042),
	FAILED_TO_CREATE_UNFREEZE_CASE_IN_SALESFORCE(6043),
	HOTEL_GUEST(6044),
	ACTIVE_FOR_PLUS_THIRTY_DAYS(6045),
	PENDING_FREEZE(6046),
	PARTIAL_SUBSIDY(6047),
	MEMBER_ALLOWED_TO_FREEZE_AGAIN(6048),
	PIF_MEMBERS_NOT_ELIGIBLE_FOR_FREEZE(6049),
	PAST_DUE_MEMBERS_NOT_ELIGIBLE_FOR_FREEZE(6050),
	UNABLE_TO_ENCRYPT_CREDIT_CARD(6051),
	COF_MISSING(6052),
	CREDITCARDTRANSACTION(6053),
	CREDITCARD(6054),
	PAYMENT_AUTHORIZATION_FAILED(6055),
	MEMBER_ALREADY_REQUESTED_FREEZE(6056),
	MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA(6057),
	FREEZE_FEE_REQUIRED(6058),
	MOSO_ERROR(6059),
	MOSO_ERROR_FRONT_DESK(6060),
	PAYMENT_FAILED(6061),
	FUTURE_FREEZE(6062),
	EMAIL_REQUIRED(6063),
	NOT_ON_FREEZE(6064),
	EITHER_DURATION_DAYS_OR_DURATION_MONTHS_REQUIRED(6065),
	FREEZE_TOO_EARLY(6066),
	INELIGIBLE_TO_FREEZE(6067),
	UNABLE_TO_FREEZE(6068),
	PAST_DUE_BALANCE(6069),
	ACTIVE_FREEZE (6070),
	REGULAR_FREEZE_PER_CONTRACTUAL_YEAR(6071),
	PENDING_AGREEMENT(6072),
	PENDING_FREEZE_NEW(6073),
	REGULAR_REASON_IS_ALLOWED(6074);
	
	private static final String MESSAGE_ID_4_DIGIT = "%04d";

	private int code;

	private FreezeServiceExceptionEnum(int errorCode) {
		code = errorCode;
	}

	public int getCode() {
		return code;
	}

	public static FreezeServiceExceptionEnum valueOf(int code) {
		FreezeServiceExceptionEnum[] exceptionMessages = values();
		for (FreezeServiceExceptionEnum messages : exceptionMessages) {
			if (messages.getCode() == code)
				return messages;
		}
		return NO_CODE;

	}

	public String getMessageId() {
		return String.format(MESSAGE_ID_4_DIGIT, getCode());
	}

	public void setCode(int code) {
		this.code = code;
	}

}
