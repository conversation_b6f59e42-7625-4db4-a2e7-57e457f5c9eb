/**
 * 
 */
package com.equinoxfitness.freezeservice.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import com.equinoxfitness.redisUtil.RedisTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.commons.utils.ErrorMessageHandler;
import com.equinoxfitness.commons.utils.ExceptionMessageEnum;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput;
import com.equinoxfitness.freezeservice.dvo.CorpAgreementDetail;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.service.FreezeService;
import com.equinoxfitness.redis.contract.member.Member;
import com.equinoxfitness.redis.service.RedisApiService;

/**
 * <AUTHOR>
 *
 */
@Component
public class FreezeEligibilityRule {
	
	@Autowired
	private RedisApiService redisApiService;
	
	@Autowired
	private ErrorMessageHandler errorMessageHandler;

	@Autowired
	private FreezeService freezeService;
	@Autowired
	@Qualifier("tokenNoFreezeTemplate")
	private RedisTemplate tokenNoFreezeTemplate;

	@Autowired
	private FreezeServiceHelper freezeServiceHelper;
	
	@Value("#{'${cancellationStateId.list}'.split(',')}")
	private List<String> cancellationStateId;

	private final static Logger logger = LoggerFactory.getLogger(FreezeEligibilityRule.class);
	private static final String FREEZE_STATUS_ACTIVE = "Active";
	private static final String CONTRACT_STATUS_ACTIVE = "Active";
	private static final String CONTRACT_STATUS_ON_FREEZE = "On Freeze";
	private static final String CONTRACT_STATUS_PENDING = "Pending";
	private static final String CONTRACT_STATUS_CANCELLED = "CANCELLED";
	private static final String CONTRACT_STATUS_HOLD = "HOLD";
	private static final String CONTRACT_STATUS_EXPIRED = "EXPIRED";
	private static final String CONTRACT_STATUS_NOT_FINALIZED = "NotFinalized";
	private static final String CONTRACT_STATUS_NON_FINALIZED = "NonFinalized";
	private static int contractCutOffTimeInDays = 90;
	private static int MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE = 30;
	private static final String STATUS_ERROR = "error";
	private static final String STATUS_WARNING = "warning";
	private static final String STATUS_ELIGIBLE = "eligible";
	private static final String CONTRACT_DATE = "2023-02-06";
	private static final String REASON_REGULAR = "Regular";
    private static final String AMEX_DESTINATION_ACCESS = "Amex Destination Access";
	private static final String AMEX_CENTURIAN = "Amex Centurion";
    private static final String SOURCE_CLUBAPP = "clubapp";
    private static final String SOURCE_ENGAGE = "engage";
    private static final String SOURCE_CONCIERGE = "concierge";
	private static final String REDUCED_FREEZE_CONTRACT_DATE = "2025-04-01";
	private static final String REASON_REG = "Reg";
	private static final String FREEZE_STATUS_RESCINDED = "Rescinded";


	public CheckFreezeEligiblityOutput eligibilityRuleCheck(MemberAgreementDetail memberAgreementDetail,
			CheckFreezeEligibilityInput input, Boolean hasCOF) {

		// Declaration and initialization
		String contractStatus = memberAgreementDetail.getContractStatus();
		Date cancellationDate = memberAgreementDetail.getCancellationDate();
		String membershipClass = memberAgreementDetail.getMembershipClass();
		String memberStatus = memberAgreementDetail.getMemberStatus();
		String billingMethod = memberAgreementDetail.getBillingMethod();
		long contractExpirationGreacePeriod = 5;
		CheckFreezeEligiblityOutput output = new CheckFreezeEligiblityOutput();
		long contractCancellationGreacePeriod = 15;

		/*
		 * if (input.getEndDate() != null) { freezeEndDate = input.getEndDate();
		 * logger.debug("FreezeEndDate : {}", freezeEndDate); } else if
		 * (input.getStartDate() != null && (input.getDurationMonths() != 0 ||
		 * input.getDurationDays() != 0)) {
		 * logger.debug("No freezeEndDate in the input.."); freezeEndDate =
		 * calculateEndDate(input.getStartDate(), input.getDurationMonths(),
		 * input.getDurationDays()); logger.debug("Calculated freezeEndDate : {}",
		 * freezeEndDate); }
		 */

		output.setEligibleForFreeze(false);

		logger.debug("Inside Eligibility Rule check");
		// logger.debug("Freeze Reason {}", freezeReason);
		
		// checkAlreadyInFreeze
		logger.debug("Checking Member is already have active/Future Freeze?");
		if (memberAgreementDetail.getFreezeEndTime() != null && memberAgreementDetail.getFreezeStatus() != null) {
			Date currnetDate = Calendar.getInstance().getTime();
			if (memberAgreementDetail.getFreezeEndTime().getTime() > currnetDate.getTime()
					&& memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("active")) {
				ExceptionMessage exceptionMessage = setExceptionBlock("Member already have requested for freeze",
						"Member already have requested for freeze",
						FreezeServiceExceptionEnum.MEMBER_ALREADY_REQUESTED_FREEZE.getCode(), "VALIDATION");
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				logger.debug("Member current Freeze Status: {} ",memberAgreementDetail.getFreezeStatus() );
				return output; 
			}
		}
		logger.debug("No active/Future Freeze on account");
		
		// checkMemberCorporateRule
		logger.debug("Is a Corporate Member with COF?");
		if ((memberAgreementDetail.getMembershipClass().contains("Corp") || memberAgreementDetail.getMembershipClass().contains("Subsidy")) && !hasCOF) {

			ExceptionMessage exceptionMessage = setExceptionBlock("Corporate Members are not allowed to freeze",
					"Corporate Members are not allowed to freeze",
					FreezeServiceExceptionEnum.CORPORATE_MEMBER.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			logger.debug("Member has ---> {}, and no COF. So, Freeze is not allowed. ",memberAgreementDetail.getMembershipClass());
			return output;
		}
		logger.debug("Yes");
		
		// restrictPayrollDeductedMembers
		logger.debug("Is Payroll Deducted Member?");
		if (memberAgreementDetail.getBillingMethod().equalsIgnoreCase("Payroll Deducted")) {

			ExceptionMessage exceptionMessage = setExceptionBlock("Payroll Deducted Members are not allowed to freeze",
					"Payroll Deducted Members are not allowed to freeze",
					FreezeServiceExceptionEnum.PAYROLL_DEDUCTED_MEMBERS.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			logger.debug("Yes: {} ",memberAgreementDetail.getBillingMethod() + "Member");
			return output;
		}
		logger.debug("No");
		// memberStatusRule
		logger.debug("contractStatus {}", contractStatus);
		if (contractStatus.equalsIgnoreCase("PENDING") || contractStatus.equalsIgnoreCase("CANCELLED")
				|| contractStatus.equalsIgnoreCase("ONHOLD") || contractStatus.equalsIgnoreCase("EXPIRED")) {

			ExceptionMessage exceptionMessage = setExceptionBlock(
					"Pending/Cancelled/Expired/On Hold Member are not eligible for freeze.",
					"Pending/Cancelled/Expired/On Hold Member are not eligible for freeze.",
					FreezeServiceExceptionEnum.MEMBER_STATUS_RULE.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		// membershipClassRule
		logger.debug("membershipClass {}", membershipClass);
		if (membershipClass.startsWith("Prospect") || membershipClass.startsWith("Short Term")
				|| membershipClass.startsWith("Comp")) {

			ExceptionMessage exceptionMessage = setExceptionBlock(
					"Prospect/Short term/Complementary Members are not allowed to freeze.",
					"Prospect/Short term/Complementary Members are not allowed to freeze.",
					FreezeServiceExceptionEnum.MEMBER_CLASS_RULE.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		// ContractExpirationRule
		/*
		 * if (memberAgreementDetail.getEndDate() != null &&
		 * "Fixed".equalsIgnoreCase(memberAgreementDetail.getAgreementType())) { long
		 * endDateinMilis = memberAgreementDetail.getEndDate().getTime();
		 * logger.debug("StartTimeInMilis {}", startTimeInMilis , " EndTimeInMilis {}" ,
		 * endDateinMilis); if (startTimeInMilis > endDateinMilis) {
		 * 
		 * ExceptionMessage exceptionMessage = new ExceptionMessage(); exceptionMessage
		 * .setErrorMessage("The Freeze Start date should be at least 5 days from the expiration date."
		 * ); exceptionMessage.setFriendlyMessage(
		 * "The Freeze Start date should be at least 5 days from the expiration date.");
		 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.
		 * CONTRACT_EXPIRATION_RULE.getCode());
		 * exceptionMessage.setMessageType("VALIDATION"); output.setMessages(new
		 * ExceptionMessage[] { exceptionMessage }); return output; } else { long diff =
		 * endDateinMilis - startTimeInMilis; long daysDiff = diff / (24 * 60 * 60 *
		 * 1000); if (daysDiff <= contractExpirationGreacePeriod) {
		 * 
		 * ExceptionMessage exceptionMessage = new ExceptionMessage();
		 * exceptionMessage.setErrorMessage(
		 * "The Freeze Start date should be at least 5 days from the expiration date.");
		 * exceptionMessage.setFriendlyMessage(
		 * "The Freeze Start date should be at least 5 days from the expiration date.");
		 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.
		 * CONTRACT_EXPIRATION_RULE.getCode());
		 * exceptionMessage.setMessageType("VALIDATION"); output.setMessages(new
		 * ExceptionMessage[] { exceptionMessage }); } } }
		 */

		// EquinoxFamilyMembershipRule
		if (membershipClass.equalsIgnoreCase("Employee benefit - select")) {

			ExceptionMessage exceptionMessage = setExceptionBlock(
					"Equinox Friends and Family Members are not allowed to freeze.",
					"Equinox Friends and Family Members are not allowed to freeze.",
					FreezeServiceExceptionEnum.EQUINOX_FAMILY_RULE.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		/*
		 * //PerYearContractual if(input.getStartDate()!=null) { Calendar
		 * obligationDateCalendar = Calendar.getInstance();
		 * obligationDateCalendar.setTime(memberAgreementDetail.getObligationDate());
		 * Calendar considerationDateCalendar = Calendar.getInstance();
		 * considerationDateCalendar.setTime(input.getStartDate()); Calendar
		 * contractualYearStartDateCal = Calendar.getInstance();
		 * contractualYearStartDateCal.setTime(memberAgreementDetail.getObligationDate()
		 * ); if (considerationDateCalendar.getTimeInMillis() <=
		 * obligationDateCalendar.getTimeInMillis()) {
		 * logger.debug("ConsiderationDate is same as or before the obligationDate...");
		 * // subtract 1 year from the obligation date logger.
		 * debug("Subtracting 1 year from the contractualYearStartDate since considerationDate is lesser than obligationDate..."
		 * ); contractualYearStartDateCal.add(Calendar.YEAR, -1); } else {
		 * logger.debug("ConsiderationDate is beyond the obligationDate..."); while
		 * (contractualYearStartDateCal.get(Calendar.YEAR) <
		 * considerationDateCalendar.get(Calendar.YEAR)){
		 * contractualYearStartDateCal.add(Calendar.YEAR, 1); } // subtract 1 year from
		 * the obligation date //Added 5/14/09 - SD if
		 * (contractualYearStartDateCal.getTimeInMillis() >
		 * Calendar.getInstance().getTimeInMillis())
		 * contractualYearStartDateCal.add(Calendar.YEAR, -1); } }
		 */

		// FreezeDurationRule
		/*
		 * switch (FreezeReasonEnum.getFreezeReasonFromValue(freezeReason)) { case
		 * REGULAR: logger.debug("Regular Case"); if (durationMonth > 0 && durationMonth
		 * > 3) { ExceptionMessage exceptionMessage = new ExceptionMessage();
		 * exceptionMessage.setErrorMessage("Regular freeze limit is 3 months");
		 * exceptionMessage.setFriendlyMessage("Regular freeze limit is 3 months");
		 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE
		 * .getCode()); exceptionMessage.setMessageType("VALIDATION");
		 * output.setMessages(new ExceptionMessage[] { exceptionMessage }); } break;
		 * case PREGNANCY: logger.debug("PREGNANCY Case"); if (durationMonth > 0 &&
		 * durationMonth > 9) { ExceptionMessage exceptionMessage = new
		 * ExceptionMessage();
		 * exceptionMessage.setErrorMessage("Pregnancy freeze limit is 9 months");
		 * exceptionMessage.setFriendlyMessage("Pregnancy freeze limit is 9 months");
		 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE
		 * .getCode()); exceptionMessage.setMessageType("VALIDATION");
		 * output.setMessages(new ExceptionMessage[] { exceptionMessage }); } break;
		 * case MEDICAL: logger.debug("MEDICAL Case"); if (durationMonth > 0 &&
		 * durationMonth > 6) { ExceptionMessage exceptionMessage = new
		 * ExceptionMessage();
		 * exceptionMessage.setErrorMessage("Medical freeze limit is 6 months");
		 * exceptionMessage.setFriendlyMessage("Medical freeze limit is 6 months");
		 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE
		 * .getCode()); exceptionMessage.setMessageType("VALIDATION");
		 * output.setMessages(new ExceptionMessage[] { exceptionMessage }); } break;
		 * case OTHER:
		 * 
		 * ExceptionMessage exceptionMessage = new ExceptionMessage();
		 * exceptionMessage.setErrorMessage(
		 * "Freeze eligibilty can only be checked for reasons such as Regular/Medical/Pregnancy"
		 * ); exceptionMessage.setFriendlyMessage(
		 * "Freeze eligibilty can only be checked for reasons such as Regular/Medical/Pregnancy"
		 * );
		 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE
		 * .getCode()); exceptionMessage.setMessageType("VALIDATION");
		 * output.setMessages(new ExceptionMessage[] { exceptionMessage }); break;
		 * default: break; }
		 */

		// OneFreezePerContractualYearRule
		if (input.getFreezeReason() != null && input.getStartDate() != null) {
			boolean isEligible = true;
			Date obligationDate = null;
			obligationDate = memberAgreementDetail.getObligationDate();
			List<MemberAgreementDetail> memberAgreementDetailList = new ArrayList<MemberAgreementDetail>();
			memberAgreementDetailList.add(memberAgreementDetail);
			logger.debug("obligationDate : {}", obligationDate);
			boolean isfreezedBefore = false;
			if (!isStartDateOneDayAfterPreviousEndDate(input, memberAgreementDetail)) {
				logger.info("Checking One Freeze Per Contractual Year..."); //
				// Check one freeze per contractual year //
				try {
					isfreezedBefore = checkOneFreezePerContractualYear(memberAgreementDetailList, memberAgreementDetail,
							input, obligationDate);
					if (!input.getFreezeReason().contains("Medical") &&
							!input.getFreezeReason().contains("Pregnancy")) {
						freezeService.checkDurationInDaysForEligibility(memberAgreementDetail.getHomeFacilityId(), output);
					}

				} catch (Exception e) {
					ExceptionMessage exceptionMessage = setExceptionBlock(
							e.getMessage(),
							"Failed to check rule for one freeze per Contractual year",
							FreezeServiceExceptionEnum.CONTRACTUAL_RULE.getCode(), "ERROR");			
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
					return output;
				}
				if (isfreezedBefore) {
					ExceptionMessage exceptionMessage = new ExceptionMessage();
					exceptionMessage.setErrorMessage("One freeze allowed per contractual year");
					exceptionMessage.setFriendlyMessage("One freeze allowed per contractual year");
					exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE.getCode());
					exceptionMessage.setMessageType("VALIDATION");
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
					output.setEligibleForFreeze(false);
					return output;
				}
			} else {/*
				
				 * // --------------------------------- // Check Freeze extension //
				 * ---------------------------------
				 * 
				 * if (input.getDurationMonths() != 0 || freezeEndDate != null &&
				 * FREEZE_STATUS_ACTIVE.equalsIgnoreCase(memberAgreementDetail.getFreezeStatus()
				 * )) { logger.info("Checking Freeze Extension..."); try { isEligible =
				 * checkFreezeExtension(input, memberAgreementDetailList, memberAgreementDetail,
				 * obligationDate); } catch (Exception e) { // TODO Auto-generated catch block
				 * logger.error(e.getMessage()); } if (!isEligible) { ExceptionMessage
				 * exceptionMessage = new ExceptionMessage();
				 * exceptionMessage.setErrorMessage("One freeze allowed per contractual year");
				 * exceptionMessage.setFriendlyMessage("One freeze allowed per contractual year"
				 * );
				 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE
				 * .getCode()); exceptionMessage.setMessageType("VALIDATION");
				 * output.setMessages(new ExceptionMessage[] { exceptionMessage });
				 * output.setEligibleForFreeze(false); return output; } }
				 */}

		}

		// ContractCancellationRule
		/*
		 * if ((memberStatus.equalsIgnoreCase("Active") ||
		 * memberStatus.equalsIgnoreCase("On Freeze")) && cancellationDate != null) {
		 * long cancellationDateinMilis = cancellationDate.getTime(); if
		 * (freezeEndDate.getTime() > cancellationDateinMilis) {
		 * 
		 * ExceptionMessage exceptionMessage = new ExceptionMessage();
		 * exceptionMessage.setErrorMessage("A cancellation request exists.");
		 * exceptionMessage.setFriendlyMessage("A cancellation request exists.");
		 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.
		 * CONTRACT_CANCELLATION_RULE.getCode());
		 * exceptionMessage.setMessageType("VALIDATION"); output.setMessages(new
		 * ExceptionMessage[] { exceptionMessage }); return output; } else { long diff =
		 * cancellationDateinMilis - freezeEndDate.getTime();
		 * logger.debug("Time Diff for cancellation " + diff); long daysDiff = diff /
		 * (24 * 60 * 60 * 1000); if (daysDiff <= contractCancellationGreacePeriod) {
		 * 
		 * ExceptionMessage exceptionMessage = new ExceptionMessage();
		 * exceptionMessage.setErrorMessage("A cancellation request exists.");
		 * exceptionMessage.setFriendlyMessage("A cancellation request exists.");
		 * exceptionMessage.setMessageID(FreezeServiceExceptionEnum.
		 * CONTRACT_CANCELLATION_RULE.getCode());
		 * exceptionMessage.setMessageType("VALIDATION"); output.setMessages(new
		 * ExceptionMessage[] { exceptionMessage }); return output; } } }
		 */

		output.setEligibleForFreeze(true);
		return output;

	}

	public CheckFreezeEligiblityOutput eligibilityRuleCheckV2(MemberAgreementDetail memberAgreementDetail,
			CheckFreezeEligibilityInputV2 input, Boolean hasCOF, CorpAgreementDetail corpAgreementDetail, 
			List<MemberAgreementDetail> memberAgreementDetailsList, BigDecimal balance, String facilityId) {
		CheckFreezeEligiblityOutput output = new CheckFreezeEligiblityOutput();
		Map<String, String> urlVariables = new HashMap<>();
		urlVariables.put("categories", "membership,profile");
		Member member = redisApiService.getMemberV2SpecificCategories(input.getMosoId(), urlVariables);
		if (Objects.isNull(member)) {
			ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.name(), FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.getCode(),
					ExceptionMessageEnum.ERROR.name());
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;			
		}
		if(Objects.nonNull(memberAgreementDetail.getEmailAddress())) {
			output.setEmail(memberAgreementDetail.getEmailAddress());
		}else {		
			output.setEmail(member.getProfile().getEmail());
		}		
		// Declaration and initialization
		if(input.getStartDate() == null) {
			Date date = new Date();
			input.setStartDate(date);
		}		 
		String contractStatus = memberAgreementDetail.getContractStatus();
		String membershipClass = memberAgreementDetail.getMembershipClass();		
		output.setEligibleForFreeze(false);

		if(member.getMembership().getMemberSince() != null) {
			//LocalDate currentDate = LocalDate.now(); 
			LocalDate freezeStartDate = null;
			freezeStartDate = input.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			Calendar memberSince = member.getMembership().getMemberSince();
			LocalDate memberSinceDate = LocalDateTime.ofInstant(memberSince.toInstant(), memberSince.getTimeZone().toZoneId()).toLocalDate();
			output.setMemberSince(memberSinceDate.toString());
			long daysDiff = ChronoUnit.DAYS.between(memberSinceDate,freezeStartDate);
			LocalDate contractChangeDate = LocalDate.parse(CONTRACT_DATE);
			if(!StringUtils.isEmpty(input.getFreezeReason()) && input.getFreezeReason().equalsIgnoreCase(REASON_REGULAR)) {
				if(daysDiff < 365) {
					if(memberSinceDate.isAfter(contractChangeDate) || memberSinceDate.isEqual(contractChangeDate)) {
						output.setJoinedAfterContractChange(true);
						output.setInObligation(true);
					} else {
							output.setJoinedAfterContractChange(false);
							output.setInObligation(true);
					}
				}
				if(daysDiff >= 365) {
					if(memberSinceDate.isAfter(contractChangeDate) || memberSinceDate.isEqual(contractChangeDate))
						output.setJoinedAfterContractChange(true);
					else
						output.setJoinedAfterContractChange(false);
				}
			}
		}		
		logger.debug("Inside Eligibility Rule check");		
		
        if (memberAgreementDetail.getMembershipClass().trim().equalsIgnoreCase(AMEX_DESTINATION_ACCESS)) {
			output.setStatus(STATUS_ERROR);
			output.setEligibleForFreeze(false);
			ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.AMEX_CENTURION.name(), EngageFreezeServiceExceptionEnum.AMEX_CENTURION.getCode(),
					ExceptionMessageEnum.VALIDATION.name());
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}
		
		logger.debug("Is a Hotel Guest");
		if (memberAgreementDetail.getMembershipClass().equalsIgnoreCase("Hotel Guest")) {
			output.setStatus(STATUS_ERROR);
			output.setEligibleForFreeze(false);
			if(StringUtils.isEmpty(input.getSource()) || input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
				ExceptionMessage exceptionMessage = setExceptionBlock("Hotel Guests cannot freeze.",
						"Hotel Guests cannot freeze.",
						FreezeServiceExceptionEnum.HOTEL_GUEST.getCode(), "VALIDATION");
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				logger.debug("Member has ---> {}. So, Freeze is not allowed. ",memberAgreementDetail.getMembershipClass());
			}else {
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.HOTEL_GUEST_MEMBERS.name(), EngageFreezeServiceExceptionEnum.HOTEL_GUEST_MEMBERS.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
				output.setMessages(new ExceptionMessage[] { exceptionMessage });				
			}
			return output;
		}
		
		logger.debug("Members with Pending Freeze should not be Eligible");
		if(memberAgreementDetail.getFreezeStatus() != null && memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("Pending Start")) {
			java.sql.Date freezeStartTime = memberAgreementDetail.getFreezeStartTime();
			LocalDate frzStartTime = java.sql.Date.valueOf(freezeStartTime.toString()).toLocalDate();
			Date freezeEndTime = memberAgreementDetail.getFreezeEndTime();
			LocalDate frzEndTime = java.sql.Date.valueOf(freezeEndTime.toString()).toLocalDate();
			String freezeReason = getFreezeReason(memberAgreementDetail.getFreezeReason());
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");	
			if(StringUtils.isEmpty(input.getSource()) || input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
				output.setStatus(STATUS_ERROR);
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.FUTURE_FREEZE.name(), FreezeServiceExceptionEnum.FUTURE_FREEZE.getCode(),
						ExceptionMessageEnum.VALIDATION.name(),formatter.format(frzStartTime));
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
			} else {
				output.setStatus(STATUS_WARNING);
				output.setEligibleForFreeze(true);
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PENDING_FREEZE.name(), FreezeServiceExceptionEnum.PENDING_FREEZE.getCode(),
						ExceptionMessageEnum.VALIDATION.name(),freezeReason, formatter.format(frzStartTime));
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
			}
			return output;		
		}


		// memberStatusRule
		logger.debug("contractStatus {}", contractStatus);
		if (contractStatus.equalsIgnoreCase(CONTRACT_STATUS_PENDING) || contractStatus.equalsIgnoreCase(CONTRACT_STATUS_CANCELLED)
				|| contractStatus.equalsIgnoreCase(CONTRACT_STATUS_HOLD) || contractStatus.equalsIgnoreCase(CONTRACT_STATUS_EXPIRED)
				|| contractStatus.equalsIgnoreCase(CONTRACT_STATUS_NOT_FINALIZED) || contractStatus.equalsIgnoreCase(CONTRACT_STATUS_NON_FINALIZED)) {
			output.setStatus(STATUS_ERROR);
			if(StringUtils.isEmpty(input.getSource()) || input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
				ExceptionMessage exceptionMessage = setExceptionBlock(
						"Pending/Cancelled/Expired/Not Finalized Members are not eligible for freeze.",
						"Pending/Cancelled/Expired/Not Finalized Members are not eligible for freeze.",
						FreezeServiceExceptionEnum.MEMBER_STATUS_RULE.getCode(), "VALIDATION");
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
			}else {
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.PENDING_CANCELLED_EXPIRED_MEMBERSHIP.name(), EngageFreezeServiceExceptionEnum.PENDING_CANCELLED_EXPIRED_MEMBERSHIP.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
				output.setMessages(new ExceptionMessage[] { exceptionMessage });				
			}
			return output;
		}
		
		// membershipClassRule
		logger.debug("membershipClass {}", membershipClass);
		if (membershipClass.startsWith("Prospect") || membershipClass.startsWith("Short Term")
				|| membershipClass.startsWith("Comp")) {
			output.setStatus(STATUS_ERROR);
			if(StringUtils.isEmpty(input.getSource()) || input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
				ExceptionMessage exceptionMessage = setExceptionBlock(
						"Prospect, Short Term and Complementary member types are not allowed to freeze.",
						"Prospect, Short Term and Complementary member types are not allowed to freeze.",
						FreezeServiceExceptionEnum.MEMBER_CLASS_RULE.getCode(), "VALIDATION");
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
			}else {
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.PROSPECT_SHORTTERM_COMPLEMENTARY_MEMBERS.name(), EngageFreezeServiceExceptionEnum.PROSPECT_SHORTTERM_COMPLEMENTARY_MEMBERS.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
				output.setMessages(new ExceptionMessage[] { exceptionMessage });				
			}
			return output;
		}
		
		//PIF Expiration Date Rule //AgreementTermId 1 for PIF and 2 For Bill Monthly
		if(memberAgreementDetail.getAgreementTermId()==1) {
			LocalDate freezeStartDate = null;
			if(StringUtils.isEmpty(input.getSource()) || input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
				output.setStatus(STATUS_ERROR);
				output.setEligibleForFreeze(false);
				output.setIsPIF(Boolean.TRUE);
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PIF_MEMBERS_NOT_ELIGIBLE_FOR_FREEZE.name(), FreezeServiceExceptionEnum.PIF_MEMBERS_NOT_ELIGIBLE_FOR_FREEZE.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				return output;
			} else {
				output.setIsPIF(Boolean.TRUE);
				output.setStatus(STATUS_ERROR);
				output.setEligibleForFreeze(false);
				freezeStartDate = input.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
				java.sql.Date endDate = memberAgreementDetail.getEndDate();
				LocalDate expirationDate = java.sql.Date.valueOf(endDate.toString()).toLocalDate();
				boolean isAfter = freezeStartDate.isAfter(expirationDate.minusDays(30));
				if(isAfter) {					
					ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.PIF_MEMBERS.name(), EngageFreezeServiceExceptionEnum.PIF_MEMBERS.getCode(),
							ExceptionMessageEnum.VALIDATION.name());
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
					return output;		
				}
			}
		}
//Commented the code as per https://equinoxfitness.atlassian.net/browse/SPACES-3065
//		//Past Due Eligibility Rule
//		if(balance.compareTo(BigDecimal.ZERO) > 0) {
//			output.setStatus(STATUS_ERROR);
//			output.setEligibleForFreeze(false);
//			if(input.getSource().equalsIgnoreCase("clubapp") || StringUtils.isEmpty(input.getSource())) {
//				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PAST_DUE_MEMBERS_NOT_ELIGIBLE_FOR_FREEZE.name(), FreezeServiceExceptionEnum.PAST_DUE_MEMBERS_NOT_ELIGIBLE_FOR_FREEZE.getCode(),
//						ExceptionMessageEnum.VALIDATION.name());
//				output.setMessages(new ExceptionMessage[] { exceptionMessage });
//			}else {
//				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.PAST_DUE_MEMBERS.name(), EngageFreezeServiceExceptionEnum.PAST_DUE_MEMBERS.getCode(),
//						ExceptionMessageEnum.VALIDATION.name());
//				output.setMessages(new ExceptionMessage[] { exceptionMessage });
//			}
//			return output;		
//		}
		
		//Pending Cancellation Rule
		if (Objects.nonNull(memberAgreementDetail.getCancellationDate()) && !cancellationStateId.contains(memberAgreementDetail.getCancellationStateId())) {
			//LocalDate currentDate = LocalDate.now(); 
			LocalDate freezeStartDate = input.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			LocalDate cancellationDate = memberAgreementDetail.getCancellationDate().toLocalDate();
			if(cancellationDate.isAfter(freezeStartDate)) {
				output.setStatus(STATUS_ERROR);
				output.setEligibleForFreeze(false);
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.PENDING_FUTURE_CANCELLATION_DATE.name(), EngageFreezeServiceExceptionEnum.PENDING_FUTURE_CANCELLATION_DATE.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
				output.setMessages(new ExceptionMessage[] { exceptionMessage });				
				return output;
			}
		}
		
		logger.debug("Partial Subsidy is Ineligible");
		if(Objects.nonNull(corpAgreementDetail)) {
			if(corpAgreementDetail.getAgreementType() != null && corpAgreementDetail.getAgreementType().equalsIgnoreCase("Corporate Pay Amount") || corpAgreementDetail.getAgreementType() != null && corpAgreementDetail.getAgreementType().equalsIgnoreCase("Corporate Pay Percent")) {
				output.setStatus(STATUS_ERROR);
				output.setEligibleForFreeze(false);
				if(StringUtils.isEmpty(input.getSource()) || input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
					ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PARTIAL_SUBSIDY.name(), FreezeServiceExceptionEnum.PARTIAL_SUBSIDY.getCode(),
							ExceptionMessageEnum.VALIDATION.name());
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
				}else {
					ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.PARTIAL_SUBSIDY_MEMBERSHIP_AGREEMENTS.name(), EngageFreezeServiceExceptionEnum.PARTIAL_SUBSIDY_MEMBERSHIP_AGREEMENTS.getCode(),
							ExceptionMessageEnum.VALIDATION.name());
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
				}
				return output;		
			}
		}

		//     check if Freeze too early in their tenure for new members - only for regular flow and not for medical and pregnancy
		long daysBetween = -1;
		if(member.getMembership().getMemberSince() != null && !StringUtils.isEmpty(input.getFreezeReason())) {
			// Retrieve flags and required tenure from Redis
			Map<String, String> pilotKeys = tokenNoFreezeTemplate.getRedisHashOpsData("pilot.new.member.eligibility");
			String tenureFlag = null;
			if (pilotKeys != null && !pilotKeys.isEmpty()) {
				String homeFacilityId = memberAgreementDetail.getHomeFacilityId();

				for(Map.Entry<String, String> entry : pilotKeys.entrySet()) {
					String tenureKey = entry.getKey();
					String clubIds = entry.getValue();

					// Check if the homeFacilityId is contained in the list of club IDs for this tenure
					if (clubIds.contains(homeFacilityId)) {
						tenureFlag = tenureKey;
						break; // Stop once we find the matching tenure flag
					}
				}
			}

			// Parse required tenure from the flag if it exists
			if (tenureFlag == null) {
				Calendar memberSince = member.getMembership().getMemberSince();
				Calendar currentTime = Calendar.getInstance();
				currentTime.setTime(input.getStartDate());
				daysBetween = ChronoUnit.DAYS.between(memberSince.toInstant(), currentTime.toInstant());

				if(daysBetween >=0 && daysBetween < MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE) {
					int daysDiff = (int) (MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE - daysBetween);
					LocalDate currentDate = LocalDate.now();
					currentDate = currentDate.plusDays(daysDiff);
					DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
					if (input.getFreezeReason().equalsIgnoreCase(REASON_REGULAR) && (input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE))) {
						output.setStatus(STATUS_ERROR);
						output.setEligibleForFreeze(false);
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.ACTIVE_FOR_PLUS_THIRTY_DAYS.name(), FreezeServiceExceptionEnum.ACTIVE_FOR_PLUS_THIRTY_DAYS.getCode(),
								ExceptionMessageEnum.VALIDATION.name(), formatter.format(currentDate));
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					} else {
						output.setStatus(STATUS_WARNING);
						output.setEligibleForFreeze(true);
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS.name(), EngageFreezeServiceExceptionEnum.MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS.getCode(),
								ExceptionMessageEnum.VALIDATION.name(), formatter.format(currentDate));
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					}
					return output;
				}
			} else if(input.getFreezeReason().equalsIgnoreCase(REASON_REGULAR)) {
				int requiredTenureDays = Integer.parseInt(tenureFlag.replace("_days", ""));

				// Convert start date and member since date to LocalDate
				LocalDate freezeStartDate =
						input.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
				Calendar memberSince = member.getMembership().getMemberSince();
				LocalDate memberSinceDate =
						LocalDateTime.ofInstant(memberSince.toInstant(), memberSince.getTimeZone().toZoneId())
								.toLocalDate();
				long daysDiff = ChronoUnit.DAYS.between(memberSinceDate, freezeStartDate);
				// Set member's "member since" date in output for reference
				output.setMemberSince(memberSinceDate.toString());
				// Compare tenure with required duration
				if (daysDiff <= requiredTenureDays) {
					// Set the warning message if tenure is less than required duration
					output.setStatus(STATUS_WARNING);
					ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.FREEZE_TOO_EARLY_IN_TENURE.name(), EngageFreezeServiceExceptionEnum.FREEZE_TOO_EARLY_IN_TENURE.getCode(),
							ExceptionMessageEnum.VALIDATION.name(), requiredTenureDays);
					output.setMessages(new ExceptionMessage[]{exceptionMessage});
					output.setEligibleForFreeze(false);
					return output;
				}
			}
		} else {
			if(contractStatus.equalsIgnoreCase("Active") && memberAgreementDetail.getStartDate() != null) {
					java.sql.Date startDate = memberAgreementDetail.getStartDate();
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(startDate);
					Calendar currentDate = Calendar.getInstance();
					daysBetween = ChronoUnit.DAYS.between(calendar.toInstant(), currentDate.toInstant());
			}

			if(daysBetween >=0 && daysBetween < MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE) {
				int daysDiff = (int) (MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE - daysBetween);
				LocalDate currentDate = LocalDate.now();
				currentDate = currentDate.plusDays(daysDiff);
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
				if (input.getFreezeReason().equalsIgnoreCase(REASON_REGULAR) && (input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE))) {
					output.setStatus(STATUS_ERROR);
					output.setEligibleForFreeze(false);
					ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.ACTIVE_FOR_PLUS_THIRTY_DAYS.name(), FreezeServiceExceptionEnum.ACTIVE_FOR_PLUS_THIRTY_DAYS.getCode(),
							ExceptionMessageEnum.VALIDATION.name(), formatter.format(currentDate));
					output.setMessages(new ExceptionMessage[]{exceptionMessage});
				} else {
					output.setStatus(STATUS_WARNING);
					output.setEligibleForFreeze(true);
					ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS.name(), EngageFreezeServiceExceptionEnum.MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS.getCode(),
							ExceptionMessageEnum.VALIDATION.name(), formatter.format(currentDate));
					output.setMessages(new ExceptionMessage[]{exceptionMessage});
				}
				return output;
			}
		}

		//Duplicate Existing Freeze
		if(input.getSource().equalsIgnoreCase(SOURCE_ENGAGE) && memberAgreementDetail.getFreezeStatus() != null && memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("active")) {			
			LocalDate requestedFreezeStartDate = input.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			LocalDate freezeStartTime = memberAgreementDetail.getFreezeStartTime().toLocalDate();
			LocalDate freezeEndTime = memberAgreementDetail.getFreezeEndTime().toLocalDate();
			if(requestedFreezeStartDate.isAfter(freezeEndTime) || requestedFreezeStartDate.isEqual(freezeEndTime)) {
				LocalDate frzStartDate = java.sql.Date.valueOf(freezeStartTime.toString()).toLocalDate();
				LocalDate frzEndDate = java.sql.Date.valueOf(freezeEndTime.toString()).toLocalDate();
				String freezeReason = getFreezeReason(memberAgreementDetail.getFreezeReason());								
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
				output.setStatus(STATUS_WARNING);
				output.setEligibleForFreeze(true);
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.ALREADY_REQUESTED_FREEZE.name(), EngageFreezeServiceExceptionEnum.ALREADY_REQUESTED_FREEZE.getCode(),
						ExceptionMessageEnum.VALIDATION.name(),freezeReason,formatter.format(frzStartDate),formatter.format(frzEndDate));
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
			}else {
				output.setStatus(STATUS_ERROR);
				output.setEligibleForFreeze(false);
				ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.FREEZE_EXTENSION_ERROR.name(), EngageFreezeServiceExceptionEnum.FREEZE_EXTENSION_ERROR.getCode(),
						ExceptionMessageEnum.VALIDATION.name());
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
			}
			return output;

		}
		
		// checkAlreadyInFreeze
		logger.debug("Checking Member is already have active/Future Freeze?");
		if (memberAgreementDetail.getFreezeEndTime() != null && memberAgreementDetail.getFreezeStatus() != null) {
			//Date currnetDate = Calendar.getInstance().getTime();
			LocalDate requestedFreezeStartDate = input.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			LocalDate freezeEndTime = memberAgreementDetail.getFreezeEndTime().toLocalDate();
			if (freezeEndTime.isAfter(requestedFreezeStartDate) && memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("active")) {
				Date freezeStartTime = memberAgreementDetail.getFreezeStartTime();
				SimpleDateFormat simpDate = new SimpleDateFormat("MM/dd/YYYY");
				String freezeStartDate = simpDate.format(freezeStartTime);
				output.setStatus(STATUS_ERROR);
				if(input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
					ExceptionMessage exceptionMessage = setExceptionBlock(
							"The member is already on freeze which started on "+freezeStartDate+".",
							"The member is already on freeze which started on "+freezeStartDate+".",
							FreezeServiceExceptionEnum.ALREADY_REQUESTED_FREEZE.getCode(), "VALIDATION");
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
				} else {
					LocalDate frzStartDate = java.sql.Date.valueOf(freezeStartTime.toString()).toLocalDate();
					LocalDate frzEndDate = java.sql.Date.valueOf(freezeEndTime.toString()).toLocalDate();
					String freezeReason = getFreezeReason(memberAgreementDetail.getFreezeReason());		
					DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
					output.setStatus(STATUS_WARNING);
					output.setEligibleForFreeze(true);
					ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.ALREADY_REQUESTED_FREEZE.name(), EngageFreezeServiceExceptionEnum.ALREADY_REQUESTED_FREEZE.getCode(),
							ExceptionMessageEnum.VALIDATION.name(),freezeReason, formatter.format(frzStartDate),formatter.format(frzEndDate));
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
				}
				logger.debug("Member current Freeze Status: {} ",memberAgreementDetail.getFreezeStatus() );
				return output; 
			}
		}
		logger.debug("No active/Future Freeze on account");


		if (isPilotUniversityFreezeEnabled(facilityId) && freezeServiceHelper.isUniversityMember(input.getMosoId())
			&& input.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_REGULAR)) {
			logger.debug("Inside Pilot University Freeze Eligibility check");
		} else if(!memberAgreementDetailsList.isEmpty()) {
			for(MemberAgreementDetail memberAgreementDetails : memberAgreementDetailsList) {
				if(!StringUtils.isEmpty(memberAgreementDetails.getFreezeStatus())) {
					if((!StringUtils.isEmpty(input.getFreezeReason())) && memberAgreementDetails.getFreezeStatus().equalsIgnoreCase(CONTRACT_STATUS_EXPIRED)) {
						if((memberAgreementDetails.getFreezeEndTime() != null && member.getMembership().getMemberSince() != null) && !memberAgreementDetails.getContractStatus().equalsIgnoreCase(CONTRACT_STATUS_PENDING) || !memberAgreementDetails.getContractStatus().equalsIgnoreCase(CONTRACT_STATUS_CANCELLED)
								|| !memberAgreementDetails.getContractStatus().equalsIgnoreCase(CONTRACT_STATUS_HOLD) || !memberAgreementDetails.getContractStatus().equalsIgnoreCase(CONTRACT_STATUS_EXPIRED)) {
							Calendar memberSince = member.getMembership().getMemberSince();
							LocalDate memberSinceDate = LocalDateTime.ofInstant(memberSince.toInstant(), memberSince.getTimeZone().toZoneId()).toLocalDate();
							LocalDate previousFreezeEndTime = memberAgreementDetails.getFreezeEndTime().toLocalDate();
							LocalDate previousFreezeStartTime = memberAgreementDetails.getFreezeStartTime().toLocalDate();
							
							LocalDate freezeStartDate = null;
							if(Objects.isNull(input.getStartDate()))
								freezeStartDate = LocalDate.now();
							else
								freezeStartDate = input.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

							int value = memberSinceDate.getMonth().getValue();
							boolean isTwoDigit = Integer.toString(Math.abs(value)).trim().length() == 2;
							String month = null;
							if(!isTwoDigit) {
								DecimalFormat df = new DecimalFormat("00");
								month = df.format(value);					
							} else {
								month=String.valueOf(value);					
							}
							String yearWithMembersinceMonth = freezeStartDate.getYear()+"-"+month+"-"+"01";
							LocalDate constrcutedDate = LocalDate.parse(yearWithMembersinceMonth);
							LocalDate endDate;
							LocalDate startDate;
							if(constrcutedDate.isBefore(freezeStartDate) || constrcutedDate.isEqual(freezeStartDate)) {
								startDate = constrcutedDate;
								endDate = constrcutedDate.plusYears(1);					
							} else { //date.isAfter(currentDate)
								startDate = constrcutedDate.minusYears(1);
								endDate = constrcutedDate;								
							}
							if(startDate.isBefore(previousFreezeStartTime) && endDate.isAfter(previousFreezeStartTime)) {
								DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/01/YYYY");
								if(memberAgreementDetails.getFreezeReason().contains(REASON_REGULAR) && input.getFreezeReason().equalsIgnoreCase(REASON_REGULAR) && (input.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || input.getSource().equalsIgnoreCase(SOURCE_CONCIERGE))) {
									output.setStatus(STATUS_ERROR);
									output.setEligibleForFreeze(false);
									ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.MEMBER_ALLOWED_TO_FREEZE_AGAIN.name(), FreezeServiceExceptionEnum.MEMBER_ALLOWED_TO_FREEZE_AGAIN.getCode(),
											ExceptionMessageEnum.VALIDATION.name(),formatter.format(endDate));
									output.setMessages(new ExceptionMessage[] { exceptionMessage });
									return output;
								} else if(memberAgreementDetails.getFreezeReason().contains(REASON_REGULAR) && input.getFreezeReason().equalsIgnoreCase(REASON_REGULAR) && input.getSource().equalsIgnoreCase(SOURCE_ENGAGE)) {
									output.setStatus(STATUS_WARNING);
									output.setEligibleForFreeze(true);
									ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.ONLY_ONE_REGULAR_FREEZE_PER_CONTRACTUAL_YEAR.name(), EngageFreezeServiceExceptionEnum.ONLY_ONE_REGULAR_FREEZE_PER_CONTRACTUAL_YEAR.getCode(),
											ExceptionMessageEnum.VALIDATION.name(),formatter.format(endDate));
									output.setMessages(new ExceptionMessage[] { exceptionMessage });
									return output;	
								}

							}
						}
					}
				}
			}
		}


		/*
		// checkMemberCorporateRule
		logger.debug("Is a Corporate Member with COF?");
		if ((memberAgreementDetail.getMembershipClass().contains("Corp") || memberAgreementDetail.getMembershipClass().contains("Subsidy")) && !hasCOF) {
			output.setStatus(STATUS_ERROR);
			ExceptionMessage exceptionMessage = setExceptionBlock("Corporate Members are not allowed to freeze",
					"Corporate Members are not allowed to freeze",
					FreezeServiceExceptionEnum.CORPORATE_MEMBER.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			logger.debug("Member has ---> {}, and no COF. So, Freeze is not allowed. ",memberAgreementDetail.getMembershipClass());
			return output;
		}
		logger.debug("Yes");
		
		// restrictPayrollDeductedMembers
		logger.debug("Is Payroll Deducted Member?");
		if (memberAgreementDetail.getBillingMethod().equalsIgnoreCase("Payroll Deducted")) {
			output.setStatus(STATUS_ERROR);
			ExceptionMessage exceptionMessage = setExceptionBlock("Payroll Deducted Members are not allowed to freeze",
					"Payroll Deducted Members are not allowed to freeze",
					FreezeServiceExceptionEnum.PAYROLL_DEDUCTED_MEMBERS.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			logger.debug("Yes: {} ",memberAgreementDetail.getBillingMethod() + "Member");
			return output;
		}
		logger.debug("No");
		*/			
		
		//https://equinoxfitness.atlassian.net/browse/SPACES-2849
		// EquinoxFamilyMembershipRule
		/* if (membershipClass.equalsIgnoreCase("Employee benefit - select")) {

			ExceptionMessage exceptionMessage = setExceptionBlock(
					"Equinox Friends and Family Members are not allowed to freeze",
					"Equinox Friends and Family Members are not allowed to freeze",
					FreezeServiceExceptionEnum.EQUINOX_FAMILY_RULE.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		} */

		// OneFreezePerContractualYearRule
		/* Commenting out the below duplicate block of code */
		/* if (input.getFreezeReason() != null && input.getStartDate() != null && input.getFreezeReason().contains(REASON_REGULAR)) {
			boolean isEligible = true;
			Date obligationDate = null;
			obligationDate = memberAgreementDetail.getObligationDate();
			List<MemberAgreementDetail> memberAgreementDetailList = new ArrayList<MemberAgreementDetail>();
			memberAgreementDetailList.add(memberAgreementDetail);
			logger.debug("obligationDate : {}", obligationDate);
			boolean isfreezedBefore = false;
			if (!isStartDateOneDayAfterPreviousEndDateV2(input, memberAgreementDetail)) {
				logger.info("Checking One Freeze Per Contractual Year..."); //
				// Check one freeze per contractual year //
				output.setStatus(STATUS_WARNING);
				try {
					isfreezedBefore = checkOneFreezePerContractualYearV2(memberAgreementDetailList, memberAgreementDetail,
							input, obligationDate);
				} catch (Exception e) {
					ExceptionMessage exceptionMessage = setExceptionBlock(
							e.getMessage(),
							"Failed to check rule for one freeze per Contractual year",
							FreezeServiceExceptionEnum.CONTRACTUAL_RULE.getCode(), "ERROR");			
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
					return output;
				}
				if (isfreezedBefore) {
					ExceptionMessage exceptionMessage = new ExceptionMessage();
					exceptionMessage.setErrorMessage("One freeze allowed per contractual year");
					exceptionMessage.setFriendlyMessage("One freeze allowed per contractual year");
					exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE.getCode());
					exceptionMessage.setMessageType("VALIDATION");
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
					output.setEligibleForFreeze(false);
					return output;
				}
			}
		} */		
		logger.debug("Yes");		
		output.setEligibleForFreeze(true);
		output.setStatus(STATUS_ELIGIBLE);
		return output;
	}
	
	private String getFreezeReason(String freezeReason) {
		if(freezeReason.contains("Regular"))
			return "Regular";
		if(freezeReason.equalsIgnoreCase("Medical"))
			return "Medical";
		if(freezeReason.equalsIgnoreCase("Pregnancy"))
			return "Pregnancy";
		return freezeReason;	
	}

	public CheckFreezeEligiblityOutput eligibilityRuleCheckV4(MemberAgreementDetail memberAgreementDetail,
			CheckFreezeEligibilityInputV2 input, Boolean hasCOF) {

		// Declaration and initialization
		String contractStatus = memberAgreementDetail.getContractStatus();
		Date cancellationDate = memberAgreementDetail.getCancellationDate();
		String membershipClass = memberAgreementDetail.getMembershipClass();
		String memberStatus = memberAgreementDetail.getMemberStatus();
		String billingMethod = memberAgreementDetail.getBillingMethod();
		long contractExpirationGreacePeriod = 5;
		CheckFreezeEligiblityOutput output = new CheckFreezeEligiblityOutput();
		long contractCancellationGreacePeriod = 15;
		output.setEligibleForFreeze(false);

		logger.debug("Inside Eligibility Rule check");
		// logger.debug("Freeze Reason {}", freezeReason);
		
		// checkAlreadyInFreeze
		logger.debug("Checking Member is already have active/Future Freeze?");
		if (memberAgreementDetail.getFreezeEndTime() != null && memberAgreementDetail.getFreezeStatus() != null) {
			Date currnetDate = Calendar.getInstance().getTime();
			if (memberAgreementDetail.getFreezeEndTime().getTime() > currnetDate.getTime()
					&& memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("active")) {
				ExceptionMessage exceptionMessage = setExceptionBlock(
						FreezeServiceExceptionEnum.MEMBER_ALREADY_REQUESTED_FREEZE.name(),
						"Member already have requested for freeze",
						FreezeServiceExceptionEnum.MEMBER_ALREADY_REQUESTED_FREEZE.getCode(), 
						"VALIDATION");
				output.setMessages(new ExceptionMessage[] { exceptionMessage });
				logger.debug("Member current Freeze Status: {} ", memberAgreementDetail.getFreezeStatus() );
				return output; 
			}
		}
		logger.debug("No active/Future Freeze on account");
		
		// checkMemberCorporateRule
		logger.debug("Is a Corporate Member with COF?");
		if ((memberAgreementDetail.getMembershipClass().contains("Corp") || 
				memberAgreementDetail.getMembershipClass().contains("Subsidy")) && !hasCOF) {
			ExceptionMessage exceptionMessage = setExceptionBlock(
					FreezeServiceExceptionEnum.CORPORATE_MEMBER.name(),
					"Corporate Members are not allowed to freeze",
					FreezeServiceExceptionEnum.CORPORATE_MEMBER.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			logger.debug("Member has ---> {}, and no COF. So, Freeze is not allowed. ", memberAgreementDetail.getMembershipClass());
			return output;
		}
		logger.debug("Yes");
		
		// restrictPayrollDeductedMembers
		logger.debug("Is Payroll Deducted Member?");
		if (memberAgreementDetail.getBillingMethod().equalsIgnoreCase("Payroll Deducted")) {
			ExceptionMessage exceptionMessage = setExceptionBlock(
					FreezeServiceExceptionEnum.PAYROLL_DEDUCTED_MEMBERS.name(),
					"Payroll Deducted Members are not allowed to freeze",
					FreezeServiceExceptionEnum.PAYROLL_DEDUCTED_MEMBERS.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			logger.debug("Yes: {} ",memberAgreementDetail.getBillingMethod() + "Member");
			return output;
		}
		logger.debug("No");
		
		// memberStatusRule
		logger.debug("contractStatus {}", contractStatus);
		if (contractStatus.equalsIgnoreCase("PENDING") || contractStatus.equalsIgnoreCase("CANCELLED")
				|| contractStatus.equalsIgnoreCase("ONHOLD") || contractStatus.equalsIgnoreCase("EXPIRED")) {
			ExceptionMessage exceptionMessage = setExceptionBlock(
					FreezeServiceExceptionEnum.MEMBER_STATUS_RULE.name(),
					"Pending/Cancelled/Expired/On Hold Member are not eligible for freeze",
					FreezeServiceExceptionEnum.MEMBER_STATUS_RULE.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		// membershipClassRule
		logger.debug("membershipClass {}", membershipClass);
		if (membershipClass.startsWith("Prospect") || membershipClass.startsWith("Short Term")
				|| membershipClass.startsWith("Comp")) {
			ExceptionMessage exceptionMessage = setExceptionBlock(
					FreezeServiceExceptionEnum.MEMBER_CLASS_RULE.name(),
					"Prospect/Short term/Complementary Members are not allowed to freeze",
					FreezeServiceExceptionEnum.MEMBER_CLASS_RULE.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		// EquinoxFamilyMembershipRule
		if (membershipClass.equalsIgnoreCase("Employee benefit - select")) {
			ExceptionMessage exceptionMessage = setExceptionBlock(
					FreezeServiceExceptionEnum.EQUINOX_FAMILY_RULE.name(),
					"Equinox Friends and Family Members are not allowed to freeze",
					FreezeServiceExceptionEnum.EQUINOX_FAMILY_RULE.getCode(), "VALIDATION");
			output.setMessages(new ExceptionMessage[] { exceptionMessage });
			return output;
		}

		// OneFreezePerContractualYearRule
		if (input.getFreezeReason() != null && input.getStartDate() != null) {
			boolean isEligible = true;
			Date obligationDate = null;
			obligationDate = memberAgreementDetail.getObligationDate();
			List<MemberAgreementDetail> memberAgreementDetailList = new ArrayList<MemberAgreementDetail>();
						logger.debug("obligationDate : {}", obligationDate);
			boolean isfreezedBefore = false;
			if (!isStartDateOneDayAfterPreviousEndDateV2(input, memberAgreementDetail)) {
				logger.info("Checking One Freeze Per Contractual Year..."); //
				// Check one freeze per contractual year //
				try {
						isfreezedBefore = checkOneFreezePerContractualYearV2(memberAgreementDetailList, memberAgreementDetail,
							input, obligationDate);
				} catch (Exception e) {
					ExceptionMessage exceptionMessage = setExceptionBlock(
							FreezeServiceExceptionEnum.CONTRACTUAL_RULE.name(),
							//e.getMessage(),
							"Failed to check rule for one freeze per Contractual year",
							FreezeServiceExceptionEnum.CONTRACTUAL_RULE.getCode(), "ERROR");			
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
					return output;
				}
				if (isfreezedBefore) {
					ExceptionMessage exceptionMessage = new ExceptionMessage();
					exceptionMessage.setErrorMessage(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE.name());
					exceptionMessage.setFriendlyMessage("One freeze allowed per contractual year");
					exceptionMessage.setMessageID(FreezeServiceExceptionEnum.FREEZE_DURATION_RULE.getCode());
					exceptionMessage.setMessageType("VALIDATION");
					output.setMessages(new ExceptionMessage[] { exceptionMessage });
					output.setEligibleForFreeze(false);
					return output;
				}
			}
		}
		output.setEligibleForFreeze(true);
		return output;
	}
	
	private ExceptionMessage setExceptionBlock(String errorMessage, String friendlyMessage, int messageId,
			String messageType) {

		ExceptionMessage exceptionMessage = new ExceptionMessage();
		exceptionMessage.setErrorMessage(errorMessage);
		exceptionMessage.setFriendlyMessage(friendlyMessage);
		exceptionMessage.setMessageID(messageId);
		exceptionMessage.setMessageType(messageType);
		return exceptionMessage;
	}

/*	private Date calculateEndDate(Date time, int durationMonths, int durationDays) {

		Calendar endDateCal = Calendar.getInstance();
		endDateCal.setTime(time);
		if (durationMonths != 0) {
			endDateCal.add(Calendar.MONTH, durationMonths);
		}
		if (durationDays != 0) {
			endDateCal.add(Calendar.DATE, durationDays);
		}

		endDateCal.add(Calendar.DATE, -1);
		return endDateCal.getTime();
	}*/

	private boolean isStartDateOneDayAfterPreviousEndDate(CheckFreezeEligibilityInput checkFreezeEligibilityInput,
			MemberAgreementDetail currentContract) {

		logger.debug(
				"Checking if the current start date is one day after the existing previous FreezeEndDate if the freezeReasons are the same..");

		boolean isStartDateOneDayAfterEndDate = false;

		if (currentContract != null && checkFreezeEligibilityInput != null && currentContract.getFreezeReason() != null
				&& checkFreezeEligibilityInput.getStartDate() != null && currentContract.getFreezeEndTime() != null) {

			if (currentContract.getFreezeReason().startsWith(checkFreezeEligibilityInput.getFreezeReason())
					&& FREEZE_STATUS_ACTIVE.equalsIgnoreCase(currentContract.getFreezeStatus())) {
				logger.debug("Current Start date: {}", checkFreezeEligibilityInput.getStartDate());
				logger.debug("Previous FreezeEndDate {} ", currentContract.getFreezeEndTime());

				Calendar startDate = Calendar.getInstance();
				startDate.setTime(checkFreezeEligibilityInput.getStartDate());

				Calendar previousFreezeEndDate = Calendar.getInstance();
				previousFreezeEndDate.setTime(currentContract.getFreezeEndTime());
				// Adding one day to the previous end date..
				previousFreezeEndDate.add(Calendar.DATE, 1);

				logger.debug("Calendar converted startDate: {}", startDate.getTime());
				logger.debug("Calendar converted previousFreezeEndDate: {}", previousFreezeEndDate.getTime());

				isStartDateOneDayAfterEndDate = (startDate.getTimeInMillis() == previousFreezeEndDate
						.getTimeInMillis());
				if (!isStartDateOneDayAfterEndDate) {
					logger.info(
							"Checking one freeze per contractual year instead of freeze extension since StartDate is not one day after previous FreezeEndDate...");
				}
			} else {
				logger.info("Either the current contract's freeze reason : " + currentContract.getFreezeReason()
						+ " does not start with : " + checkFreezeEligibilityInput.getFreezeReason()
						+ " and/or currentContract.getFreezeStatus() : " + currentContract.getFreezeStatus()
						+ " is not Active...");
				logger.info("Checking one freeze per contractual year instead of freeze extension...");
			}
		}

		logger.debug("Current Start date one day after existing FreezeEndDate? {}", isStartDateOneDayAfterEndDate);
		return isStartDateOneDayAfterEndDate;
	}

	private boolean isStartDateOneDayAfterPreviousEndDateV2(CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput,
			MemberAgreementDetail currentContract) {

		logger.debug(
				"Checking if the current start date is one day after the existing previous FreezeEndDate if the freezeReasons are the same..");

		boolean isStartDateOneDayAfterEndDate = false;

		if (currentContract != null && checkFreezeEligibilityInput != null && currentContract.getFreezeReason() != null
				&& checkFreezeEligibilityInput.getStartDate() != null && currentContract.getFreezeEndTime() != null) {

			if (currentContract.getFreezeReason().startsWith(checkFreezeEligibilityInput.getFreezeReason())
					&& FREEZE_STATUS_ACTIVE.equalsIgnoreCase(currentContract.getFreezeStatus())) {
				logger.debug("Current Start date: {}", checkFreezeEligibilityInput.getStartDate());
				logger.debug("Previous FreezeEndDate {} ", currentContract.getFreezeEndTime());

				Calendar startDate = Calendar.getInstance();
				startDate.setTime(checkFreezeEligibilityInput.getStartDate());

				Calendar previousFreezeEndDate = Calendar.getInstance();
				previousFreezeEndDate.setTime(currentContract.getFreezeEndTime());
				// Adding one day to the previous end date..
				previousFreezeEndDate.add(Calendar.DATE, 1);

				logger.debug("Calendar converted startDate: {}", startDate.getTime());
				logger.debug("Calendar converted previousFreezeEndDate: {}", previousFreezeEndDate.getTime());

				isStartDateOneDayAfterEndDate = (startDate.getTimeInMillis() == previousFreezeEndDate
						.getTimeInMillis());
				if (!isStartDateOneDayAfterEndDate) {
					logger.info(
							"Checking one freeze per contractual year instead of freeze extension since StartDate is not one day after previous FreezeEndDate...");
				}
			} else {
				logger.info("Either the current contract's freeze reason : " + currentContract.getFreezeReason()
						+ " does not start with : " + checkFreezeEligibilityInput.getFreezeReason()
						+ " and/or currentContract.getFreezeStatus() : " + currentContract.getFreezeStatus()
						+ " is not Active...");
				logger.info("Checking one freeze per contractual year instead of freeze extension...");
			}
		}

		logger.debug("Current Start date one day after existing FreezeEndDate? {}", isStartDateOneDayAfterEndDate);
		return isStartDateOneDayAfterEndDate;
	}
	
	private boolean checkOneFreezePerContractualYearV2(List<MemberAgreementDetail> currentContractList,
			MemberAgreementDetail currentContract, CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput,
			Date obligationDate) throws Exception {

		logger.info("Inside FreezeEligibilityHelper.checkOneFreezePerContractualYear method..");
		logger.info("checkFreezeEligibilityInput.getFreezeReason() : {}",
				checkFreezeEligibilityInput.getFreezeReason());
		boolean isFreezedBefore = false;

		if ("REGULAR".equalsIgnoreCase(checkFreezeEligibilityInput.getFreezeReason())) {
			logger.info("Checking contractual year freeze history of Regular/Pregnancy/Medical for member : {}",
					checkFreezeEligibilityInput.getMosoId());
			isFreezedBefore = checkFreezeHistoryV2(currentContractList, currentContract, checkFreezeEligibilityInput,
					obligationDate);
		}
		return isFreezedBefore;
	}

	private boolean checkOneFreezePerContractualYear(List<MemberAgreementDetail> currentContractList,
			MemberAgreementDetail currentContract, CheckFreezeEligibilityInput checkFreezeEligibilityInput,
			Date obligationDate) throws Exception {

		logger.info("Inside FreezeEligibilityHelper.checkOneFreezePerContractualYear method..");
		logger.info("checkFreezeEligibilityInput.getFreezeReason() : {}",
				checkFreezeEligibilityInput.getFreezeReason());
		boolean isFreezedBefore = false;

		if ("MEDICAL".equalsIgnoreCase(checkFreezeEligibilityInput.getFreezeReason())
				|| "REGULAR".equalsIgnoreCase(checkFreezeEligibilityInput.getFreezeReason())
				|| "PREGNANCY".equalsIgnoreCase(checkFreezeEligibilityInput.getFreezeReason())) {
			logger.info("Checking contractual year freeze history of Regular/Pregnancy/Medical for member : {}",
					checkFreezeEligibilityInput.getMemberId());
			isFreezedBefore = checkFreezeHistory(currentContractList, currentContract, checkFreezeEligibilityInput,
					obligationDate);
		}
		return isFreezedBefore;
	}

	/*private boolean checkFreezeExtension(CheckFreezeEligibilityInput checkFreezeEligibilityInput,
			List<MemberAgreementDetail> currentContractList, MemberAgreementDetail currentContract, Date obligationDate)
			throws Exception {
		logger.debug("Inside FreezeEligibilityHelper.checkFreezeExtension method..");
		boolean isEligible = false;
		if (checkFreezeEligibilityInput.getStartDate() != null
				&& checkFreezeEligibilityInput.getDurationMonths() != 0) {
			switch (FreezeReasonEnum.getFreezeReasonFromValue(checkFreezeEligibilityInput.getFreezeReason())) {
			case REGULAR:
				isEligible = validateFreezeExtension(checkFreezeEligibilityInput, currentContractList, currentContract,
						obligationDate, 3);
				break;
			case PREGNANCY:
				isEligible = validateFreezeExtension(checkFreezeEligibilityInput, currentContractList, currentContract,
						obligationDate, 9);
				break;
			case MEDICAL:
				isEligible = validateFreezeExtension(checkFreezeEligibilityInput, currentContractList, currentContract,
						obligationDate, 6);
				break;
			}
		}
		return isEligible;
	}

	public boolean validateFreezeExtension(CheckFreezeEligibilityInput freezeRequest,
			List<MemberAgreementDetail> memberCurrentContractList, MemberAgreementDetail memberCurrentContract,
			Date obligationDate, Integer freezeDurationLimit) throws Exception {
		logger.debug("Inside FreezeEligibilityHelper.validateFreezeExtension method..");
		int previousFreezeDuration = 0;
		int currentFreezeDuration = 0;
		int newFreezeDuration = 0;
		int totalFreezeDuration = 0;
		String failureReason = "";
		// Get the member Id
		String memberId = freezeRequest.getMemberId();
		// get the freeze reason
		String freezeReason = freezeRequest.getFreezeReason();
		// get Current Contract
		// get the new freeze start date and set a calender
		Calendar newFreezeStartDateCalendar = Calendar.getInstance();
		newFreezeStartDateCalendar.setTime(freezeRequest.getStartDate());
		// set the new freeze end date
		Calendar newFreezeEndDateCalendar = Calendar.getInstance();
		newFreezeEndDateCalendar.setTime(freezeRequest.getStartDate());
		newFreezeEndDateCalendar.add(Calendar.MONTH, freezeRequest.getDurationMonths());

		// get the current freeze start date and set a calender
		if (memberCurrentContract.getFreezeStartTime() != null && memberCurrentContract.getFreezeEndTime() != null) {
			Calendar currentFreezeStartDateCalendar = Calendar.getInstance();
			currentFreezeStartDateCalendar.setTime(memberCurrentContract.getFreezeStartTime());
			// get the current freeze end date and set a calender
			Calendar currentFreezeEndDateCalendar = Calendar.getInstance();
			currentFreezeEndDateCalendar.setTime(memberCurrentContract.getFreezeEndTime());
			if (freezeRequest.getFreezeReason() != null
					&& !memberCurrentContract.getFreezeReason().startsWith(freezeRequest.getFreezeReason())) {
				// different freeze type not a valid extension
				logger.error("The new freeze reason : " + freezeRequest.getFreezeReason()
						+ " is different from the current contract's freeze reason : "
						+ memberCurrentContract.getFreezeReason());
				failureReason = "The member is on " + memberCurrentContract.getFreezeReason()
						+ " freeze and hence is not eligible for " + freezeRequest.getFreezeReason() + " freeze";
			} else {
				// check for a gap between the new and the old freeze
				logger.debug(
						"The new freeze reason is same as the contract's freeze reason...Calculating total duration...");
				// Following "IF" condition is redundant and can be removed.
				if (!isStartDateOneDayAfterPreviousEndDate(freezeRequest, memberCurrentContract)) {
					logger.error("The new freeze start date should be one day after the current freeze End-Date");
					failureReason = "The new freeze start date should be one day after the current freeze End-Date";
				} else {
					// check for a limit extension
					// check to the history of the member to see if there was any freeze
					previousFreezeDuration = getPreviousFreezeDuration(memberId, memberCurrentContractList,
							memberCurrentContract, obligationDate, freezeReason, freezeRequest.getStartDate(), true);

					logger.debug("Previous contract's FreezeDuration : {}", previousFreezeDuration);
					// Get the current freeze duration
					currentFreezeDuration = getDurationInMonths(memberCurrentContract);
					logger.debug("Current contract's FreezeDuration in months : {}", currentFreezeDuration);

					// get the new freeze duration
					newFreezeDuration = freezeRequest.getDurationMonths();

					totalFreezeDuration = previousFreezeDuration + currentFreezeDuration + newFreezeDuration;
					logger.debug("Total FreezeDuration for member to check eligibility: {}", totalFreezeDuration);

					logger.debug("FreezeDurationLimit for a '" + freezeRequest.getFreezeReason() + "' freeze is : "
							+ freezeDurationLimit);
					// add a failure reason and return the call
					if (totalFreezeDuration > freezeDurationLimit.intValue()) {
						failureReason = " Freeze already scheduled and the new request is not a valid extension";
					}
				}
			}
		}
		if (StringUtils.isBlank(failureReason)) {
			return true;
		}
		return false;
	}*/

	private int getPreviousFreezeDuration(String memberId, List<MemberAgreementDetail> detailsDVOList,
			MemberAgreementDetail memberCurrentContract, Date obligationDate, String freezeReason,
			Date considerationDate, boolean isExtension) throws Exception {
		logger.debug("Inside FreezeEligibilityHelper.getPreviousFreezeDuration method..");
		int previousFreezesduration = 0;

		/*if (isExtension) {
			logger.info("Calculating freeze duration for freeze extension...");
		} else {*/
			logger.info("Calculating freeze duration for total freeze per contractual year...");
		//}

		if (detailsDVOList != null) {
			logger.info("Size of MemberContractDetailsDVO list : {}", detailsDVOList.size());
		}

		// get the member current contract
		// get Contract Type
		if (memberCurrentContract.getAgreementType() != null
				&& memberCurrentContract.getAgreementType().equalsIgnoreCase("Fixed")) {
			logger.debug("Calculating past duration for a fixed agreement member : {}", memberId);
			// For paid in full members, we check the Contract-Number
			Integer currentContractNumber = Integer.valueOf(memberCurrentContract.getMemberAgreementId());
			Integer currentContractId = Integer.valueOf(memberCurrentContract.getAgreementId());

			logger.info("currentContractNumber : {}", currentContractNumber);
			logger.info("currentContractId : {}", currentContractId);

			for (MemberAgreementDetail processedContract : detailsDVOList) {
				// check contracts with the same contract number
				Integer processedContractNumber = Integer.valueOf(processedContract.getMemberAgreementId());
				Integer processedContractId = Integer.valueOf(processedContract.getAgreementId());

				logger.info("processedContractNumber : {}", processedContractNumber);
				logger.info("processedContractId : {}", processedContractId);
				logger.info("processedContract.getContractStatus() : {}", processedContract.getContractStatus());
				logger.info("processedContract.getFreezeReason() : {}", processedContract.getFreezeReason());
				logger.info("processedContract.getStartDate() : {}", processedContract.getStartDate());
				logger.info("processedContract.getEndDate() : {}", processedContract.getEndDate());

				/*if (isExtension) {
					if (processedContractNumber.equals(currentContractNumber)
							&& processedContractId != currentContractId && processedContract.getContractStatus() != null
							&& CONTRACT_STATUS_ON_FREEZE.equalsIgnoreCase(processedContract.getContractStatus())
							&& processedContract.getFreezeReason() != null
							&& processedContract.getFreezeReason().startsWith(freezeReason)
							&& processedContract.getStartDate() != null && processedContract.getEndDate() != null) {
						// get the freeze duration for that freeze
						logger.info("Getting duration in months to increment previousFreezesduration");
						previousFreezesduration += getDurationInMonths(processedContract);
					}
				} else {*/
					if (processedContractNumber.equals(currentContractNumber)
							&& processedContract.getContractStatus() != null
							&& processedContract.getFreezeReason() != null
							&& (processedContract.getFreezeReason().startsWith("REGULAR")
									|| processedContract.getFreezeReason().startsWith("PREGNANCY")
									|| processedContract.getFreezeReason().startsWith("MEDICAL")
									|| processedContract.getFreezeReason().startsWith("EXTERNAL"))
							&& processedContract.getStartDate() != null && processedContract.getEndDate() != null) {
						// get the freeze duration for that freeze
						logger.info("Getting duration in months to increment previousFreezesduration");
						previousFreezesduration += getDurationInMonths(processedContract);
					}
			//	}
			}
			logger.debug(
					"Previous FreezeDuration for the fixed member : " + memberId + " is : " + previousFreezesduration);
		} else {
			logger.debug("Calculating past duration for a non fixed agreement member : {} ", memberId);
			// for bill monthly members
			if (obligationDate != null) {
				Calendar obligationDateCalendar = Calendar.getInstance();
				obligationDateCalendar.setTime(obligationDate);
				Calendar considerationDateCalendar = Calendar.getInstance();
				considerationDateCalendar.setTime(considerationDate);
				Calendar contractualYearStartDateCal = Calendar.getInstance();
				contractualYearStartDateCal.setTime(obligationDate);
				if (considerationDateCalendar.getTimeInMillis() <= obligationDateCalendar.getTimeInMillis()) {
					logger.debug("ConsiderationDate is same as or before the obligationDate...");
					// subtract 1 year from the obligation date
					logger.debug(
							"Subtracting 1 year from the contractualYearStartDate since considerationDate is lesser than obligationDate...");
					contractualYearStartDateCal.add(Calendar.YEAR, -1);
				} else {
					logger.debug("ConsiderationDate is beyond the obligationDate...");
					while (contractualYearStartDateCal.get(Calendar.YEAR) < considerationDateCalendar
							.get(Calendar.YEAR)) {
						contractualYearStartDateCal.add(Calendar.YEAR, 1);
					}
					// subtract 1 year from the obligation date
					// Added 5/14/09 - SD
					if (contractualYearStartDateCal.getTimeInMillis() > Calendar.getInstance().getTimeInMillis())
						contractualYearStartDateCal.add(Calendar.YEAR, -1);
				}

				logger.debug("Fetching a list of contract's for a year...");
				logger.debug("Contractual year start date :{} ", contractualYearStartDateCal.getTime());
				// get a list of one year contract
				ArrayList<MemberAgreementDetail> applicableContracts = getOneYearContractHistory(memberId,
						detailsDVOList, contractualYearStartDateCal.getTime());

				if (applicableContracts != null && applicableContracts.size() > 0) {
					/*
					 * loop through the one year list and extract any contract With the same freeze
					 * reason as the current contract and contract status is on freeze and the
					 * contract Id is not equal to current contract id
					 * 
					 */
					for (MemberAgreementDetail processedContract : detailsDVOList) {

						logger.info("ProcessedContract.getFreezeReason : {}", processedContract.getFreezeReason());
						logger.info("processedContract.getStartDate() : {}", processedContract.getStartDate());
						logger.info("processedContract.getEndDate() : {}", processedContract.getEndDate());
						logger.info("processedContract.getContractStatus() : {}",
								processedContract.getContractStatus());

					/*	if (isExtension) {
							if (CONTRACT_STATUS_ON_FREEZE.equalsIgnoreCase(processedContract.getContractStatus())
									&& processedContract.getFreezeReason().startsWith(freezeReason)
									&& processedContract.getStartDate() != null
									&& processedContract.getEndDate() != null) {
								logger.info("Getting duration in months to increment previousFreezesduration...");
								// get the freeze duration for that freeze
								previousFreezesduration += getDurationInMonths(processedContract);
							}
						} else {*/
							if ((processedContract.getFreezeReason().equalsIgnoreCase("REGULAR")
									|| processedContract.getFreezeReason().equalsIgnoreCase("PREGNANCY")
									|| processedContract.getFreezeReason().equalsIgnoreCase("MEDICAL")
									|| processedContract.getFreezeReason().equalsIgnoreCase("EXTERNAL"))
									&& processedContract.getStartDate() != null
									/*&& processedContract.getEndDate() != null*/) {
								logger.info("Getting duration in months to increment previousFreezesduration...");
								// get the freeze duration for that freeze
								previousFreezesduration += getDurationInMonths(processedContract);
							}
						//}
					}
					logger.info("PreviousFreezesduration for a pertual member Id : " + memberId + " is : "
							+ previousFreezesduration);
				}

			} else {
				logger.error("No Obligation date present for a perpetual member (" + memberId + ")");
				throw new Exception("No Obligation date present for a perpetual member (" + memberId + ")");
			}
		}
		return previousFreezesduration;

	}

	private List<MemberAgreementDetail> getContractualYearHistory(String memberId, List<MemberAgreementDetail> detailsDVOList, MemberAgreementDetail memberCurrentContract, Date obligationDate, String freezeReason, Date considerationDate) throws Exception {
		logger.debug("Inside FreezeEligibilityHelper.getContractualYearHistory method..");
		List<Member> memberList = new ArrayList<Member>();
		// get the list of members
		// loop through the list and get the contractual year history
		if (obligationDate != null) {
			Calendar obligationDateCalendar = Calendar.getInstance();
			obligationDateCalendar.setTime(obligationDate);
			Calendar considerationDateCalendar = Calendar.getInstance();
			considerationDateCalendar.setTime(considerationDate);
			Calendar contractualYearStartDateCal = Calendar.getInstance();
			contractualYearStartDateCal.setTime(obligationDate);
			if (considerationDateCalendar.getTimeInMillis() <= obligationDateCalendar.getTimeInMillis()) {
				logger.debug("ConsiderationDate is same as or before the obligationDate...");
				// subtract 1 year from the obligation date
				logger.debug(
						"Subtracting 1 year from the contractualYearStartDate since considerationDate is lesser than obligationDate...");
				contractualYearStartDateCal.add(Calendar.YEAR, -1);
			} else {
				logger.debug("ConsiderationDate is beyond the obligationDate...");
				while (contractualYearStartDateCal.get(Calendar.YEAR) < considerationDateCalendar
						.get(Calendar.YEAR)) {
					contractualYearStartDateCal.add(Calendar.YEAR, 1);
				}
				// subtract 1 year from the obligation date
				// Added 5/14/09 - SD
				if (contractualYearStartDateCal.getTimeInMillis() > Calendar.getInstance().getTimeInMillis())
					contractualYearStartDateCal.add(Calendar.YEAR, -1);
			}

			logger.debug("Fetching a list of contract's for a year...");
			logger.debug("Contractual year start date :{} ", contractualYearStartDateCal.getTime());
			// get a list of one year contract
			ArrayList<MemberAgreementDetail> applicableContracts = getOneYearContractHistoryV2(memberId,
					detailsDVOList, contractualYearStartDateCal.getTime());
			return applicableContracts;
		} else {
			logger.error("No Obligation date present for a perpetual member (" + memberId + ")");
			return Collections.emptyList();
		}
	}


	private int getPreviousFreezeDurationV2(String memberId, List<MemberAgreementDetail> detailsDVOList,
										  MemberAgreementDetail memberCurrentContract, Date obligationDate, String freezeReason,
										  Date considerationDate, boolean isExtension) throws Exception {
		logger.debug("Inside FreezeEligibilityHelper.getPreviousFreezeDuration method..");
		int previousFreezesduration = 0;

		if (detailsDVOList != null) {
			logger.info("Size of MemberContractDetailsDVO list : {}", detailsDVOList.size());
		}

		logger.debug("Calculating past duration for a non fixed agreement member : {} ", memberId);
		// for bill monthly members
		if (obligationDate != null) {
			Calendar obligationDateCalendar = Calendar.getInstance();
			obligationDateCalendar.setTime(obligationDate);
			Calendar considerationDateCalendar = Calendar.getInstance();
			considerationDateCalendar.setTime(considerationDate);
			Calendar contractualYearStartDateCal = Calendar.getInstance();
			contractualYearStartDateCal.setTime(obligationDate);
			if (considerationDateCalendar.getTimeInMillis() <= obligationDateCalendar.getTimeInMillis()) {
				logger.debug("ConsiderationDate is same as or before the obligationDate...");
				// subtract 1 year from the obligation date
				logger.debug(
						"Subtracting 1 year from the contractualYearStartDate since considerationDate is lesser than obligationDate...");
				contractualYearStartDateCal.add(Calendar.YEAR, -1);
			} else {
				logger.debug("ConsiderationDate is beyond the obligationDate...");
				while (contractualYearStartDateCal.get(Calendar.YEAR) < considerationDateCalendar
						.get(Calendar.YEAR)) {
					contractualYearStartDateCal.add(Calendar.YEAR, 1);
				}
				// subtract 1 year from the obligation date
				// Added 5/14/09 - SD
				if (contractualYearStartDateCal.getTimeInMillis() > Calendar.getInstance().getTimeInMillis())
					contractualYearStartDateCal.add(Calendar.YEAR, -1);
			}

			logger.debug("Fetching a list of contract's for a year...");
			logger.debug("Contractual year start date :{} ", contractualYearStartDateCal.getTime());
			// get a list of one year contract
			ArrayList<MemberAgreementDetail> applicableContracts = getOneYearContractHistoryV2(memberId,
					detailsDVOList, contractualYearStartDateCal.getTime());

			if (applicableContracts != null && applicableContracts.size() > 0) {
				/*
				 * loop through the one year list and extract any contract With the same freeze
				 * reason as the current contract and contract status is on freeze and the
				 * contract Id is not equal to current contract id
				 *
				 */
				for (MemberAgreementDetail processedContract : detailsDVOList) {

					logger.info("ProcessedContract.getFreezeReason : {}", processedContract.getFreezeReason());
					logger.info("processedContract.getStartDate() : {}", processedContract.getStartDate());
					logger.info("processedContract.getEndDate() : {}", processedContract.getEndDate());
					logger.info("processedContract.getContractStatus() : {}",
							processedContract.getContractStatus());

					if ((processedContract.getFreezeReason().contains("Regular"))
							&& processedContract.getStartDate() != null
						/*&& processedContract.getEndDate() != null*/) {
						logger.info("Getting duration in months to increment previousFreezesduration...");
						// get the freeze duration for that freeze
						previousFreezesduration += getDurationInMonths(processedContract);
					}
					//}
				}
				logger.info("PreviousFreezesduration for a pertual member Id : " + memberId + " is : "
						+ previousFreezesduration);
			}

		} else {
			logger.error("No Obligation date present for a perpetual member (" + memberId + ")");
			throw new Exception("No Obligation date present for a perpetual member (" + memberId + ")");
		}

		return previousFreezesduration;

	}

	private int getDurationInMonths(MemberAgreementDetail processedContract) {
		logger.debug("Inside FreezeEligibilityHelper.getDurationInMonths method..");
		int freezeDurationInMonths = 0;
		if (processedContract.getFreezeEndTime() != null && processedContract.getFreezeStartTime() != null) {
			logger.info("Calculating freeze duration..");
			Calendar freezeStartDate = Calendar.getInstance();
			freezeStartDate.setTime(processedContract.getFreezeStartTime());
			Calendar freezeEndDate = Calendar.getInstance();
			freezeEndDate.setTime(processedContract.getFreezeEndTime());
			int diffYear = freezeEndDate.get(Calendar.YEAR) - freezeStartDate.get(Calendar.YEAR);
			freezeDurationInMonths = ((diffYear * 12) + freezeEndDate.get(Calendar.MONTH)
					- freezeStartDate.get(Calendar.MONTH));
			logger.info("freezeDurationInMonths : {}", freezeDurationInMonths);
		}
		return freezeDurationInMonths;
	}

	private ArrayList<MemberAgreementDetail> getOneYearContractHistory(String memberId,
			List<MemberAgreementDetail> detailsDVOList, Date ConsiderationDate) throws Exception {
		logger.debug("Inside FreezeEligibilityHelper.getOneYearContractHistory method..");
		MemberAgreementDetail memberCurrentContract = getCurrentContract(detailsDVOList);
		ArrayList<MemberAgreementDetail> validContracts = new ArrayList<MemberAgreementDetail>();
		ArrayList<MemberAgreementDetail> oneYearContractHistory = new ArrayList<MemberAgreementDetail>();
		ArrayList<MemberAgreementDetail> applicableContracts = new ArrayList<MemberAgreementDetail>();
		Calendar considerationDateCal = Calendar.getInstance();
		considerationDateCal.setTime(ConsiderationDate);

		/*
		 * looping through the contract history and filtering out all contracts except
		 * the ones that has status of On Freeze, Active, Pending
		 * 
		 */
		Integer currentContractId = Integer.valueOf(memberCurrentContract.getMemberAgreementId());
		for (MemberAgreementDetail processedContract : detailsDVOList) {
			Integer processedContractId = Integer.valueOf(processedContract.getMemberAgreementId());

			if (processedContractId != currentContractId && processedContract.getContractStatus() != null
					&& (CONTRACT_STATUS_ON_FREEZE.equalsIgnoreCase(processedContract.getContractStatus())
							|| CONTRACT_STATUS_ACTIVE.equalsIgnoreCase(processedContract.getContractStatus())
							|| CONTRACT_STATUS_PENDING.equalsIgnoreCase(processedContract.getContractStatus()))) {
				logger.debug("Adding a valid contract...");
				validContracts.add(processedContract);
			}

		}

		if (validContracts != null && validContracts.size() > 0) {
			logger.debug("check1");
			/*
			 * looping through the valid contract list and filtering out any contract that
			 * is not within 1 contractual year
			 */
			for (MemberAgreementDetail processedContract : detailsDVOList) {
				Calendar processedContractEffectiveDateCal = Calendar.getInstance();
				processedContractEffectiveDateCal.setTime(processedContract.getStartDate());
				logger.debug("Processed "+processedContractEffectiveDateCal.getTimeInMillis() );
				logger.debug("Processed1 "+considerationDateCal.getTimeInMillis());
				if (processedContractEffectiveDateCal.getTimeInMillis() >= considerationDateCal.getTimeInMillis()) {
					oneYearContractHistory.add(processedContract);
					logger.debug("check2");
				}
			}

			// sort the list from the latest contract to the earliest one
			if (oneYearContractHistory != null && oneYearContractHistory.size() > 0) {
				logger.debug("OneYearContractHistory size before sorting : {} ", oneYearContractHistory.size());

				Collections.sort(oneYearContractHistory, new Comparator<MemberAgreementDetail>() {

					public int compare(MemberAgreementDetail contract1, MemberAgreementDetail contract2) {

						int returnResult = 0;

						Integer contract1Id = Integer.valueOf(contract1.getMemberAgreementId());
						Integer contract2Id = Integer.valueOf(contract2.getMemberAgreementId());
						// sort descending
						if (contract1Id > contract2Id) {
							returnResult = -1;
						}
						if (contract1Id < contract2Id) {
							returnResult = 1;
						}
						if (contract1Id == contract2Id) {
							returnResult = 0;
						}

						return returnResult;
					}

				});

				MemberAgreementDetail currentContract = memberCurrentContract;
				/*
				 * looping through one year contract history and filtering out those contract
				 * with a gap of more than 90 days
				 */
				long msDiff = 0;
				long daysDiff = 0;
				for (MemberAgreementDetail processedContract : detailsDVOList) {
					if (currentContract.getMemberAgreementId() != processedContract.getMemberAgreementId()) {
						// Get the effective date of the current contract
						Calendar effectiveDateCal = Calendar.getInstance();
						Date effectiveDate = currentContract.getStartDate();
						effectiveDateCal.setTime(effectiveDate);
						// Get the valid to date of the current contract
						Calendar validToDateCal = Calendar.getInstance();
						if (processedContract.getEndDate() != null) {
							Date validToDate = processedContract.getEndDate();
							validToDateCal.setTime(validToDate);

							/*
							 * calculate the difference between the current contract effective date and the
							 * proccessed contract valid date
							 * 
							 */
							msDiff = effectiveDateCal.getTimeInMillis() - validToDateCal.getTimeInMillis();
							daysDiff = msDiff / (24 * 60 * 60 * 1000);
							logger.debug(
									"Difference between the current contract effective date and the proccessed contract valid date : {}",
									daysDiff);
							logger.debug("ContractCutOffTimeInDays : {}", contractCutOffTimeInDays);
							if (daysDiff < contractCutOffTimeInDays) {

								/*
								 * valid contract, the gap is less than 90 days add the processed contract to
								 * the applicable contract list set the current contrac to the processed one and
								 * continue, otherwise break
								 */

								applicableContracts.add(processedContract);
								currentContract = processedContract;

							} else {
								logger.debug(
										"ContractCutOffTimeInDays is more than the difference between the current contract effective date and the proccessed contract valid date... ");
								break;
							}

						}
					}
				}
			}

		}
		return applicableContracts;
	}

	private ArrayList<MemberAgreementDetail> getOneYearContractHistoryV2(String memberId,
																	   List<MemberAgreementDetail> detailsDVOList, Date ConsiderationDate) throws Exception {
		logger.debug("Inside FreezeEligibilityHelper.getOneYearContractHistory method..");
		MemberAgreementDetail memberCurrentContract = getCurrentContractV2(detailsDVOList);
		ArrayList<MemberAgreementDetail> validContracts = new ArrayList<MemberAgreementDetail>();
		ArrayList<MemberAgreementDetail> oneYearContractHistory = new ArrayList<MemberAgreementDetail>();
		Calendar considerationDateCal = Calendar.getInstance();
		considerationDateCal.setTime(ConsiderationDate);
		Map<String, MemberAgreementDetail> uniqueContractsMap = new LinkedHashMap<>();


		/*
		 * looping through the contract history and filtering out all contracts except
		 * the ones that has status of On Freeze, Active, Pending
		 *
		 */
		Integer currentContractId = Integer.valueOf(memberCurrentContract.getMemberAgreementId());
		for (MemberAgreementDetail processedContract : detailsDVOList) {
			Integer processedContractId = Integer.valueOf(processedContract.getMemberAgreementId());

			if (processedContractId != currentContractId && processedContract.getContractStatus() != null
					&& (CONTRACT_STATUS_ON_FREEZE.equalsIgnoreCase(processedContract.getContractStatus())
					|| CONTRACT_STATUS_ACTIVE.equalsIgnoreCase(processedContract.getContractStatus())
					|| CONTRACT_STATUS_PENDING.equalsIgnoreCase(processedContract.getContractStatus()))) {
				logger.debug("Adding a valid contract...");
				uniqueContractsMap.put(processedContract.getMemberAgreementId(), memberCurrentContract);
			}

		}

		return new ArrayList<>(uniqueContractsMap.values());
	}

	private MemberAgreementDetail getCurrentContract(List<MemberAgreementDetail> detailsDVOList) {

		logger.debug("Inside FreezeEligibilityHelper.getCurrentContract method..");
		Collections.sort(detailsDVOList, new Comparator<MemberAgreementDetail>() {

			public int compare(MemberAgreementDetail contract1, MemberAgreementDetail contract2) {

				int returnResult = 0;

				Integer contract1Id = Integer.valueOf(contract1.getMemberAgreementId());
				Integer contract2Id = Integer.valueOf(contract2.getMemberAgreementId());
				// sort descending
				if (contract1Id > contract2Id) {
					returnResult = -1;
				}
				if (contract1Id < contract2Id) {
					returnResult = 1;
				}
				if (contract1Id == contract2Id) {
					returnResult = 0;
				}
				return returnResult;
			}

		});

		for (MemberAgreementDetail memberContractDetailsDVO : detailsDVOList) {
			if (memberContractDetailsDVO.getFreezeStatus() == null
					|| FREEZE_STATUS_ACTIVE.equalsIgnoreCase(memberContractDetailsDVO.getFreezeStatus())) {
				return memberContractDetailsDVO;
			}
		}
		
		return detailsDVOList.get(0);

	}
	
	private MemberAgreementDetail getCurrentContractV2(List<MemberAgreementDetail> detailsDVOList) {

		logger.debug("Inside FreezeEligibilityHelper.getCurrentContract method..");
		Collections.sort(detailsDVOList, new Comparator<MemberAgreementDetail>() {

			public int compare(MemberAgreementDetail contract1, MemberAgreementDetail contract2) {

				int returnResult = 0;

				Integer contract1Id = Integer.valueOf(contract1.getMemberAgreementId());
				Integer contract2Id = Integer.valueOf(contract2.getMemberAgreementId());
				// sort descending
				if (contract1Id > contract2Id) {
					returnResult = -1;
				}
				if (contract1Id < contract2Id) {
					returnResult = 1;
				}
				if (contract1Id == contract2Id) {
					returnResult = 0;
				}
				return returnResult;
			}

		});

		Optional<MemberAgreementDetail> currentAgrrement =  detailsDVOList.stream()
		.filter(agreement -> agreement.getStartDate() != null && agreement.getObligationDate() != null) // Ensure dates are not null
		.max(Comparator.comparing(MemberAgreementDetail::getObligationDate)
				.thenComparing(MemberAgreementDetail::getStartDate));
		if (currentAgrrement.isPresent())
			return currentAgrrement.get();
		return null;

	}

	private boolean checkFreezeHistory(List<MemberAgreementDetail> detailsDVOList,
			MemberAgreementDetail currentContract, CheckFreezeEligibilityInput checkFreezeEligibilityInput,
			Date obligationDate) throws Exception {
		boolean hasFreezed = false;
		logger.info("Inside FreezeEligibilityHelper.checkFreezeHistory method..");
		String memberId = checkFreezeEligibilityInput.getMemberId();
		// get the freeze type
		String freezeReason = checkFreezeEligibilityInput.getFreezeReason();
		logger.info("MemberId : " + memberId + " , FreezeReason : " + freezeReason);
		// get Current Contract
		if (currentContract != null) {
			logger.info(
					"Fetching previousFreezeduration for either a Regular/Medical/Pregnancy/External freeze in the contractualYear...");

			int previousFreezeduration = getPreviousFreezeDuration(memberId, detailsDVOList, currentContract,
					obligationDate, freezeReason, checkFreezeEligibilityInput.getStartDate(), false);
			logger.info("PreviousFreezeduration : {}", previousFreezeduration);

			if (previousFreezeduration > 0) {
				hasFreezed = true;
				return hasFreezed;
			}
		}
		return hasFreezed;
	}
	
	private boolean checkFreezeHistoryV2(List<MemberAgreementDetail> detailsDVOList,
			MemberAgreementDetail currentContract, CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput,
			Date obligationDate) throws Exception {
		boolean hasFreezed = false;
		logger.info("Inside FreezeEligibilityHelper.checkFreezeHistory method..");
		String memberId = checkFreezeEligibilityInput.getMosoId();
		// get the freeze type
		String freezeReason = checkFreezeEligibilityInput.getFreezeReason();
		logger.info("MemberId : " + memberId + " , FreezeReason : " + freezeReason);
		// get Current Contract
		if (currentContract != null) {
			logger.info(
					"Fetching previousFreezeduration for either a Regular/Medical/Pregnancy/External freeze in the contractualYear...");

			int previousFreezeduration = getPreviousFreezeDurationV2(memberId, detailsDVOList, currentContract,
					obligationDate, freezeReason, checkFreezeEligibilityInput.getStartDate(), false);
			logger.info("PreviousFreezeduration : {}", previousFreezeduration);

			if (previousFreezeduration > 0) {
				hasFreezed = true;
				return hasFreezed;
			}
		}
		return hasFreezed;
	}

    public CheckFreezeEligiblityOutput eligibilityRuleCheckV3(MemberAgreementDetail memberAgreementDetail, CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput, Boolean hasCOF, CorpAgreementDetail corpAgreementDetail, List<MemberAgreementDetail> memberAgreementDetailList, BigDecimal balance, String facilityId) {
        CheckFreezeEligiblityOutput output = new CheckFreezeEligiblityOutput();
        Map<String, String> urlVariables = new HashMap<>();
        urlVariables.put("categories", "membership,profile");
        Member member = redisApiService.getMemberV2SpecificCategories(checkFreezeEligibilityInput.getMosoId(), urlVariables);
        if (Objects.isNull(member)) {
            ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.name(), FreezeServiceExceptionEnum.MISSING_GLOBEY_MEMBERSHIP_PROFILE_DATA.getCode(),
                    ExceptionMessageEnum.ERROR.name());
            output.setMessages(new ExceptionMessage[]{exceptionMessage});
            return output;
        }
        if (Objects.nonNull(memberAgreementDetail.getEmailAddress())) {
            output.setEmail(memberAgreementDetail.getEmailAddress());
        } else {
            output.setEmail(member.getProfile().getEmail());
        }
        // Declaration and initialization
        if (checkFreezeEligibilityInput.getStartDate() == null) {
            Date date = new Date();
            checkFreezeEligibilityInput.setStartDate(date);
        }
        String contractStatus = memberAgreementDetail.getContractStatus();
        String membershipClass = memberAgreementDetail.getMembershipClass();
        output.setEligibleForFreeze(false);

        if (member.getMembership().getMemberSince() != null) {
            //LocalDate currentDate = LocalDate.now();
            LocalDate freezeStartDate = null;
            freezeStartDate = checkFreezeEligibilityInput.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            Calendar memberSince = member.getMembership().getMemberSince();
            LocalDate memberSinceDate = LocalDateTime.ofInstant(memberSince.toInstant(), memberSince.getTimeZone().toZoneId()).toLocalDate();
            output.setMemberSince(memberSinceDate.toString());
            long daysDiff = ChronoUnit.DAYS.between(memberSinceDate, freezeStartDate);
            LocalDate contractChangeDate = LocalDate.parse(CONTRACT_DATE);
            if (!StringUtils.isEmpty(checkFreezeEligibilityInput.getFreezeReason()) && checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(REASON_REGULAR)) {
                if (daysDiff < 365) {
                    if (memberSinceDate.isAfter(contractChangeDate) || memberSinceDate.isEqual(contractChangeDate)) {
                        output.setJoinedAfterContractChange(true);
                        output.setInObligation(true);
                    } else {
                        output.setJoinedAfterContractChange(false);
                        output.setInObligation(true);
                    }
                }
                if (daysDiff >= 365) {
                    if (memberSinceDate.isAfter(contractChangeDate) || memberSinceDate.isEqual(contractChangeDate))
                        output.setJoinedAfterContractChange(true);
                    else
                        output.setJoinedAfterContractChange(false);
                }
            }
        }

		logger.debug("Check if cancel freeze eligibility rules is enabled for the facility");
		String pilotCancelFreezeFaciltiyKeys =  tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_CANCEL_FREEZE_ELIGIBILITY_RULES);
		if (pilotCancelFreezeFaciltiyKeys != null) {
			List<String> pilotCancelFreezeFacilties = Arrays.asList(pilotCancelFreezeFaciltiyKeys.split(","));
			if (pilotCancelFreezeFacilties.contains(facilityId)) {
				logger.debug("Inside Eligibility Rule check");
				if (memberAgreementDetail.getMembershipClass().trim().equalsIgnoreCase(AMEX_DESTINATION_ACCESS)
						|| memberAgreementDetail.getMembershipClass().trim().equalsIgnoreCase(AMEX_CENTURIAN)) {
					output.setStatus(STATUS_ERROR);
					output.setEligibleForFreeze(false);
					ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.getCode(),
							ExceptionMessageEnum.VALIDATION.name());
					output.setMessages(new ExceptionMessage[]{exceptionMessage});
					return output;
				}

				logger.debug("Is a Hotel Guest");
				if (memberAgreementDetail.getMembershipClass().equalsIgnoreCase("Hotel Guest")) {
					output.setStatus(STATUS_ERROR);
					output.setEligibleForFreeze(false);
					if (StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
						ExceptionMessage exceptionMessage = setExceptionBlock("Hotel Guests cannot freeze.",
								"Hotel Guests cannot freeze.",
								FreezeServiceExceptionEnum.HOTEL_GUEST.getCode(), "VALIDATION");
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
						logger.debug("Member has ---> {}. So, Freeze is not allowed. ", memberAgreementDetail.getMembershipClass());
					} else {
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.getCode(),
								ExceptionMessageEnum.VALIDATION.name());
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					}
					return output;
				}

				logger.debug("Members with Pending Freeze should not be Eligible");
				if (memberAgreementDetail.getFreezeStatus() != null && memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("Pending Start")) {
					java.sql.Date freezeStartTime = memberAgreementDetail.getFreezeStartTime();
					LocalDate frzStartTime = java.sql.Date.valueOf(freezeStartTime.toString()).toLocalDate();
					Date freezeEndTime = memberAgreementDetail.getFreezeEndTime();
					LocalDate frzEndTime = java.sql.Date.valueOf(freezeEndTime.toString()).toLocalDate();
					DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
					if (StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
						output.setStatus(STATUS_ERROR);
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PENDING_FREEZE_NEW.name(), FreezeServiceExceptionEnum.PENDING_FREEZE_NEW.getCode(),
								ExceptionMessageEnum.VALIDATION.name(), formatter.format(frzStartTime), formatter.format(frzEndTime));
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					} else {
						output.setStatus(STATUS_WARNING);
						output.setEligibleForFreeze(true);
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PENDING_FREEZE_NEW.name(), FreezeServiceExceptionEnum.PENDING_FREEZE_NEW.getCode(),
								ExceptionMessageEnum.VALIDATION.name(), formatter.format(frzStartTime), formatter.format(frzEndTime));
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					}
					return output;
				}


				// memberStatusRule
				logger.debug("contractStatus {}", contractStatus);
				if (contractStatus.equalsIgnoreCase(CONTRACT_STATUS_PENDING) || contractStatus.equalsIgnoreCase(CONTRACT_STATUS_CANCELLED)
						|| contractStatus.equalsIgnoreCase(CONTRACT_STATUS_HOLD) || contractStatus.equalsIgnoreCase(CONTRACT_STATUS_EXPIRED)
						|| contractStatus.equalsIgnoreCase(CONTRACT_STATUS_NOT_FINALIZED) || contractStatus.equalsIgnoreCase(CONTRACT_STATUS_NON_FINALIZED)) {
					output.setStatus(STATUS_ERROR);
					if (StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
						ExceptionMessage exceptionMessage = setExceptionBlock(
								"Pending/Cancelled/Expired/Not Finalized Members are not eligible for freeze.",
								"Pending/Cancelled/Expired/Not Finalized Members are not eligible for freeze.",
								FreezeServiceExceptionEnum.MEMBER_STATUS_RULE.getCode(), "VALIDATION");
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					} else {
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.getCode(),
								ExceptionMessageEnum.VALIDATION.name());
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					}
					return output;
				}

				// membershipClassRule
				logger.debug("membershipClass {}", membershipClass);
				if (membershipClass.startsWith("Prospect") || membershipClass.startsWith("Short Term")
						|| membershipClass.startsWith("Comp")) {
					output.setStatus(STATUS_ERROR);
					if (StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
						ExceptionMessage exceptionMessage = setExceptionBlock(
								"Prospect, Short Term and Complementary member types are not allowed to freeze.",
								"Prospect, Short Term and Complementary member types are not allowed to freeze.",
								FreezeServiceExceptionEnum.MEMBER_CLASS_RULE.getCode(), "VALIDATION");
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					} else {
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.INELIGIBLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.INELIGIBLE_TO_FREEZE.getCode(),
								ExceptionMessageEnum.VALIDATION.name());
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					}
					return output;
				}

				//PIF Expiration Date Rule //AgreementTermId 1 for PIF and 2 For Bill Monthly
				if (memberAgreementDetail.getAgreementTermId() == 1) {
					//LocalDate freezeStartDate = null;
					if (StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
						output.setStatus(STATUS_ERROR);
						output.setEligibleForFreeze(false);
						output.setIsPIF(Boolean.TRUE);
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.getCode(),
								ExceptionMessageEnum.VALIDATION.name());
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
						return output;
					} else {
						output.setIsPIF(Boolean.TRUE);
						output.setStatus(STATUS_ERROR);
						output.setEligibleForFreeze(false);
						LocalDate freezeStartDate = checkFreezeEligibilityInput.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
						java.sql.Date endDate = memberAgreementDetail.getEndDate();
						LocalDate expirationDate = java.sql.Date.valueOf(endDate.toString()).toLocalDate();
						boolean isAfter = freezeStartDate.isAfter(expirationDate.minusDays(30));
						if (isAfter) {
							ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.getCode(),
									ExceptionMessageEnum.VALIDATION.name());
							output.setMessages(new ExceptionMessage[]{exceptionMessage});
							return output;
						}
					}
				}

				//Past Due Eligibility Rule
				if (!StringUtils.isEmpty(member.getMembership().getBalance())
						&& !member.getMembership().getBalance().equals("0")) {
					balance = new BigDecimal(member.getMembership().getBalance());
					if (balance.compareTo(new BigDecimal(0)) > 0) {
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PAST_DUE_BALANCE.name(), FreezeServiceExceptionEnum.PAST_DUE_BALANCE.getCode(),
								ExceptionMessageEnum.VALIDATION.name());
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
						return output;
					}
				}

				//Pending Cancellation Rule
				if (Objects.nonNull(memberAgreementDetail.getCancellationDate()) && !cancellationStateId.contains(memberAgreementDetail.getCancellationStateId())) {
					//LocalDate currentDate = LocalDate.now();
					LocalDate freezeStartDate = checkFreezeEligibilityInput.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate cancellationDate = memberAgreementDetail.getCancellationDate().toLocalDate();
					if (cancellationDate.isAfter(freezeStartDate)) {
						output.setStatus(STATUS_ERROR);
						output.setEligibleForFreeze(false);
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.UNABLE_TO_FREEZE.getCode(),
								ExceptionMessageEnum.VALIDATION.name());
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
						return output;
					}
				}

				logger.debug("Partial Subsidy is Ineligible");
				if (Objects.nonNull(corpAgreementDetail)) {
					if (corpAgreementDetail.getAgreementType() != null && corpAgreementDetail.getAgreementType().equalsIgnoreCase("Corporate Pay Amount") || corpAgreementDetail.getAgreementType() != null && corpAgreementDetail.getAgreementType().equalsIgnoreCase("Corporate Pay Percent")) {
						output.setStatus(STATUS_ERROR);
						output.setEligibleForFreeze(false);
						if (StringUtils.isEmpty(checkFreezeEligibilityInput.getSource()) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
							ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PARTIAL_SUBSIDY.name(), FreezeServiceExceptionEnum.PARTIAL_SUBSIDY.getCode(),
									ExceptionMessageEnum.VALIDATION.name());
							output.setMessages(new ExceptionMessage[]{exceptionMessage});
						} else {
							ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.INELIGIBLE_TO_FREEZE.name(), FreezeServiceExceptionEnum.INELIGIBLE_TO_FREEZE.getCode(),
									ExceptionMessageEnum.VALIDATION.name());
							output.setMessages(new ExceptionMessage[]{exceptionMessage});
						}
						return output;
					}
				}

				//     check if Freeze too early in their tenure for new members - only for regular flow and not for medical and pregnancy
				long daysBetween = -1;
				if (member.getMembership().getMemberSince() != null && !StringUtils.isEmpty(checkFreezeEligibilityInput.getFreezeReason())) {
					// Retrieve flags and required tenure from Redis
					Map<String, String> pilotKeys = tokenNoFreezeTemplate.getRedisHashOpsData("pilot.new.member.eligibility");
					String tenureFlag = null;
					if (pilotKeys != null && !pilotKeys.isEmpty()) {
						String homeFacilityId = memberAgreementDetail.getHomeFacilityId();

						for (Map.Entry<String, String> entry : pilotKeys.entrySet()) {
							String tenureKey = entry.getKey();
							String clubIds = entry.getValue();

							// Check if the homeFacilityId is contained in the list of club IDs for this tenure
							if (clubIds.contains(homeFacilityId)) {
								tenureFlag = tenureKey;
								break; // Stop once we find the matching tenure flag
							}
						}
					}

					// Parse required tenure from the flag if it exists
					if (tenureFlag == null) {
						Calendar memberSince = member.getMembership().getMemberSince();
						Calendar currentTime = Calendar.getInstance();
						currentTime.setTime(checkFreezeEligibilityInput.getStartDate());
						daysBetween = ChronoUnit.DAYS.between(memberSince.toInstant(), currentTime.toInstant());

						if (daysBetween >= 0 && daysBetween < MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE) {
							int daysDiff = (int) (MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE - daysBetween);
							LocalDate currentDate = LocalDate.now();
							currentDate = currentDate.plusDays(daysDiff);
							DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
							if (checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(REASON_REGULAR) && (checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE))) {
								output.setStatus(STATUS_ERROR);
								output.setEligibleForFreeze(false);
								ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.ACTIVE_FOR_PLUS_THIRTY_DAYS.name(), FreezeServiceExceptionEnum.ACTIVE_FOR_PLUS_THIRTY_DAYS.getCode(),
										ExceptionMessageEnum.VALIDATION.name(), formatter.format(currentDate));
								output.setMessages(new ExceptionMessage[]{exceptionMessage});
							} else {
								output.setStatus(STATUS_WARNING);
								output.setEligibleForFreeze(true);
								ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS.name(), EngageFreezeServiceExceptionEnum.MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS.getCode(),
										ExceptionMessageEnum.VALIDATION.name(), formatter.format(currentDate));
								output.setMessages(new ExceptionMessage[]{exceptionMessage});
							}
							return output;
						}
					} else if (checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(REASON_REGULAR)) {
						int requiredTenureDays = Integer.parseInt(tenureFlag.replace("_days", ""));

						// Convert start date and member since date to LocalDate
						LocalDate freezeStartDate =
								checkFreezeEligibilityInput.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
						Calendar memberSince = member.getMembership().getMemberSince();
						LocalDate memberSinceDate =
								LocalDateTime.ofInstant(memberSince.toInstant(), memberSince.getTimeZone().toZoneId())
										.toLocalDate();
						long daysDiff = ChronoUnit.DAYS.between(memberSinceDate, freezeStartDate);
						// Set member's "member since" date in output for reference
						output.setMemberSince(memberSinceDate.toString());
						DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
						
						LocalDate frozenDate = memberSinceDate.plusDays(90);
						// Compare tenure with required duration
						if (daysDiff <= requiredTenureDays) {
							// Set the warning message if tenure is less than required duration
							output.setStatus(STATUS_WARNING);
							ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.PENDING_AGREEMENT.name(), FreezeServiceExceptionEnum.PENDING_AGREEMENT.getCode(),
									ExceptionMessageEnum.VALIDATION.name(), formatter.format(frozenDate));
							output.setMessages(new ExceptionMessage[]{exceptionMessage});
							output.setEligibleForFreeze(false);
							return output;
						}
					}
				} else {
					if (contractStatus.equalsIgnoreCase("Active") && memberAgreementDetail.getStartDate() != null) {
						java.sql.Date startDate = memberAgreementDetail.getStartDate();
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(startDate);
						Calendar currentDate = Calendar.getInstance();
						daysBetween = ChronoUnit.DAYS.between(calendar.toInstant(), currentDate.toInstant());
					}

					if (daysBetween >= 0 && daysBetween < MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE) {
						int daysDiff = (int) (MEMBER_ACTIVE_PLUS_THIRTY_DAYS_FOR_FREEZE - daysBetween);
						LocalDate currentDate = LocalDate.now();
						currentDate = currentDate.plusDays(daysDiff);
						DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
						if (checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(REASON_REGULAR) && (checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE))) {
							output.setStatus(STATUS_ERROR);
							output.setEligibleForFreeze(false);
							ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.ACTIVE_FOR_PLUS_THIRTY_DAYS.name(), FreezeServiceExceptionEnum.ACTIVE_FOR_PLUS_THIRTY_DAYS.getCode(),
									ExceptionMessageEnum.VALIDATION.name(), formatter.format(currentDate));
							output.setMessages(new ExceptionMessage[]{exceptionMessage});
						} else {
							output.setStatus(STATUS_WARNING);
							output.setEligibleForFreeze(true);
							ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(EngageFreezeServiceExceptionEnum.MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS.name(), EngageFreezeServiceExceptionEnum.MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS.getCode(),
									ExceptionMessageEnum.VALIDATION.name(), formatter.format(currentDate));
							output.setMessages(new ExceptionMessage[]{exceptionMessage});
						}
						return output;
					}
				}

				//Duplicate Existing Freeze
				if (checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_ENGAGE) && memberAgreementDetail.getFreezeStatus() != null && memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("active")) {
					LocalDate requestedFreezeStartDate = checkFreezeEligibilityInput.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate freezeStartTime = memberAgreementDetail.getFreezeStartTime().toLocalDate();
					LocalDate freezeEndTime = memberAgreementDetail.getFreezeEndTime().toLocalDate();
					DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
					if (requestedFreezeStartDate.isAfter(freezeEndTime) || requestedFreezeStartDate.isEqual(freezeEndTime)) {
						LocalDate frzStartDate = java.sql.Date.valueOf(freezeStartTime.toString()).toLocalDate();
						LocalDate frzEndDate = java.sql.Date.valueOf(freezeEndTime.toString()).toLocalDate();
						String freezeReason = getFreezeReason(memberAgreementDetail.getFreezeReason());
						output.setStatus(STATUS_WARNING);
						output.setEligibleForFreeze(true);
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.ACTIVE_FREEZE.name(), FreezeServiceExceptionEnum.ACTIVE_FREEZE.getCode(),
								ExceptionMessageEnum.VALIDATION.name(), formatter.format(frzStartDate), formatter.format(frzEndDate));
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					} else {
						LocalDate frzStartDate = java.sql.Date.valueOf(freezeStartTime.toString()).toLocalDate();
						LocalDate frzEndDate = java.sql.Date.valueOf(freezeEndTime.toString()).toLocalDate();
						output.setStatus(STATUS_ERROR);
						output.setEligibleForFreeze(false);
						ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.ACTIVE_FREEZE.name(), FreezeServiceExceptionEnum.ACTIVE_FREEZE.getCode(),
								ExceptionMessageEnum.VALIDATION.name(), formatter.format(frzStartDate), formatter.format(frzEndDate));
						output.setMessages(new ExceptionMessage[]{exceptionMessage});
					}
					return output;

				}

				// checkAlreadyInFreeze
				logger.debug("Checking Member is already have active/Future Freeze?");
				if (memberAgreementDetail.getFreezeEndTime() != null && memberAgreementDetail.getFreezeStatus() != null) {
					//Date currnetDate = Calendar.getInstance().getTime();
					LocalDate requestedFreezeStartDate = checkFreezeEligibilityInput.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate freezeEndTime = memberAgreementDetail.getFreezeEndTime().toLocalDate();
					if (freezeEndTime.isAfter(requestedFreezeStartDate) && memberAgreementDetail.getFreezeStatus().equalsIgnoreCase("active")) {
						Date freezeStartTime = memberAgreementDetail.getFreezeStartTime();
						LocalDate frzStartDate = java.sql.Date.valueOf(freezeStartTime.toString()).toLocalDate();
						LocalDate frzEndDate = java.sql.Date.valueOf(freezeEndTime.toString()).toLocalDate();
						DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
						output.setStatus(STATUS_ERROR);
						if (checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
							ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.ACTIVE_FREEZE.name(), FreezeServiceExceptionEnum.ACTIVE_FREEZE.getCode(),
									ExceptionMessageEnum.VALIDATION.name(), formatter.format(frzStartDate), formatter.format(frzEndDate));
							output.setMessages(new ExceptionMessage[]{exceptionMessage});
						} else {
							output.setStatus(STATUS_WARNING);
							output.setEligibleForFreeze(true);
							ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.ACTIVE_FREEZE.name(), FreezeServiceExceptionEnum.ACTIVE_FREEZE.getCode(),
									ExceptionMessageEnum.VALIDATION.name(), formatter.format(frzStartDate), formatter.format(frzEndDate));
							output.setMessages(new ExceptionMessage[]{exceptionMessage});
						}
						logger.debug("Member current Freeze Status: {} ", memberAgreementDetail.getFreezeStatus());
						return output;
					}
				}

				// check for regular freeze per contractual year in obj and out obj
				// please check ig university flag is on or not
				if (isPilotUniversityFreezeEnabled(facilityId) && freezeServiceHelper.isUniversityMember(checkFreezeEligibilityInput.getMosoId())
					&& checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(REASON_REGULAR)) {
					logger.debug("Inside Pilot University Freeze Eligibility check");
				} else if (memberAgreementDetail.getFreezeStatus() != null && (memberAgreementDetail.getFreezeReason().contains(REASON_REG) || memberAgreementDetail.getFreezeReason().equalsIgnoreCase("15 Days In Ob Exception")
						|| memberAgreementDetail.getFreezeReason().equalsIgnoreCase("45 Days In Ob Exception")) && checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(REASON_REGULAR)) {
					// check redis in obligation flag and out of obligation flag
					String inObj = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.CANCEL_FREEZE_CONTRACT_YEAR_IN_OBJ);
					String outObj = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.CANCEL_FREEZE_CONTRACT_YEAR_OUT_OBJ);

					List<String> inObligationList = inObj != null ? Arrays.asList(inObj.split(",")) : Collections.emptyList();
					List<String> outObligationList = outObj != null ? Arrays.asList(outObj.split(",")) : Collections.emptyList();

					boolean isObjFlag = !CollectionUtils.isEmpty(inObligationList) && inObligationList.contains(facilityId);
					boolean isOutObjFlag = !CollectionUtils.isEmpty(outObligationList) && outObligationList.contains(facilityId);

					if (isOutObjFlag && isObjFlag) {
						logger.debug("Facility is in both in obligation and out of obligation list check if either obligation or out of obligation");
						checkForOneFreezePerContractualYear(memberAgreementDetail, checkFreezeEligibilityInput, memberAgreementDetailList, member, output);
					} else if (isOutObjFlag && !isObjFlag) {
						logger.debug("Facility is in out of obligation list, check for out of obligation only and skipping the check for in obligation");
						if (!output.isInObligation()) {
							checkForOneFreezePerContractualYear(memberAgreementDetail, checkFreezeEligibilityInput, memberAgreementDetailList, member, output);
						}
					} else if (isObjFlag && !isOutObjFlag) {
						logger.debug("Facility is in in obligation list, check for in obligation only and skipping the check for out of obligation");
						if (output.isInObligation()) {
							checkForOneFreezePerContractualYear(memberAgreementDetail, checkFreezeEligibilityInput, memberAgreementDetailList, member, output);
						}
					}

					if (output.getStatus() != null && (output.getStatus().equalsIgnoreCase(STATUS_ERROR) || output.getStatus().equalsIgnoreCase(STATUS_WARNING))) {
						return output;
					}
				}


							/*boolean isfreezedBefore = false;
							if (!isStartDateOneDayAfterPreviousEndDateV2(checkFreezeEligibilityInput, memberAgreementDetail)) {
								logger.info("Checking One Freeze Per Contractual Year..."); //
								// Check one freeze per contractual year //
								try {
									isfreezedBefore = checkOneFreezePerContractualYearV2(memberAgreementDetailList, memberAgreementDetail,
											checkFreezeEligibilityInput, obligationDate);
								} catch (Exception e) {
									ExceptionMessage exceptionMessage = setExceptionBlock(
											FreezeServiceExceptionEnum.CONTRACTUAL_RULE.name(),
											//e.getMessage(),
											"Failed to check rule for one freeze per Contractual year",
											FreezeServiceExceptionEnum.CONTRACTUAL_RULE.getCode(), "ERROR");
									output.setMessages(new ExceptionMessage[]{exceptionMessage});
									return output;
								}
								if (isfreezedBefore) {
									Calendar memberSince = member.getMembership().getMemberSince();
									LocalDate memberSinceDate = LocalDateTime.ofInstant(memberSince.toInstant(), memberSince.getTimeZone().toZoneId())
													.toLocalDate();
									int value = memberSinceDate.getMonth().getValue();
									boolean isTwoDigit = Integer.toString(Math.abs(value)).trim().length() == 2;
									String month = null;
									if(!isTwoDigit) {
										DecimalFormat df = new DecimalFormat("00");
										month = df.format(value);
									} else {
										month=String.valueOf(value);
									}

									LocalDate freezeStartDate = null;
									if(Objects.isNull(checkFreezeEligibilityInput.getStartDate()))
										freezeStartDate = LocalDate.now();
									else
										freezeStartDate = checkFreezeEligibilityInput.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
									String yearWithMembersinceMonth = freezeStartDate.getYear()+"-"+month+"-"+"01";
									LocalDate constrcutedDate = LocalDate.parse(yearWithMembersinceMonth);
									LocalDate startDate = null;
									LocalDate endDate = null;
									if(constrcutedDate.isBefore(freezeStartDate) || constrcutedDate.isEqual(freezeStartDate)) {
										startDate = constrcutedDate;
										endDate = constrcutedDate.plusYears(1);
									} else { //date.isAfter(currentDate)
										startDate = constrcutedDate.minusYears(1);
										endDate = constrcutedDate;
									}

									DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");

									output.setStatus(STATUS_ERROR);
									output.setEligibleForFreeze(false);
									ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.REGULAR_FREEZE_PER_CONTRACTUAL_YEAR.name(), FreezeServiceExceptionEnum.REGULAR_FREEZE_PER_CONTRACTUAL_YEAR.getCode(),
											ExceptionMessageEnum.VALIDATION.name(), formatter.format(memberAgreementDetail.getFreezeStartTime().toLocalDate()), formatter.format(endDate));
									output.setMessages(new ExceptionMessage[]{exceptionMessage});
									return output;
								}
							}
						}*/
			}
		}

        logger.debug("Yes");
        output.setEligibleForFreeze(true);
        output.setStatus(STATUS_ELIGIBLE);
        return output;
    }

	private boolean isPilotUniversityFreezeEnabled(String homeFacilityId) {
		String pilotUniversityFreezeFaciltiyKeys = tokenNoFreezeTemplate.getRedisValueOpsData(FreezeMembershipConstants.PILOT_UNIVERSITY_FREEZE_FEE);
		if (pilotUniversityFreezeFaciltiyKeys != null) {
			List<String> pilotUniversityFreezeFacilties = Arrays.asList(pilotUniversityFreezeFaciltiyKeys.split(","));
            return pilotUniversityFreezeFacilties.contains(homeFacilityId);
		}
		return false;
	}

	private CheckFreezeEligiblityOutput checkForOneFreezePerContractualYear(MemberAgreementDetail memberAgreementDetail, CheckFreezeEligibilityInputV2 checkFreezeEligibilityInput, List<MemberAgreementDetail> memberAgreementDetailList, Member member, CheckFreezeEligiblityOutput output) {
		memberAgreementDetailList.add(memberAgreementDetail);

		logger.debug("No active/Future Freeze on account");
		Date obligationDate = memberAgreementDetail.getObligationDate();
		ArrayList<MemberAgreementDetail> applicableContracts = null;

		if (obligationDate != null) {
			Calendar obligationDateCalendar = Calendar.getInstance();
			obligationDateCalendar.setTime(obligationDate);
			Calendar considerationDateCalendar = Calendar.getInstance();
			considerationDateCalendar.setTime(checkFreezeEligibilityInput.getStartDate());
			Calendar contractualYearStartDateCal = Calendar.getInstance();
			contractualYearStartDateCal.setTime(obligationDate);
			if (considerationDateCalendar.getTimeInMillis() <= obligationDateCalendar.getTimeInMillis()) {
				logger.debug("ConsiderationDate is same as or before the obligationDate...");
				// subtract 1 year from the obligation date
				logger.debug(
						"Subtracting 1 year from the contractualYearStartDate since considerationDate is lesser than obligationDate...");
				contractualYearStartDateCal.add(Calendar.YEAR, -1);
			} else {
				logger.debug("ConsiderationDate is beyond the obligationDate...");
				while (contractualYearStartDateCal.get(Calendar.YEAR) < considerationDateCalendar
						.get(Calendar.YEAR)) {
					contractualYearStartDateCal.add(Calendar.YEAR, 1);
				}
				// subtract 1 year from the obligation date
				// Added 5/14/09 - SD
				if (contractualYearStartDateCal.getTimeInMillis() > Calendar.getInstance().getTimeInMillis())
					contractualYearStartDateCal.add(Calendar.YEAR, -1);
			}

			logger.debug("Fetching a list of contract's for a year...");
			logger.debug("Contractual year start date :{} ", contractualYearStartDateCal.getTime());
			// get a list of one year contract
			try {
				applicableContracts = getOneYearContractHistoryV2(checkFreezeEligibilityInput.getMosoId(), memberAgreementDetailList, obligationDate);
			} catch (Exception e) {
				logger.error("Error while getting the contractual year history : {}", e.getMessage());

			}
			
			
			
			//Checking member allowed to freeze again
			if (!memberAgreementDetailList.isEmpty()) {
				for (MemberAgreementDetail memberAgreementDetails : memberAgreementDetailList) {
					if (!StringUtils.isEmpty(memberAgreementDetails.getFreezeStatus())) {
						if ((!StringUtils.isEmpty(checkFreezeEligibilityInput.getFreezeReason())) && memberAgreementDetails.getFreezeStatus().equalsIgnoreCase(CONTRACT_STATUS_EXPIRED)) {
							if ((memberAgreementDetails.getFreezeEndTime() != null && member.getMembership().getMemberSince() != null) && !memberAgreementDetails.getContractStatus().equalsIgnoreCase(CONTRACT_STATUS_PENDING) || !memberAgreementDetails.getContractStatus().equalsIgnoreCase(CONTRACT_STATUS_CANCELLED)
									|| !memberAgreementDetails.getContractStatus().equalsIgnoreCase(CONTRACT_STATUS_HOLD) || !memberAgreementDetails.getContractStatus().equalsIgnoreCase(CONTRACT_STATUS_EXPIRED)) {
								Calendar memberSince = member.getMembership().getMemberSince();
								LocalDate memberSinceDate = LocalDateTime.ofInstant(memberSince.toInstant(), memberSince.getTimeZone().toZoneId()).toLocalDate();
								LocalDate previousFreezeEndTime = memberAgreementDetails.getFreezeEndTime().toLocalDate();
								LocalDate previousFreezeStartTime = memberAgreementDetails.getFreezeStartTime().toLocalDate();

								LocalDate freezeStartDate = null;
								if (Objects.isNull(checkFreezeEligibilityInput.getStartDate()))
									freezeStartDate = LocalDate.now();
								else
									freezeStartDate = checkFreezeEligibilityInput.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

								int value = memberSinceDate.getMonth().getValue();
								boolean isTwoDigit = Integer.toString(Math.abs(value)).trim().length() == 2;
								String month = null;
								if (!isTwoDigit) {
									DecimalFormat df = new DecimalFormat("00");
									month = df.format(value);
								} else {
									month = String.valueOf(value);
								}	
								
								if (applicableContracts != null && !applicableContracts.isEmpty()) {
									// get the recent contract from the list
									MemberAgreementDetail recentContract = applicableContracts.get(0);
									LocalDate latestFreezeStartDate = recentContract.getFreezeStartTime().toLocalDate();
								

									String yearWithMembersinceMonth = contractualYearStartDateCal.get(Calendar.YEAR) + "-" + month + "-" + "01";
									LocalDate constrcutedDate = LocalDate.parse(yearWithMembersinceMonth);
									LocalDate endDate = constrcutedDate.plusYears(1).minusDays(1);
									LocalDate newStartDate = endDate.plusDays(1);
	
									if (latestFreezeStartDate.isAfter(constrcutedDate) && latestFreezeStartDate.isBefore(endDate) && !freezeStartDate.isAfter(endDate)) {
										DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/YYYY");
										if ((memberAgreementDetails.getFreezeReason().contains(REASON_REG) || memberAgreementDetail.getFreezeReason().equalsIgnoreCase("15 Days In Ob Exception") ||
												memberAgreementDetail.getFreezeReason().equalsIgnoreCase("45 Days In Ob Exception")) && checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(REASON_REGULAR) && (checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CLUBAPP) || checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_CONCIERGE))) {
											output.setStatus(STATUS_ERROR);
											output.setEligibleForFreeze(false);
											ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.MEMBER_ALLOWED_TO_FREEZE_AGAIN.name(), FreezeServiceExceptionEnum.MEMBER_ALLOWED_TO_FREEZE_AGAIN.getCode(),
													ExceptionMessageEnum.VALIDATION.name(), formatter.format(newStartDate));
											output.setMessages(new ExceptionMessage[]{exceptionMessage});
											return output;
										} else if ((memberAgreementDetails.getFreezeReason().contains(REASON_REG) || memberAgreementDetail.getFreezeReason().equalsIgnoreCase("15 Days In Ob Exception") ||
												memberAgreementDetail.getFreezeReason().equalsIgnoreCase("45 Days In Ob Exception")) && checkFreezeEligibilityInput.getFreezeReason().equalsIgnoreCase(REASON_REGULAR) && checkFreezeEligibilityInput.getSource().equalsIgnoreCase(SOURCE_ENGAGE)) {
											output.setStatus(STATUS_WARNING);
											output.setEligibleForFreeze(true);
											ExceptionMessage exceptionMessage = errorMessageHandler.createExceptionMessage(FreezeServiceExceptionEnum.REGULAR_FREEZE_PER_CONTRACTUAL_YEAR.name(), FreezeServiceExceptionEnum.REGULAR_FREEZE_PER_CONTRACTUAL_YEAR.getCode(),
												ExceptionMessageEnum.VALIDATION.name(), formatter.format(latestFreezeStartDate), formatter.format(newStartDate));
											output.setMessages(new ExceptionMessage[]{exceptionMessage});
											return output;
										}
									}

								}
							}
						}
					}
				}
			}
			
		}
		return output;
	}

}
