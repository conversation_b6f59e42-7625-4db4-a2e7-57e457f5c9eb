package com.equinoxfitness.freezeservice.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.BindException;
import java.text.SimpleDateFormat;
import java.util.Objects;
import java.util.function.Supplier;

import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.equinoxfitness.freezeservice.contract.Club__r;
import com.equinoxfitness.freezeservice.contract.EngagePostResponse;
import com.equinoxfitness.freezeservice.contract.Feedback;
import com.equinoxfitness.freezeservice.contract.FeedbackContact;
import com.equinoxfitness.freezeservice.contract.MemberFreezeCase;
import com.equinoxfitness.salesforce.contract.Wrapper;
import com.equinoxfitness.salesforce.service.impl.SalesforceApiNonWebServiceImpl;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Value;


@Component
public class SalesforceUtil {

	private static final String SOURCE_CONCIERGE = "concierge";

	private static final String WEB_ = "web_";

	public static final String SOURCE_CLUBAPP = "clubapp";

	@Autowired
	SalesforceApiNonWebServiceImpl salesforceApiNonWebServiceImpl;

	@Value("${freeze.medical.record.type.id}")
	private String freezeMedicalRecordTypeId;

	@Value("${api.cast.iron.user.id}")
	private String apiCastIronUserId;


	private static final Logger logger = LoggerFactory.getLogger(SalesforceUtil.class);

	public String createMemberFreezeCase(MemberFreezeCase memberFreezeCase) {
		logger.info("createMemberFreezeCase calling::{}",memberFreezeCase);
		Feedback feedbackSfRequest = this.buildFeedbackSFRequest(memberFreezeCase).get();
		logger.info("Freeze case request sending to Engage: {}", new Gson().toJson(feedbackSfRequest));
		
		Wrapper<EngagePostResponse> response = salesforceApiNonWebServiceImpl.post("Case", feedbackSfRequest,
				EngagePostResponse.class);
		return !StringUtils.isEmpty(response.getMessage()) ? null : response.getData().getId();
	}

	private Supplier<Feedback> buildFeedbackSFRequest(MemberFreezeCase memberFreezeCase) {
		return () -> {
			Feedback feedback = new Feedback();
			feedback.setDescription(memberFreezeCase.getDescription());
			feedback.setDoNotContact(false);
			Club__r clubId = new Club__r();
			clubId.setFacilityId(memberFreezeCase.getClubId());
			feedback.setClubC(clubId);
			FeedbackContact contact = new FeedbackContact();
			contact.setMosoId(memberFreezeCase.getMosoId());
			feedback.setContactC(contact);
			feedback.setFeedbackSource(memberFreezeCase.getSource());
			if(memberFreezeCase.getSource().equalsIgnoreCase(SOURCE_CONCIERGE)) {
				feedback.setOrigin("Equinox.com");
			} else if(memberFreezeCase.getSource().equalsIgnoreCase(WEB_)) {
				feedback.setOrigin(FreezeMembershipConstants.ACCOUNT_EQUINOX);
			} else if (memberFreezeCase.getSource().equalsIgnoreCase(SOURCE_CLUBAPP)) {
				feedback.setOrigin("Club (Front Desk)");
			} else {
				feedback.setOrigin(memberFreezeCase.getSource());
			}
			feedback.setFeedbackType("Suggestion");
			feedback.setSubject("Membership Freeze");
			feedback.setCategory("Membership");
			feedback.setFollowUpRequired(true);
			feedback.setFreezeReason(memberFreezeCase.getFreezeReason());
			feedback.setStatus("Action");
			
			if(!StringUtils.isEmpty(memberFreezeCase.getSource()) && FreezeMembershipConstants.SOURCE_WEBSITE.equalsIgnoreCase(memberFreezeCase.getSource())) {
				feedback.setRecordTypeId(FreezeMembershipConstants.FREEZE_EXCEPTION_RECORD_TYPE_ID);
				feedback.setFreezeStartDate(LocalDate.now().toString());
			}
				
			if(Objects.nonNull(memberFreezeCase.getDurationMonths()) && memberFreezeCase.getDurationMonths() > 0)
				feedback.setFreezeDurationMonths(memberFreezeCase.getDurationMonths());
			
			if(Objects.nonNull(memberFreezeCase.getDurationDays()) && memberFreezeCase.getDurationDays() > 0)
				feedback.setFreezeDurationDays(memberFreezeCase.getDurationDays());
			
			if(Objects.nonNull(memberFreezeCase.getFreezeFees()))
				feedback.setFreezeFees(memberFreezeCase.getFreezeFees().toString());
			
			if(Objects.nonNull(memberFreezeCase.getFreezeFeeWaived()))
				feedback.setFreezeFeeWaived(memberFreezeCase.getFreezeFeeWaived());
			
			if(!StringUtils.isEmpty(memberFreezeCase.getSource()) && FreezeMembershipConstants.SOURCE_CLUBAPP.equalsIgnoreCase(memberFreezeCase.getSource())) {
				feedback.setRecordTypeId(FreezeMembershipConstants.FREEZE_EXCEPTION_RECORD_TYPE_ID);
			}

			if (memberFreezeCase.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_MEDICAL)
					|| memberFreezeCase.getFreezeReason().equalsIgnoreCase(FreezeMembershipConstants.REASON_PREGNANCY)) {
				feedback.setRecordTypeId(freezeMedicalRecordTypeId);
				feedback.setOwnerId(apiCastIronUserId);
				feedback.setFreezeRequestedDate(LocalDate.now().toString());
				feedback.setFreezeStartDate(memberFreezeCase.getStartDate());
				if (Objects.isNull(memberFreezeCase.getFreezeFees())) {
					if (memberFreezeCase.isPilotFreezeMedFee()) {
						BigDecimal freezeFees = BigDecimal.valueOf(FreezeMembershipConstants.MED_PREGNANCY_FEE);
						if (memberFreezeCase.getCountryCode().equalsIgnoreCase(FreezeMembershipConstants.COUNTRY_UK)) {
							freezeFees = freezeFees.multiply(BigDecimal.valueOf(0.8)).multiply(BigDecimal.valueOf(memberFreezeCase.getDurationMonths()));
							// do not add any decimal
							freezeFees =  freezeFees.setScale(0, RoundingMode.DOWN);

						} else {
							freezeFees = freezeFees.multiply(BigDecimal.valueOf(memberFreezeCase.getDurationMonths()));
						}
						feedback.setFreezeFees(freezeFees.toString());
					} else {
						feedback.setFreezeFees(String.valueOf(0));
					}
				}
				feedback.setDescription(FreezeMembershipConstants.MEDICAL_PREGNANCY_CASE_DESCRIPTION);
			}
			
			return feedback;
		};

	}



}
