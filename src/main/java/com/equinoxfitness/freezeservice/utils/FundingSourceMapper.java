package com.equinoxfitness.freezeservice.utils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Calendar;

import org.springframework.jdbc.core.RowMapper;

import com.equinoxfitness.freezeservice.contract.FundingSource;

public class FundingSourceMapper implements RowMapper<FundingSource> {

	@Override
	public FundingSource mapRow(ResultSet rs, int rowNum) throws SQLException {
		FundingSource fundingSource = new FundingSource();
		fundingSource.setMemberId(rs.getString("memberId"));
		fundingSource.setCardHolderName(rs.getString("CardHolderName"));
		fundingSource.setCreditType(rs.getString("CreditCardType"));
		fundingSource.setDisplayMask(rs.getString("DisplayMask"));

		if (rs.getDate("expirationDate") != null) {
			Calendar expDate = Calendar.getInstance();
			expDate.setTime(rs.getDate("expirationDate"));
			fundingSource.setExpiryDate(expDate);
		}
		
		fundingSource.setPaySourceId(rs.getInt("PaySourceId"));
		fundingSource.setCreditCardToken(rs.getString("token"));
		fundingSource.setAccountId(rs.getString("AccountId"));
		fundingSource.setIsForRecurringCharge(rs.getBoolean("UseForRecurring"));
		fundingSource.setIsForNonRecurringCharge(rs.getBoolean("UseForNonRecurring"));
		fundingSource.setBillingCountry(rs.getString("country"));
		fundingSource.setBillingAddress1(rs.getString("BillingStreet"));
		fundingSource.setBillingAddress2(rs.getString("BillingStreet2"));
		fundingSource.setBillingCity(rs.getString("city"));
		fundingSource.setBillingState(rs.getString("BillingState"));
		fundingSource.setCreditLimit(rs.getBigDecimal("creditLimit"));
		fundingSource.setMaxTransactionAmount(rs.getBigDecimal("MaxTransactionAmount"));
		
		return fundingSource;
	}
}
