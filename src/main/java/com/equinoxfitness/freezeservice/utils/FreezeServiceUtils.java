package com.equinoxfitness.freezeservice.utils;

import java.math.BigDecimal;

import com.equinoxfitness.freezeservice.contract.CaptureRequest;
import com.equinoxfitness.freezeservice.contract.CreditCard;
import com.equinoxfitness.freezeservice.contract.EncryptInput;
import com.equinoxfitness.freezeservice.contract.FundingSource;
import com.equinoxfitness.freezeservice.contract.PreAuthorizeRequest;
import com.equinoxfitness.freezeservice.contract.VoidRequest;

public interface FreezeServiceUtils {

	public FundingSource toFundingSource(String memberId);

	public EncryptInput toEncryptCC(CreditCard creditCard, String tokenFormat, int countryCode, String facilityAccountingCode,
			String requestedBy, String threadId);

	public PreAuthorizeRequest toPreAuthorizeRequest(FundingSource fundingSource, BigDecimal freezeFees,
			int countryCode, String mosoId, String tokenFormat, String facilityAccountingCode, String correlationId);

	public CaptureRequest toCaptureRequest(BigDecimal freezeFees, int countryCode, String mosoId,
			String transactionTag, String correlationId);

	public VoidRequest toVoidRequest(BigDecimal freezeFees, int countryCode, String referenceNumber, String transactionTag);

}
