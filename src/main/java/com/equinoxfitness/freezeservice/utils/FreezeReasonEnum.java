/**
 * 
 */
package com.equinoxfitness.freezeservice.utils;

/**
 * <AUTHOR>
 *
 */
public enum FreezeReasonEnum {

	REGULAR("Regular"),
	PREGNANCY("Pregnancy"),
	MEDICAL("Medical"),
	EXTERNAL("External"),
	OTHER("");
	
	private String freezeReason;
	FreezeReasonEnum(String freezeReason) {
		this.freezeReason=freezeReason;
	}

	public String getReason() {
		return this.freezeReason;
	}
	
	public static FreezeReasonEnum getFreezeReasonFromValue(String value){
		for (FreezeReasonEnum freezeReason : values()){
			if(freezeReason.getReason().equalsIgnoreCase(value)){
				return freezeReason;
			}
		}
		return OTHER;
	}
}
