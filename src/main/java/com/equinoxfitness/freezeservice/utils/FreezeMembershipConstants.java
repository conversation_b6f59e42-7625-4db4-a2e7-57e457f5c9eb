package com.equinoxfitness.freezeservice.utils;

public class FreezeMembershipConstants {

	private FreezeMembershipConstants() {
		
	}
	
	public static final String FREEZE_ACTIVE_STATUS = "Active";
	public static final String FREEZE_PENDING_START = "Pending Start";
	public static final String FREEZE_REASON_FINANCIAL_FREEZE = "Finance Freeze";
	public static final String FREEZE_REASON_CORP_SPONSOR_FREEZE = "Corp Sponsor Freeze";
	public static final String COUNTRY_US = "US";
	public static final String COUNTRY_UK = "UK";
	public static final String COUNTRY_CA = "CA";

	public static final String ACTIVE_PENDING_FREEZE_FRIENDLY_MESSAGE = "There is already an Active/Pending Freeze. <NAME_EMAIL> if you want to cancel an existing Freeze.";
	
	public static final String UNFREEZE_RECORD_TYPE_ID = "012120000019jsWAAQ";
	public static final String UNFREEZE_CASE_STATUS = "No Action";
	public static final String UNFREEZE_CANCELLATION_TYPE = "Removing someone from Freeze early or cancel a pending request";
	public static final String UNFREEZE_CASE_ORIGIN = "Equinox.com";
	public static final String UNFREEZE_CASE_SUBJECT = "Unfreeze";
	public static final String UNFREEZE_CASE_DESCRIPTION = "Unfreeze membership case creation";
	public static final String UNFREEZE_CASE_CATEGORIES = "Membership";
	public static final String UNFREEZE_CASE_SUB_CATEGORIES = "Freeze";
	
	public static final Integer UNFREEZE_NOTE_TYPE_ID = 37;
	public static final Integer FREEZE_EMAIL_LINK_EXPIRATION_DAYS=7;
	public static final String DEFAULT_COUNTRY_US="US";
	
	public static final String PAYEEZY = "Payeezy";
	public static final String MOSOPAY = "MoSoPay";
	public static final String APPROVED = "approved";
	public static final String REGULAR_FEE_WAIVED_OB_EXCEPTION = "Regular (Fee Waived In Ob Exception)";
	public static final String REGULAR_FEE_WAIVED = "Regular (Fee Waived)";
	public static final String REGULAR_FEE_WAIVE = "Regular (Fee Waive)";
	public static final String REGULAR_FEE_WAIVED_EXCEPTION = "Regular (Fee Waived Exception)";
	public static final String REGULAR_OB_EXCEPTION = "Regular (In Ob Exception)";
	public static final String EXCEPTION = " (Exception)";
	public static final String EXCEPTION_NEW = " (Exception) - New";
	public static final String FREEZE_FEE_EXCEPTION = "Exception";
	public static final String SOURCE_ENGAGE = "Engage";
	public static final String SOURCE_CLUBAPP = "clubapp";
	public static final String SOURCE_CLUB_APP = "Club App";
	public static final String STATUS_ERROR = "error";
	public static final String VALIDATION = "VALIDATION";
	public static final String REASON_REGULAR = "Regular";
	public static final String REASON_MEDICAL = "Medical";
	public static final String REASON_PREGNANCY = "Pregnancy";
	public static final String SOURCE_WEBSITE = "Website";
	public static final String SOURCE_WEB = "web_";
	public static final String SOURCE_CLUB_FRONT_DESK = "Club (Front Desk)";
	public static final String SEND_FREEZE_LINK_FROM_ENGAGE = "concierge";
	public static final String SEND_CANCEL_LINK_FROM_ENGAGE = "Website";
	public static final String SEND_CANCEL_LINK_FROM_CLUB_APP = "Website";

	public static final Integer TENDER_TYPE_ID_PAYEEZY = 913;
	public static final Integer TENDER_TYPE_ID_CREDIT_CARD = 103;
	public static final String TAX_CODES = "TaxCodes";
	public static final String FREEZE_RECORD_TYPE_ID = "012120000012dElAAI";
	public static final String FREEZE_EXCEPTION_RECORD_TYPE_ID = "012120000012dEkAAI";
	public static final String DAYS = "Days";
	public static final String MONTHS = "Months";
	public static final String REGULAR_B = "Reg B";
	public static final String REGULAR_C = "Reg C";
	public static final String IN_OB_EXCEPTION = "In Ob Exception";

	// freeze redis flags constants
	public static final String CANCEL_FREEZE_CONTRACT_YEAR_IN_OBJ = "cancel.freeze.one.per.contractual.year.in.ob";
	public static final String CANCEL_FREEZE_CONTRACT_YEAR_OUT_OBJ = "cancel.freeze.one.per.contractual.year.out.of.ob";
	public static final String PILOT_MONTHLY_FREEZE_FEE = "pilot.monthly.freeze.fee";
	public static final String PILOT_FREEZE_DURATION_DAYS = "pilot.freeze.duration.days";
	public static final String PILOT_FREEZE_30_DAYS_PER_YEAR = "pilot.freeze.30.days.per.year";
	public static final String PILOT_FREEZE_45_DAYS_PER_YEAR = "pilot.freeze.45.days.per.year";
	public static final String CLUBAPP_PILOT_FREEZE_30_DAYS_PER_YEAR = "freeze30DaysPerYear";
	public static final String CLUBAPP_PILOT_FREEZE_45_DAYS_PER_YEAR = "freeze45DaysPerYear";
	public static final String PILOT_CLUB_APP_FACILITIES = "pilot.clubapp.api.facilities";
	public static final String PILOT_WEB_CANCELLATION = "pilot.web.cancellation";
	public static final String PILOT_FREEZE_REDUCED_CONTRACT_CHANGE_DATE_30 = "pilot.freeze.reduced.contract.change.date.30";
	public static final String PILOT_FREEZE_REDUCED_CONTRACT_CHANGE_DATE_45 = "pilot.freeze.reduced.contract.change.date.45";
	public static final String PILOT_CANCEL_FREEZE_ELIGIBILITY_RULES = "pilot.cancel.freeze.eligibility.rules";

	// case origin constants
	public static final String ACCOUNT_EQUINOX = "Account Equinox.com";
	public static final String EQUINOX = "Equinox.com";
	public static final String WEBSITE = "Website";
	public static final String API_ERROR = "error from API";
	public static final String CLUB_FRONT_DESK = "Club (Front Desk)";

	public static final int FREEZE_FEE_100 = 100;
	public static final int FREEZE_FEE_50 = 50;
	public static final int FREEZE_FEE_75 = 75;
	public static final int MED_PREGNANCY_FEE = 25;

	public static final String CLUBAPP_PILOT_FREEZE_15_DAYS = "freezeDuration15Days";
	public static final String CLUBAPP_PILOT_FREEZE_30_DAYS = "freezeDuration30Days";
	public static final String CLUBAPP_PILOT_FREEZE_45_DAYS = "freezeDuration45Days";
	// add it for 60 and 90 dyas
	public static final String CLUBAPP_PILOT_FREEZE_60_DAYS = "freezeDuration60Days";
	public static final String CLUBAPP_PILOT_FREEZE_90_DAYS = "freezeDuration90Days";

	public static final String UNIVERSITY_FREEZE = "University Freeze";
	public static final String PILOT_UNIVERSITY_FREEZE_FEE = "pilot.freeze.university.with.fee";
	public static final String REGULAR = "Reg";
	public static final String PILOT_MEDICAL_FREEZE_FEE  = "pilot.freeze.medical.fee";

	public static final String MEDICAL_FEE = "Medical Fee";
	public static final String PREGNANCY_FEE = "Pregnancy Fee";

	public static final String FREEZE_MEDICAL_RECORD_TYPE_ID = "012dz000000IUFlAAO";
	public static final String API_CAST_IRON_USER_ID = "005A0000004o2JQIAY";

	public static final String MEDICAL_PREGNANCY_CASE_DESCRIPTION = "Member requested for freeze due to Medical or pregnancy. Please audit the documents and proceed with approval or rejection";


}
