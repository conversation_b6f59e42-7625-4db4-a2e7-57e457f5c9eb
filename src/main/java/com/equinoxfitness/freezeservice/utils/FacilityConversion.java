/**
 * 
 */
package com.equinoxfitness.freezeservice.utils;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 */
@Component
public class FacilityConversion {

	public String facilityConversion(String facilityId) {
		// check for facilityId
		Map<String, String> facilityMap = new HashMap<String, String>();
		facilityMap.put("060", "401");
		facilityMap.put("005", "105");
		facilityMap.put("004", "104");
		facilityMap.put("071", "701");
		facilityMap.put("072", "702");
		facilityMap.put("003", "103");
		facilityMap.put("007", "107");
		facilityMap.put("113-E", "11301");
		facilityMap.put("148-E", "14801");
		facilityMap.put("054", "141");
		facilityMap.put("052", "140");
		facilityMap.put("008", "108");
		facilityMap.put("009", "109");
		facilityMap.put("002", "102");
		facilityMap.put("006", "106");
		facilityMap.put("010", "110");
		facilityMap.put("011", "111");
		facilityMap.put("014", "112");
		facilityMap.put("CORP1", "999");

		String id = facilityMap.get(facilityId);

		if (id == null) {
			return facilityId;
		}

		return id;

	}

}
