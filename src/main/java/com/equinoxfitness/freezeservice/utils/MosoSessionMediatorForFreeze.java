/**
 * 
 */
package com.equinoxfitness.freezeservice.utils;

import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.common.moso.contract.NewSessionData;



/**
 * <AUTHOR>
 *
 */
@Service
public class MosoSessionMediatorForFreeze extends WebServiceGatewaySupport{
private static final Logger logger= LoggerFactory.getLogger(MosoSessionMediatorForFreeze.class);
	
	@Value("${freeze.moso.getSession.url}")
	private String getMoSoSessionUrl;
		
	@Value("${freeze.moso.user}")
	private String userName;
	
	@Value("${freeze.moso.password}")
	private String password;

	@Value("#{${freeze.defaultFacilityByCountryCode.map}}")
	private Map<Integer,String> defaultFacilityByCountryCode;
	
	@Value("${freeze.defaultFacility}")
	private String defaultFacilityId;
	
	@PostConstruct
	public void init() {
		logger.info("Loaded MoSoSessionMediator...");
		logger.info("Default URI: "+this.getDefaultUri());
	}

	/**
	 * Get a session for Moso transaction. This is for read operation only. For any insert/update related operations. please pass in the facilityId so the correct facility get the credit.
	 */
	public GetSessionOutput getSession(Integer countryCode) {
		return getSession(countryCode, null,null);
	}

	/**
	 * Get a session for Moso transaction.
	 */
	public GetSessionOutput getSession(Integer countryCode, String facilityId,String threadId) {
		logger.debug("Invoking getSession");
		
		String endpointKey = getEndPointKey(countryCode, facilityId,threadId);
		return getSessionInMoso(endpointKey,threadId);
	}

	/**
	 * Call Moso API to get auth token and cookie value
	 * @param endpointkey endpoint used by Moso to identity club
	 * @return auth token and cookie value
	 */
	private GetSessionOutput getSessionInMoso(String endpointkey,String threadId) {
		logger.debug("getting sesssion for " + endpointkey);
		ResponseEntity<String> getSessionResponse = null;
		GetSessionOutput output = new GetSessionOutput();
		String correlationId = threadId;
		logger.debug("Correlation-ID : " + correlationId);

		NewSessionData newSessionData = new NewSessionData();
		newSessionData.setUsername(userName);
		newSessionData.setPassword(password);
		newSessionData.setEndpointkey(endpointkey);
		
		// Set headers for the request
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Correlation-ID", correlationId);
		HttpEntity<Object> requestWithHeader = new HttpEntity<>(newSessionData, headers);
		
		try {
			RestTemplate restTemplate = new RestTemplate();
			getSessionResponse = restTemplate.postForEntity(getMoSoSessionUrl, requestWithHeader, String.class);
		} catch (HttpServerErrorException | HttpClientErrorException e) {
			logger.error("GetSession failed!!!");
			logger.error(e.getResponseBodyAsString());
			//output.setMessage(e.getResponseBodyAsString());
			return output;
		} catch (Exception e) {
			logger.error("GetSession failed !!!");
			logger.error(e.getMessage());
			//output.setMessage(e.getMessage());
			return output;
		}
		
		//cookie value
		List<String> cookie = getSessionResponse.getHeaders().get("Set-Cookie");
		for (String c : cookie) {
			if (c.contains("AWSELB")) {
				output.setCookieValue(c);
			}
		}
		
		output.setDetailField(endpointkey);
		output.setAuthTokenValue(getSessionResponse.getBody().replace("\"", ""));
		
		return output;
	}

	private String getEndPointKey(Integer countryCode, String facilityId,String correlationId) {
		String endPoint = "-Web_Endpoint";
		//String correlationId = httpServletRequest.getHeader("Correlation-ID");
		
		if (facilityId == null || "998".equals(facilityId)) {
			facilityId = defaultFacilityByCountryCode.getOrDefault(countryCode, defaultFacilityId);
		}
		
		//special case for corp facility
		if ("CORP".equalsIgnoreCase(facilityId)) {
			facilityId += countryCode;
			endPoint = "-API_Endpoint";
		}
		
		if(!StringUtils.isBlank(correlationId)) {
			correlationId=correlationId.toLowerCase();
			if (correlationId.startsWith("engage")) {
				endPoint = "-ENGAGE_Endpoint";
			}else if (correlationId.startsWith("kraken")) {
				endPoint = "-API_Endpoint";
			}
		}
		
		return facilityId + endPoint;
	}
}
