package com.equinoxfitness.freezeservice.utils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.equinoxfitness.commons.utils.CountryCode;
import com.equinoxfitness.freezeservice.contract.BillingInformation;
import com.equinoxfitness.freezeservice.contract.CaptureRequest;
import com.equinoxfitness.freezeservice.contract.CreditCard;
import com.equinoxfitness.freezeservice.contract.EncryptInput;
import com.equinoxfitness.freezeservice.contract.FundingSource;
import com.equinoxfitness.freezeservice.contract.PreAuthorizeRequest;
import com.equinoxfitness.freezeservice.contract.VoidRequest;
import com.equinoxfitness.freezeservice.dao.impl.CardOnFileDaoImpl;

@Service
public class FreezeServiceUtilsImpl implements FreezeServiceUtils {
	
	@Value("#{${ccType.map}}")
	private Map<String, String> ccTypeMap;
	
	@Autowired
	CardOnFileDaoImpl cardOnFileDaoImpl;
	
	Logger logger = LoggerFactory.getLogger(FreezeServiceUtilsImpl.class);
	
	private static final String MOSO_EXPIRE_DATE_FORMAT = "MMyy";

	@Override
	public FundingSource toFundingSource(String memberId) {
		FundingSource fundingSource = cardOnFileDaoImpl.getFundingSource(memberId);
		String ccType = ccTypeMap.get(fundingSource.getCreditType()); //convert moso cof credit card type to cc type used in payment service
		if (ccType != null)
			fundingSource.setCreditType(ccType);
		
		return fundingSource;
	}

	@Override
	public EncryptInput toEncryptCC(CreditCard creditCard, String tokenFormat, int countryCode, String facilityId, 
			String requestedBy, String threadId) {
		EncryptInput encryptInput = new EncryptInput();

		String creditCardType = "";

		switch (creditCard.getCreditCardType()) {
		case 1:
			creditCardType = "AMERICANEXPRESS";
			break;
		case 2:
			creditCardType = "DISCOVER";
			break;
		case 6:
			creditCardType = "MASTERCARD";
			break;
		case 7:
			creditCardType = "VISA";
			break;
		case 8:
			creditCardType = "Debit Card";
			break;

		}
		encryptInput.setCountryCode(countryCode);
		encryptInput.setCreditCardNumber(creditCard.getCreditCardNumber());
		encryptInput.setCreditCardType(creditCardType);
		encryptInput.setCurrency(CountryCode.valueOf(countryCode).currency());
		encryptInput.setCvv(creditCard.getCvvNumber());
		encryptInput.setExpiryDate(creditCard.getCreditCardExpiryDate());
		encryptInput.setFacilityId(facilityId);
		encryptInput.setNameOnCard(creditCard.getNameOnCard());
		encryptInput.setPaymentGateway(tokenFormat);
		encryptInput.setTokenFormat(tokenFormat);
		encryptInput.setRequestedBy(requestedBy);
		encryptInput.setThreadId(threadId);

		return encryptInput;
	}

	@Override
	public PreAuthorizeRequest toPreAuthorizeRequest(FundingSource fundingSource, BigDecimal amount,
			int countryCode, String mosoId, String tokenFormat, String facilityId, String threadId) {
		SimpleDateFormat mosoExpireDateFormat = new SimpleDateFormat(MOSO_EXPIRE_DATE_FORMAT);

		PreAuthorizeRequest preAuthorizeRequest = new PreAuthorizeRequest();
		BillingInformation billingInformation  = new BillingInformation();
		
		preAuthorizeRequest.setAmount(amount);
		preAuthorizeRequest.setCreditCardToken(fundingSource.getCreditCardToken());
		preAuthorizeRequest.setTokenFormat(tokenFormat);
		preAuthorizeRequest.setCurrency(CountryCode.valueOf(countryCode).currency());
		preAuthorizeRequest.setExpiryDate(mosoExpireDateFormat.format(fundingSource.getExpiryDate().getTime()));
		preAuthorizeRequest.setMemberId(fundingSource.getMemberId());
		preAuthorizeRequest.setNameOnCard(fundingSource.getCardHolderName());
		preAuthorizeRequest.setReferenceNum("FZ" + mosoId);
		preAuthorizeRequest.setCreditCardType(fundingSource.getCreditType());
		preAuthorizeRequest.setFacilityId(facilityId);
		preAuthorizeRequest.setThreadId(threadId);
		preAuthorizeRequest.setMemberId(mosoId);

		billingInformation.setAddress1(fundingSource.getBillingAddress1());
		billingInformation.setAddress2(fundingSource.getBillingAddress2());
		billingInformation.setCity(fundingSource.getBillingCity());
		billingInformation.setState(fundingSource.getBillingState());
		billingInformation.setZip(fundingSource.getBillingPostalCode());
		billingInformation.setCountry(fundingSource.getBillingCountry());
		
		preAuthorizeRequest.setBillingInformation(billingInformation);
		return preAuthorizeRequest;
	}

	@Override
	public CaptureRequest toCaptureRequest(BigDecimal freezeFees, int countryCode, String mosoId,
			String transactionTag, String correlationId) {
		CaptureRequest captureRequest = new CaptureRequest();
		captureRequest.setAmount(freezeFees);
		captureRequest.setCurrencyCode(CountryCode.valueOf(countryCode).currency());
		captureRequest.setRequestedBy("FZ" + mosoId);
		captureRequest.setTransactionTag(transactionTag);
		captureRequest.setTransactionType("capture");
		captureRequest.setThreadId(correlationId);

		return captureRequest;
	}

	@Override
	public VoidRequest toVoidRequest(BigDecimal freezeFees, int countryCode, String referenceNumber, String transactionTag) {
		VoidRequest voidRequest = new VoidRequest();
		voidRequest.setAmount(freezeFees);
		voidRequest.setCurrencyCode(CountryCode.valueOf(countryCode).currency());
		voidRequest.setRequestedBy(referenceNumber);
		voidRequest.setTransactionTag(transactionTag);
		voidRequest.setTransactionType("void");
		
		return voidRequest;
	}

}
