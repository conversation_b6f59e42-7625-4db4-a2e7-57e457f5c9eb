/**
 * 
 */
package com.equinoxfitness.freezeservice.utils;

import java.math.BigDecimal;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import javax.net.ssl.SSLContext;

import com.equinoxfitness.freezeservice.contract.*;
import com.equinoxfitness.freezeservice.contract.sfdc.*;
import com.equinoxfitness.freezeservice.contract.sfdc.Record;
import org.apache.commons.lang.StringUtils;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.TrustStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.common.moso.contract.MosoTokenResponse;
import com.equinoxfitness.commons.service.EventsService;
import com.equinoxfitness.commons.utils.DynamoPK;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.invoice.CreditCardAuthResult;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewFinalizeInvoice;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewInvoiceItem;
import com.equinoxfitness.freezeservice.contract.moso.invoice.PaymentTender;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.salesforce.contract.Wrapper;
import com.equinoxfitness.salesforce.service.SalesforceApiService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

/**
 * <AUTHOR>
 *
 */
@Component
public class FreezeServiceHelper {
	
	@Value("${token.hypergate.url}")
    private  String tokenUrl;
	
	@Value("${token.hypergate.apikey}")
	private  String apikeyV2;
	
	@Autowired
	private EventsService eventsService;
	
	@Autowired
	private SalesforceApiService salesforceApiService;

	private static final Logger logger = LoggerFactory.getLogger(FreezeServiceHelper.class);
	
	private static final String MOSO_EXPIRE_DATE_FORMAT = "MMyy";
	
	DateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
	
	private static final String EST_TIMZONE_REGION = "America/New_York";

	public Note setnoteRequest(String memberId, String content) {

		Note note = new Note();
		note.setContactTypeId(4);
		note.setId(memberId);
		note.setNoteTypeId(10);
		note.setCheckinAlert(false);
		note.setContent(content);
		return note;
	}
	
	public Note setnoteRequest(String memberId, String content, int noteTypeId) {

		Note note = new Note();
		note.setContactTypeId(4);
		note.setId(memberId);
		note.setNoteTypeId(noteTypeId);
		note.setCheckinAlert(false);
		note.setContent(content);
		return note;
	}

	public NewFinalizeInvoice setCreateAndFinalizeReq(double itemPrice, FreezeExtensionInput freezeExtensionInput,
			String itemCode, int clientAccountId, String accountingCode) {

		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date currentDate = Calendar.getInstance().getTime();
		newFinalizeInvoice.setAccountId(clientAccountId);
		newFinalizeInvoice.setBusinessUnitCode(accountingCode);
		List<NewInvoiceItem> itemInvoiceList = new ArrayList<NewInvoiceItem>();
		NewInvoiceItem invoiceItem = new NewInvoiceItem();
		invoiceItem.setIsItem(true);
		invoiceItem.setItemCode(itemCode);
		invoiceItem.setPrice(BigDecimal.valueOf(itemPrice));
		invoiceItem.setQuantity(freezeExtensionInput.getExtensionDurationMonths());
		itemInvoiceList.add(invoiceItem);
		newFinalizeInvoice.setItems(itemInvoiceList);
		newFinalizeInvoice.setMemberId(freezeExtensionInput.getMemberId());
		List<PaymentTender> paymentTenderList = new ArrayList<PaymentTender>();
		PaymentTender paymentTender = new PaymentTender();
		paymentTender.setAmount(BigDecimal.valueOf(itemPrice * freezeExtensionInput.getExtensionDurationMonths()));
		paymentTender.setClientAccountId(clientAccountId);
		paymentTender.setTenderTypeId(108);
		paymentTenderList.add(paymentTender);
		newFinalizeInvoice.setPayments(paymentTenderList);
		newFinalizeInvoice.setTargetDate(dateFormat.format(currentDate));
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request " + mapper.writeValueAsString(newFinalizeInvoice));
		} catch (JsonProcessingException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		return newFinalizeInvoice;
	}
	
	public NewFinalizeInvoice setCreateAndFinalizeReqV2(double itemPrice, FreezeExtensionInputV2 freezeExtensionInput,
			String itemCode, int clientAccountId, String accountingCode, int countryCode) {
		Integer tenderTypeId = countryCode == 1 ? FreezeMembershipConstants.TENDER_TYPE_ID_PAYEEZY : FreezeMembershipConstants.TENDER_TYPE_ID_CREDIT_CARD;
		
		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date currentDate = Calendar.getInstance().getTime();
		newFinalizeInvoice.setAccountId(clientAccountId);
		newFinalizeInvoice.setBusinessUnitCode(accountingCode);
		List<NewInvoiceItem> itemInvoiceList = new ArrayList<NewInvoiceItem>();
		NewInvoiceItem invoiceItem = new NewInvoiceItem();
		invoiceItem.setIsItem(true);
		invoiceItem.setItemCode(itemCode);
		invoiceItem.setPrice(BigDecimal.valueOf(itemPrice));
		invoiceItem.setQuantity(freezeExtensionInput.getExtensionDurationMonths());
		itemInvoiceList.add(invoiceItem);
		newFinalizeInvoice.setItems(itemInvoiceList);
		newFinalizeInvoice.setMemberId(freezeExtensionInput.getMosoId());
		List<PaymentTender> paymentTenderList = new ArrayList<PaymentTender>();
		PaymentTender paymentTender = new PaymentTender();
		paymentTender.setAmount(BigDecimal.valueOf(itemPrice * freezeExtensionInput.getExtensionDurationMonths()));
		paymentTender.setClientAccountId(clientAccountId);
		paymentTender.setTenderTypeId(108); //SPACES-2998, Reverted to 108 until new feature rolls out
		paymentTenderList.add(paymentTender);
		newFinalizeInvoice.setPayments(paymentTenderList);
		newFinalizeInvoice.setTargetDate(dateFormat.format(currentDate));
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request " + mapper.writeValueAsString(newFinalizeInvoice));
		} catch (JsonProcessingException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		return newFinalizeInvoice;
	}
	
	public NewFinalizeInvoice setCreateAndFinalizeReqV3(double itemPrice, FreezeExtensionInputV3 freezeExtensionInput,
			String itemCode, int clientAccountId, String accountingCode, int countryCode) {
		Integer tenderTypeId = countryCode == 1 ? FreezeMembershipConstants.TENDER_TYPE_ID_PAYEEZY : FreezeMembershipConstants.TENDER_TYPE_ID_CREDIT_CARD;
		
		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date currentDate = Calendar.getInstance().getTime();
		newFinalizeInvoice.setAccountId(clientAccountId);
		newFinalizeInvoice.setBusinessUnitCode(accountingCode);
		List<NewInvoiceItem> itemInvoiceList = new ArrayList<NewInvoiceItem>();
		NewInvoiceItem invoiceItem = new NewInvoiceItem();
		invoiceItem.setIsItem(true);
		invoiceItem.setItemCode(itemCode);
		invoiceItem.setPrice(BigDecimal.valueOf(itemPrice));
		invoiceItem.setQuantity(freezeExtensionInput.getExtensionDurationMonths());
		itemInvoiceList.add(invoiceItem);
		newFinalizeInvoice.setItems(itemInvoiceList);
		newFinalizeInvoice.setMemberId(freezeExtensionInput.getMosoId());
		List<PaymentTender> paymentTenderList = new ArrayList<PaymentTender>();
		PaymentTender paymentTender = new PaymentTender();
		paymentTender.setAmount(BigDecimal.valueOf(itemPrice * freezeExtensionInput.getExtensionDurationMonths()));
		paymentTender.setClientAccountId(clientAccountId);
		paymentTender.setTenderTypeId(108); //SPACES-2998, Reverted to 108 until new feature rolls out
		paymentTenderList.add(paymentTender);
		newFinalizeInvoice.setPayments(paymentTenderList);
		newFinalizeInvoice.setTargetDate(dateFormat.format(currentDate));
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request " + mapper.writeValueAsString(newFinalizeInvoice));
		} catch (JsonProcessingException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		return newFinalizeInvoice;
	}

	public NewFinalizeInvoice setCreateAndFinalizeReqV5(double itemPrice, int durationMonths, String mosoId,
														String itemCode, int clientAccountId, String accountingCode, int countryCode) {
		Integer tenderTypeId = countryCode == 1 ? FreezeMembershipConstants.TENDER_TYPE_ID_PAYEEZY : FreezeMembershipConstants.TENDER_TYPE_ID_CREDIT_CARD;

		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date currentDate = Calendar.getInstance().getTime();
		newFinalizeInvoice.setAccountId(clientAccountId);
		newFinalizeInvoice.setBusinessUnitCode(accountingCode);
		List<NewInvoiceItem> itemInvoiceList = new ArrayList<NewInvoiceItem>();
		NewInvoiceItem invoiceItem = new NewInvoiceItem();
		invoiceItem.setIsItem(true);
		invoiceItem.setItemCode(itemCode);
		invoiceItem.setPrice(BigDecimal.valueOf(itemPrice));
		invoiceItem.setQuantity(durationMonths);
		itemInvoiceList.add(invoiceItem);
		newFinalizeInvoice.setItems(itemInvoiceList);
		newFinalizeInvoice.setMemberId(mosoId);
		List<PaymentTender> paymentTenderList = new ArrayList<PaymentTender>();
		PaymentTender paymentTender = new PaymentTender();
		paymentTender.setAmount(BigDecimal.valueOf(itemPrice * durationMonths));
		paymentTender.setClientAccountId(clientAccountId);
		paymentTender.setTenderTypeId(108); //SPACES-2998, Reverted to 108 until new feature rolls out
		paymentTenderList.add(paymentTender);
		newFinalizeInvoice.setPayments(paymentTenderList);
		newFinalizeInvoice.setTargetDate(dateFormat.format(currentDate));
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request " + mapper.writeValueAsString(newFinalizeInvoice));
		} catch (JsonProcessingException e1) {
			logger.error("Failed to serialize NewFinalizeInvoice: {}", e1.getMessage());
		}
		return newFinalizeInvoice;
	}

	public SuspensionRequest setSuspensionRequest(String freezeReasonId, MemberAgreementDetail memberAgreementDetail,
			FreezeMembershipInput freezeMembershipInput) {

		SuspensionRequest suspensionRequest = new SuspensionRequest();
		DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
		suspensionRequest.setAgreementId(memberAgreementDetail.getMemberAgreementId());
		suspensionRequest.setComments(freezeMembershipInput.getDescription());
		if (freezeMembershipInput.getEndDate() != null) {
			suspensionRequest
					.setFreezeEndDate(dateFormat.format(freezeMembershipInput.getEndDate()).replaceAll("-", ""));
		}
		suspensionRequest.setFreezeReasonId(Integer.valueOf(freezeReasonId));
		if (freezeMembershipInput.getStartDate() != null) {
			suspensionRequest
					.setFreezeStartDate(dateFormat.format(freezeMembershipInput.getStartDate()).replaceAll("-", ""));
		}
		if (freezeMembershipInput.getRequestedDate() != null) {
			suspensionRequest
					.setRequestedDate(dateFormat.format(freezeMembershipInput.getRequestedDate()).replaceAll("-", ""));
		}
		suspensionRequest.setRoleId(freezeMembershipInput.getMemberId());
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request :{}" , mapper.writeValueAsString(suspensionRequest));
		} catch (JsonProcessingException e1) {
			logger.error("Exception : {} ",e1.getMessage());
		}
		return suspensionRequest;
	}
	
	public SuspensionRequest setSuspensionRequestV2(String freezeReasonId, MemberAgreementDetail memberAgreementDetail,
			FreezeMembershipInputV2 freezeMembershipInput) {

		SuspensionRequest suspensionRequest = new SuspensionRequest();
		DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
		suspensionRequest.setAgreementId(memberAgreementDetail.getMemberAgreementId());
		suspensionRequest.setComments(freezeMembershipInput.getDescription());
		if (freezeMembershipInput.getEndDate() != null) {
			suspensionRequest
					.setFreezeEndDate(dateFormat.format(freezeMembershipInput.getEndDate()).replaceAll("-", ""));
		}
		suspensionRequest.setFreezeReasonId(Integer.valueOf(freezeReasonId));
		if (freezeMembershipInput.getStartDate() != null) {
			suspensionRequest
					.setFreezeStartDate(dateFormat.format(freezeMembershipInput.getStartDate()).replaceAll("-", ""));
		}
		if (freezeMembershipInput.getRequestedDate() != null) {
			suspensionRequest
					.setRequestedDate(dateFormat.format(freezeMembershipInput.getRequestedDate()).replaceAll("-", ""));
		}
		suspensionRequest.setRoleId(freezeMembershipInput.getMosoId());
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request :{}" , mapper.writeValueAsString(suspensionRequest));
		} catch (JsonProcessingException e1) {
			logger.error("Exception : {} ",e1.getMessage());
		}
		return suspensionRequest;
	}
	
	public SuspensionRequest setSuspensionRequestV3(String freezeReasonId, MemberAgreementDetail memberAgreementDetail,
			FreezeMembershipInputV4 freezeMembershipInput) {

		SuspensionRequest suspensionRequest = new SuspensionRequest();
		DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
		suspensionRequest.setAgreementId(memberAgreementDetail.getMemberAgreementId());
		suspensionRequest.setComments(freezeMembershipInput.getDescription());
		if (freezeMembershipInput.getEndDate() != null) {
			suspensionRequest
					.setFreezeEndDate(dateFormat.format(freezeMembershipInput.getEndDate()).replaceAll("-", ""));
		}
		suspensionRequest.setFreezeReasonId(Integer.valueOf(freezeReasonId));
		if (freezeMembershipInput.getStartDate() != null) {
			suspensionRequest
					.setFreezeStartDate(dateFormat.format(freezeMembershipInput.getStartDate()).replaceAll("-", ""));
		}
		if (freezeMembershipInput.getRequestedDate() != null) {
			suspensionRequest
					.setRequestedDate(dateFormat.format(freezeMembershipInput.getRequestedDate()).replaceAll("-", ""));
		}
		suspensionRequest.setRoleId(freezeMembershipInput.getMosoId());
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request :{}" , mapper.writeValueAsString(suspensionRequest));
		} catch (JsonProcessingException e1) {
			logger.error("Exception : {} ",e1.getMessage());
		}
		return suspensionRequest;
	}
	
	public SuspensionRequest setSuspensionRequestV3(String freezeReasonId,  MemberAgreementDetail memberAgreementDetail,
			FreezeMembershipInputV3 freezeMembershipInput,Date endDate) {
		SuspensionRequest suspensionRequest = new SuspensionRequest();
		DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
		suspensionRequest.setAgreementId(memberAgreementDetail.getMemberAgreementId());
		suspensionRequest.setComments(freezeMembershipInput.getComments());
		if (endDate != null) {
			suspensionRequest
					.setFreezeEndDate(dateFormat.format(endDate).replaceAll("-", ""));
		}
		suspensionRequest.setFreezeReasonId(Integer.valueOf(freezeReasonId));
		if (freezeMembershipInput.getClubOpenDate() != null) {
			suspensionRequest
					.setFreezeStartDate(dateFormat.format(freezeMembershipInput.getClubOpenDate()).replaceAll("-", ""));
		}
		if (freezeMembershipInput.getRequestedDate() != null) {
			suspensionRequest
					.setRequestedDate(dateFormat.format(freezeMembershipInput.getRequestedDate()).replaceAll("-", ""));
		}
		suspensionRequest.setRoleId(freezeMembershipInput.getMosoId());
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request :{}" , mapper.writeValueAsString(suspensionRequest));
		} catch (JsonProcessingException e1) {
			logger.error("Exception : {} ",e1.getMessage());
		}
		return suspensionRequest;
	}

	public CheckFreezeEligibilityInput setCheckFreezeEligibilityInput(FreezeMembershipInput freezeMembershipInput) {

		CheckFreezeEligibilityInput checkFreezeEligibilityInput = new CheckFreezeEligibilityInput();
		checkFreezeEligibilityInput.setDurationMonths(freezeMembershipInput.getDurationMonths());
		checkFreezeEligibilityInput.setEndDate(freezeMembershipInput.getEndDate());
		checkFreezeEligibilityInput.setFreezeReason(freezeMembershipInput.getFreezeReasonId());
		checkFreezeEligibilityInput.setMemberId(freezeMembershipInput.getMemberId());
		checkFreezeEligibilityInput.setRequestedDate(freezeMembershipInput.getRequestedDate());
		checkFreezeEligibilityInput.setStartDate(freezeMembershipInput.getStartDate());
		return checkFreezeEligibilityInput;
	}
	
	public CheckFreezeEligibilityInput setCheckFreezeEligibilityInputV2(FreezeMembershipInputV2 freezeMembershipInput) {

		CheckFreezeEligibilityInput checkFreezeEligibilityInput = new CheckFreezeEligibilityInput();
		checkFreezeEligibilityInput.setDurationMonths(freezeMembershipInput.getDurationMonths());
		checkFreezeEligibilityInput.setEndDate(freezeMembershipInput.getEndDate());
		checkFreezeEligibilityInput.setFreezeReason(freezeMembershipInput.getFreezeReasonId());
		checkFreezeEligibilityInput.setMemberId(freezeMembershipInput.getMosoId());
		checkFreezeEligibilityInput.setRequestedDate(freezeMembershipInput.getRequestedDate());
		checkFreezeEligibilityInput.setStartDate(freezeMembershipInput.getStartDate());
		return checkFreezeEligibilityInput;
	}
	
	public CheckFreezeEligibilityInput setCheckFreezeEligibilityInputV3(FreezeMembershipInputV3 freezeMembershipInput,Date endDate) {
		CheckFreezeEligibilityInput checkFreezeEligibilityInput = new CheckFreezeEligibilityInput();
		checkFreezeEligibilityInput.setEndDate(endDate);
		checkFreezeEligibilityInput.setFreezeReason(freezeMembershipInput.getFreezeReason());
		checkFreezeEligibilityInput.setMemberId(freezeMembershipInput.getMosoId());
		checkFreezeEligibilityInput.setRequestedDate(freezeMembershipInput.getRequestedDate());
		checkFreezeEligibilityInput.setStartDate(freezeMembershipInput.getClubOpenDate());
		return checkFreezeEligibilityInput;
	}

	public HttpEntity<HttpHeaders> setEntity(GetSessionOutput sessionOutput) {

		logger.debug("AuthToken : {} ", sessionOutput.getAuthTokenValue());
		logger.debug("Cookie : {}", sessionOutput.getCookieValue());

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("authToken",sessionOutput.getAuthTokenValue());
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);
		return entity;

	}

	public String createToken(TokenRequest input, String correlationId, String clubLocalTime) {		
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX"); 
		LocalDateTime localDateTime = LocalDateTime.parse(clubLocalTime, formatter);
		long startTime = System.currentTimeMillis();
		HttpHeaders headers = new HttpHeaders();
		headers.set("apikey", apikeyV2);
		headers.set("Content-Type", "application/json");
		HttpEntity<?> httpEntity = new HttpEntity<Object>(input,headers);		
		RestTemplate restTemplate=getSSLtemplate();
		ResponseEntity<String> entity=null;
		String token = null;
		try{
			String url=tokenUrl+"?expirationTime="+localDateTime.plusDays(FreezeMembershipConstants.FREEZE_EMAIL_LINK_EXPIRATION_DAYS);
			entity=restTemplate.exchange(url,HttpMethod.POST,httpEntity,String.class);
			token=entity.getBody();
			logger.info("generated freeze email token {} for member {}",token,input.getTokenData().getMemberId());		
			eventsService.logEventsInDynamoTrace(DynamoPK.MEMBER_ID+input.getTokenData().getMemberId(),"clubapp-api-gatekeeper-freeze.token."+input.getTokenData().getMemberId(), correlationId, correlationId, input, entity.getBody(), entity.getStatusCode(), System.currentTimeMillis() - startTime);
		}catch(Exception e){
			logger.error("error while creating the token: {}",e.getMessage());
		}
		return token;
	}
	
	private  RestTemplate getSSLtemplate(){
		
		RestTemplate restTemplate = new RestTemplate();
	    SSLContext sslContext;
	    try {
	    	TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
	    	sslContext = org.apache.http.ssl.SSLContexts.custom()
	    			.loadTrustMaterial(null, acceptingTrustStrategy)
	    			.build();

	    	SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

	    	CloseableHttpClient httpClient = HttpClients.custom()
	    			.setSSLSocketFactory(csf)
	    			.build();

	    	HttpComponentsClientHttpRequestFactory requestFactory =
	    			new HttpComponentsClientHttpRequestFactory();

	    	requestFactory.setHttpClient(httpClient);
	    	restTemplate = new RestTemplate(requestFactory);

	    } catch (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e) {
			logger.error(e.getMessage());
			e.printStackTrace();
		} 

	    
	    return restTemplate;
	}

	public NewFinalizeInvoice setCreateAndFinalizeReqV4(double itemPrice, int durationMonths, String mosoId, String itemCode, int clientAccountId, 
			String accountingCode, int countryCode, BigDecimal totalAmount, FundingSource fundingSource, PreAuthorizeResult preAuthResult) {
		
		Integer tenderTypeId = countryCode == 1 ? FreezeMembershipConstants.TENDER_TYPE_ID_PAYEEZY : FreezeMembershipConstants.TENDER_TYPE_ID_CREDIT_CARD;
		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date currentDate = Calendar.getInstance().getTime();
		SimpleDateFormat mosoExpireDateFormat = new SimpleDateFormat(MOSO_EXPIRE_DATE_FORMAT);
		String comments = "";
		try {
			comments = "CC Type :"+fundingSource.getCreditType()+",CC Last 4 :"+fundingSource.getDisplayMask()+",Auth Code :"+preAuthResult.getReferenceNumber()+",OrderId : "+mosoId;
		} catch (Exception e) {
			e.printStackTrace();
		}
		newFinalizeInvoice.setAccountId(clientAccountId);
		newFinalizeInvoice.setBusinessUnitCode(accountingCode);
		List<NewInvoiceItem> itemInvoiceList = new ArrayList<NewInvoiceItem>();
		NewInvoiceItem invoiceItem = new NewInvoiceItem();
		invoiceItem.setIsItem(true);
		invoiceItem.setItemCode(itemCode);
		invoiceItem.setPrice(BigDecimal.valueOf(itemPrice));	// Amount with out tax i.e freeze fees
		invoiceItem.setQuantity(durationMonths);
		itemInvoiceList.add(invoiceItem);
		newFinalizeInvoice.setItems(itemInvoiceList);
		newFinalizeInvoice.setMemberId(mosoId);
		
		List<PaymentTender> paymentTenderList = new ArrayList<PaymentTender>();
		CreditCardAuthResult creditCardAuthResult = new CreditCardAuthResult();
		creditCardAuthResult.setExpirationDate(mosoExpireDateFormat.format(fundingSource.getExpiryDate().getTime()));
		creditCardAuthResult.setPostalCode(fundingSource.getBillingPostalCode());
		creditCardAuthResult.setAccountName(fundingSource.getCardHolderName());
		creditCardAuthResult.setProcessorCode(preAuthResult.getProcessorCode());
		creditCardAuthResult.setReferenceNumber(preAuthResult.getReferenceNumber());
		creditCardAuthResult.setCreditCardToken(fundingSource.getCreditCardToken());
		creditCardAuthResult.setCreditCardMask(fundingSource.getDisplayMask());
		
		PaymentTender paymentTender = new PaymentTender();
		paymentTender.setAmount(totalAmount); 	// Total calculated amount with tax 
		paymentTender.setClientAccountId(clientAccountId);
		paymentTender.setTenderTypeId(tenderTypeId);
		paymentTender.setCreditCardAuthResult(creditCardAuthResult );
		paymentTender.setTenderTypeId(108); //SPACES-2998, Reverted to 108 until new feature rolls out
		paymentTenderList.add(paymentTender);
		newFinalizeInvoice.setPayments(paymentTenderList);
		newFinalizeInvoice.setTargetDate(dateFormat.format(currentDate));
		newFinalizeInvoice.setComments(comments);
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		try {
			logger.debug("API Request " + mapper.writeValueAsString(newFinalizeInvoice));
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		return newFinalizeInvoice;
	}

	public String createNoteInEngage(String memberId, String firstName, String contactId, String notes, String emailNoteTitle) {
		logger.info("Entered createNoteAndToken method()....");
		
		Wrapper<ContactData> contactData = null;
		if(StringUtils.isEmpty(firstName)) {
			logger.info("Entering Salesforce common-lib to perform get() call for the first name for memberId: "+memberId);
			contactData = salesforceApiService.get(DynamoPK.MEMBER_ID+memberId,"Select firstname from Contact WHERE id='"+contactId+"'", ContactData.class);
			
			if(contactData == null || contactData.getData()==null || contactData.getData().getTotalSize() == 0) {
				logger.info("Unable to fecth first name for the give contact: "+contactId);
				return null;
			}
			
			if(contactData.getData().getRecords().get(0).getFirstName() != null) {
				firstName = contactData.getData().getRecords().get(0).getFirstName();
				logger.info("Got the FirstName from Salesforce/Engage: "+firstName);
			}
		}
		
		NoteInput noteInput=new NoteInput();
		noteInput.setId(null);
		noteInput.setParentId(contactId);
		noteInput.setTitle(emailNoteTitle +" "+ firstName);
		noteInput.setBody(notes);
		
		logger.info("Entering Salesforce common-lib to perform post() call to create the DS Sent Note for memberId: "+memberId);
		Wrapper<NoteOutput> noteOutput = salesforceApiService.post(DynamoPK.MEMBER_ID+memberId,"note", noteInput, NoteOutput.class);
		
		if(noteOutput!=null && noteOutput.getData().getSuccess()==true){
			String responseFromEngage=noteOutput.getData().getId();
			logger.debug("Created note in Salesforce/Engage for memberId: {} & token from engage: {}",memberId,responseFromEngage);
			 return responseFromEngage;
		 }
		else { 
			logger.info("Failed to created the DS Sent Note for memberId: "+memberId);
			return null;
		}
	}

	public HttpEntity<HttpHeaders> setEntityV4(MosoTokenResponse mosoTokenResponse) {
		logger.debug("AuthToken : {} ", mosoTokenResponse.getMosoSessionToken());

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("authToken",mosoTokenResponse.getMosoSessionToken());
		headers.set("mosouserapikey", mosoTokenResponse.getMosoUserApikey());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);
		return entity;
	}

	public boolean isUniversityMember(String memberId) {
		// call salesforce with query
		logger.info("Checking if member is a university member for memberId: {}", memberId);
		boolean isUniversityMember = false;
		String query = "select Id, Corp_Group__r.Industry from Contact where Moso_Id__c = '" + memberId + "'";
		try {
			Wrapper<ContactData> domainDetails = salesforceApiService.get(query, ContactData.class);
			List<Record> records = Optional.ofNullable(domainDetails).map(Wrapper::getData)
					.map(ContactData::getRecords).orElse(Collections.emptyList());
			if (!CollectionUtils.isEmpty(records)) {
				for (Record newRecord : records) {
					String industry = newRecord.getCorpGroup() == null ? null : newRecord.getCorpGroup().getIndustry();
					if (industry != null && industry.equalsIgnoreCase("Education/School")) {
						isUniversityMember = true;
						logger.info("Member is a university member for memberId: {}", memberId);
						break;
					}
				}
			}
			if (!isUniversityMember) {
				logger.info("Member is not a university member for memberId: {}", memberId);
			}
		} catch (Exception e) {
			logger.error("Error while checking if member is a university member for memberId: {}, error: {}", memberId, e.getMessage());
			return false;
		}
		return isUniversityMember;
	}

	public MedicalFreezeEmailInput setMedicalFreezeEmailInput(MemberFreezeCase memberFreezeCase) {
		MedicalFreezeEmailInput medicalFreezeEmailInput = new MedicalFreezeEmailInput();
		medicalFreezeEmailInput.setMemberId(memberFreezeCase.getMosoId());
		medicalFreezeEmailInput.setEmailAddress(memberFreezeCase.getEmailAddress());
		medicalFreezeEmailInput.setMemberFirstName(memberFreezeCase.getMemberFirstName());
		medicalFreezeEmailInput.setStartDate(memberFreezeCase.getStartDate());
		if (memberFreezeCase.getDurationMonths() > 0) {
			medicalFreezeEmailInput.setDurationMonths(memberFreezeCase.getDurationMonths());
		}
		medicalFreezeEmailInput.setFreezeFees(memberFreezeCase.getFreezeFees());
		medicalFreezeEmailInput.setCountryCode(memberFreezeCase.getCountryCode());
		medicalFreezeEmailInput.setPilotFreezeMedFee(memberFreezeCase.isPilotFreezeMedFee());
		return medicalFreezeEmailInput;
	}
}
