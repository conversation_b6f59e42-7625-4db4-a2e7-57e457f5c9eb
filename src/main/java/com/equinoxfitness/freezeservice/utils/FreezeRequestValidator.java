package com.equinoxfitness.freezeservice.utils;

import java.util.Objects;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2;
import com.equinoxfitness.freezeservice.contract.FreezeEmailInput;

@Component
public class FreezeRequestValidator {

	public FreezeServiceExceptionEnum validate(CheckFreezeEligibilityInputV2 checkFreezeEligibilityInputV2) {

		if (Objects.isNull(checkFreezeEligibilityInputV2.getMosoId()) || StringUtils.isEmpty(checkFreezeEligibilityInputV2.getMosoId().trim()))
			return FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED;	
		if (Objects.isNull(checkFreezeEligibilityInputV2.getFreezeReason()) || StringUtils.isEmpty(checkFreezeEligibilityInputV2.getFreezeReason().trim()))
			return FreezeServiceExceptionEnum.FREEZE_REASON_BLANK;	
		if (Objects.isNull(checkFreezeEligibilityInputV2.getSource()) || StringUtils.isEmpty(checkFreezeEligibilityInputV2.getSource().trim()))
			return FreezeServiceExceptionEnum.SOURCE_REQUIRED;
		return null;

	}

	public FreezeServiceExceptionEnum memberFreezeEmailValidate(FreezeEmailInput freezeEmailInput) {
		if (Objects.isNull(freezeEmailInput.getMemberId()) || StringUtils.isEmpty(freezeEmailInput.getMemberId().trim()))
			return FreezeServiceExceptionEnum.MEMBER_ID_REQUIRED;	
		if (Objects.isNull(freezeEmailInput.getFreezeReason()) || StringUtils.isEmpty(freezeEmailInput.getFreezeReason().trim()))
			return FreezeServiceExceptionEnum.FREEZE_REASON_BLANK;	
		if (Objects.isNull(freezeEmailInput.getEmail()) || StringUtils.isEmpty(freezeEmailInput.getEmail().trim()))
			return FreezeServiceExceptionEnum.EMAIL_REQUIRED;
		return null;
	}

}
