package com.equinoxfitness.freezeservice.utils;

public enum EngageFreezeServiceExceptionEnum {
	NO_CODE(0),
	PARTIAL_SUBSIDY_MEMBERSHIP_AGREEMENTS(1),
	PROSPECT_SHORTTERM_COMPLEMENTARY_MEMBERS(2),
	PENDING_CANCELLED_EXPIRED_MEMBERSHIP(3),
	HOTEL_GUEST_MEMBERS(4),
	PIF_MEMBERS(5),
	PAST_DUE_MEMBERS(6),
	ONLY_ONE_REGULAR_FREEZE_PER_CONTRACTUAL_YEAR(7),
	MEMBER_MUST_BE_ACTIVE_FOR_AT_LEAST_THIRTY_DAYS(8),
	PENDING_FREEZE(9),
	ALREADY_REQUESTED_FREEZE(10),
	AMEX_CENTURION(11),
	PENDING_CANCELLED_EXPIRED_ON_HOLD_MEMBERS(12),
	PENDING_FUTURE_CANCELLATION_DATE(13),
	FREEZE_EXTENSION_ERROR(14),
	FREEZE_TOO_EARLY_IN_TENURE(15),
	FREEZE_CONTRACTUAL_YEAR_IN_OBJ(16),
	FREEZE_CONTRACTUAL_YEAR_OUT_OBJ(16);

	private static final String MESSAGE_ID_4_DIGIT = "%04d";
	
	
	private int code;

	private EngageFreezeServiceExceptionEnum(int errorCode) {
		code = errorCode;
	}

	public int getCode() {
		return code;
	}

	public static EngageFreezeServiceExceptionEnum valueOf(int code) {
		EngageFreezeServiceExceptionEnum[] exceptionMessages = values();
		for (EngageFreezeServiceExceptionEnum messages : exceptionMessages) {
			if (messages.getCode() == code)
				return messages;
		}
		return NO_CODE;

	}

	public String getMessageId() {
		return String.format(MESSAGE_ID_4_DIGIT, getCode());
	}

	public void setCode(int code) {
		this.code = code;
	}

}
