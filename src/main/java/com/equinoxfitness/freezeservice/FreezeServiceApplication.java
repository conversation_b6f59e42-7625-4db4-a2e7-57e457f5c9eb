package com.equinoxfitness.freezeservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@ComponentScan("com.equinoxfitness.commons")
@ComponentScan("com.equinoxfitness.events")
@ComponentScan("com.equinoxfitness.salesforce")
@ComponentScan("com.equinoxfitness.redis.service")
@ComponentScan("com.equinoxfitness.emailcommunication.service")
@ComponentScan("com.equinoxfitness.common")
//Need to Comment @EnableDiscoveryClient for the purpose Dockerization of freeze service
//@EnableDiscoveryClient
public class FreezeServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(FreezeServiceApplication.class, args);
	}

	@Bean
	public RestTemplate restTemplate() {
	    return new RestTemplate();
	}
}

