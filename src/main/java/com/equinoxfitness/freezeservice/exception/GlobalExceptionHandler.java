package com.equinoxfitness.freezeservice.exception;

import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.commons.output.BaseResponse;
import com.equinoxfitness.commons.utils.ErrorMessageHandler;

@ControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

	@Autowired
	private ErrorMessageHandler errorMessageHandler;


	@ExceptionHandler(DataFetchException.class)
	protected ResponseEntity<Object> handleDataFetchException(DataFetchException ex) {
		logger.info("DataFetchException");
		ExceptionMessage exceptionMessage = errorMessageHandler.createCustomExceptionMessage(ex.getMessage(), ex.getMessage(), ex.getStatus().value(), ex.getStatus().name());
		return new ResponseEntity<>(this.buildErrorResponse().apply(exceptionMessage),ex.getStatus());
	}	

	private Function<ExceptionMessage, Object> buildErrorResponse() {
		return exceptionMessage -> {
			BaseResponse response=new BaseResponse();
			response.setMessages(new ExceptionMessage[] {exceptionMessage});			
			return response;
		};
	}
}
