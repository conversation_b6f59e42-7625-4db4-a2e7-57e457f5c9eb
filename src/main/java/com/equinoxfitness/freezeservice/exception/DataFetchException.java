package com.equinoxfitness.freezeservice.exception;

import org.springframework.http.HttpStatus;

public class DataFetchException extends RuntimeException {  

	private static final long serialVersionUID = -6534294781658233724L;

	private HttpStatus status;	

	public HttpStatus getStatus() {
		return status;
	}	

	public DataFetchException(String message, HttpStatus status)  
	{    
		super(message);

		this.status=status;
	}  
} 