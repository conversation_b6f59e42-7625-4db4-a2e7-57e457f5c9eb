package com.equinoxfitness.freezeservice;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.boot.test.context.SpringBootTest;

import com.equinoxfitness.commons.redis.RedisKeyGenerator;
import com.equinoxfitness.commons.redis.RedisManager;
import com.equinoxfitness.commons.redis.dto.MosoMemberDataVo;
import com.equinoxfitness.commons.utils.CountryCode;
import com.equinoxfitness.freezeservice.service.MemberRedisService;
import com.equinoxfitness.freezeservice.service.impl.MemberRedisServiceImpl;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ RedisKeyGenerator.class, CountryCode.class })
@SpringBootTest
public class MemberRedisServiceTest {

	@InjectMocks
	MemberRedisService memberRedisService = new MemberRedisServiceImpl();

	@Mock
	RedisManager redisManager;

	@Test
	public void MemberRedisServiceTest_ifMosoMemberNotFoundForMemberKey() {
		PowerMockito.mockStatic(RedisKeyGenerator.class);
		PowerMockito.when(RedisKeyGenerator.generateMemberKey(Mockito.anyString(), Mockito.anyString()))
				.thenReturn("29502653");
		Mockito.when(redisManager.getMosoMemberData(Mockito.anyString())).thenReturn(null);

		MosoMemberDataVo mosoMemberDataVo = memberRedisService.getMosoMemberData("1000243252", 1);
		assertNull(mosoMemberDataVo);
	}

	@Test
	public void MemberRedisServiceTest_validCase() {
		Map<String, String> mosoMemberMap = new HashMap<String, String>();
		mosoMemberMap.put("home_club_code", "101");
		mosoMemberMap.put("moso_id", "100232997");
		mosoMemberMap.put("source_system", "testSourceSystem");

		PowerMockito.mockStatic(RedisKeyGenerator.class);
		PowerMockito.mockStatic(CountryCode.class);
		PowerMockito.when(RedisKeyGenerator.generateMemberKey(Mockito.anyString(), Mockito.anyString()))
				.thenReturn("29502653");
		PowerMockito.when(CountryCode.valueOf(Mockito.anyString())).thenReturn(CountryCode.ca);
		Mockito.when(redisManager.getMosoMemberData(Mockito.anyString())).thenReturn(mosoMemberMap);

		MosoMemberDataVo mosoMemberDataVo = memberRedisService.getMosoMemberData("1000243252", 6);
		assertEquals("100232997", mosoMemberDataVo.getMosoMemberId());
	}
}
