package com.equinoxfitness.freezeservice.contract.moso;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.equinoxfitness.freezeservice.contract.moso.invoice.CreditCardAuthResult;
import com.equinoxfitness.freezeservice.contract.moso.invoice.DiscountCodeSlim;
import com.equinoxfitness.freezeservice.contract.moso.invoice.GiftCardInfo;
import com.equinoxfitness.freezeservice.contract.moso.invoice.Invoice;
import com.equinoxfitness.freezeservice.contract.moso.invoice.InvoiceResult;
import com.equinoxfitness.freezeservice.contract.moso.invoice.Location;
import com.equinoxfitness.freezeservice.contract.moso.invoice.LockerDetails;
import com.equinoxfitness.freezeservice.contract.moso.invoice.MosoMemberDataVo;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewInvoiceItem;
import com.equinoxfitness.freezeservice.contract.moso.invoice.PaymentTender;
import com.equinoxfitness.freezeservice.contract.moso.invoice.RedeemInvoiceResult;
import com.equinoxfitness.freezeservice.contract.moso.invoice.ReturnInvoice;
import com.equinoxfitness.freezeservice.contract.moso.invoice.Transaction;

import static org.junit.Assert.assertEquals;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MosoBeanTest {

	private Location location;
	private Transaction transaction;
	private List<Transaction> transactions;
	private PriceDetails[] prices;
	private DiscountCodeSlim discountCodeSlim;
	private List<DiscountCodeSlim> discountCodes;
	private GiftCardInfo giftCardInfo;
	private LockerDetails lockerDetails;
	private List<PaymentTender> paymentTenders;
	private PaymentTender paymentTender;
	private CreditCardAuthResult creditCardAuthResult;
	private Location locations;

	@Before
	public void setup() {
		location = new Location();
		location.setCode("121");
		location.setName("testLocation");

		transactions = new ArrayList<Transaction>();
		transaction = new Transaction();
		transaction.setAmount("100");
		transaction.setBundleGroupId(3020);
		transaction.setBundleId(111);
		transaction.setComments("testcomments");
		transaction.setDescription("testDescription");
		transaction.setGroupId(002);
		transaction.setId(102);
		transaction.setInvoiceId(20032904);
		transaction.setQuantity(23);
		transaction.setTargetDate("2019-03-07T09:52:59.145Z");
		transaction.setTargetLocation(location);
		transaction.setTransactionItemId(32131);
		transaction.setTransactionType("testTransactionType");
		transaction.setUnitPrice("30");

		transactions.add(transaction);

		prices = new PriceDetails[1];
		prices[0] = new PriceDetails();
		prices[0].setBusinessUnitCode("45364");
		prices[0].setPrice(824);
		prices[0].setSourceId(367224);
		prices[0].setSourceName("testsourceName");
		prices[0].setStartDateEx("testStatdate");

		discountCodeSlim = new DiscountCodeSlim();
		discountCodeSlim.setAmount(new BigDecimal("123321"));
		discountCodeSlim.setBundleId(101);
		discountCodeSlim.setItemID(111);
		discountCodeSlim.setDiscountCodeId(45353);

		discountCodes = new ArrayList<DiscountCodeSlim>();
		discountCodes.add(discountCodeSlim);

		giftCardInfo = new GiftCardInfo();
		giftCardInfo.setAmount(new BigDecimal("236334"));
		giftCardInfo.setExpirationDate("2019-03-07T09:52:59.145Z");
		giftCardInfo.setGiftCardNumber("92320");

		lockerDetails = new LockerDetails();
		lockerDetails.setLockerArea("locker area");
		lockerDetails.setLockerBank("lockerBank");
		lockerDetails.setLockerCategory("lockerCategory");
		lockerDetails.setLockerNumber(*********);
		lockerDetails.setLockerSize("23");

		paymentTenders = new ArrayList<PaymentTender>();
		paymentTender = new PaymentTender();
		creditCardAuthResult = new CreditCardAuthResult();
		creditCardAuthResult.setAccountName("test");

		paymentTender.setAdditionalInfo("testAdditionalInfo");
		paymentTender.setAmount(new BigDecimal("3325452"));
		paymentTender.setClientAccountId(********);
		paymentTender.setClientAccountPaySourceId(********);
		paymentTender.setCreditCardAuthResult(creditCardAuthResult);
		paymentTender.setCreditCardToken("750943kjnvkjdsk34");
		paymentTender.setGiftCardNumber("***********");
		paymentTender.setTenderTypeId(424);

		paymentTenders.add(paymentTender);

		locations = new Location();
		locations.setCode("121");
		locations.setName("testLocation");
	}

	@Test
	public void creditCardAuthResult_getterSetter() {
		CreditCardAuthResult creditCardAuthResult = new CreditCardAuthResult();

		creditCardAuthResult.setAccountName("testAccountName");
		creditCardAuthResult.setAvsResponseCode("avsResponseCode");
		creditCardAuthResult.setCreditCardMask("creditCardMask");
		creditCardAuthResult.setCreditCardToken("creditCardToken");
		creditCardAuthResult.setExpirationDate("2019-03-07T09:52:59.145Z");
		creditCardAuthResult.setPostalCode("323232");
		creditCardAuthResult.setProcessorCode("453525");
		creditCardAuthResult.setReferenceNumber("********");
		creditCardAuthResult.setResponseCode("200");
		creditCardAuthResult.setResponseMessage("responseMessage");
		creditCardAuthResult.setTrackData("trackData");

		Assert.assertEquals("testAccountName", creditCardAuthResult.getAccountName());
		Assert.assertEquals("avsResponseCode", creditCardAuthResult.getAvsResponseCode());
		Assert.assertEquals("creditCardMask", creditCardAuthResult.getCreditCardMask());
		Assert.assertEquals("creditCardToken", creditCardAuthResult.getCreditCardToken());
		Assert.assertEquals("2019-03-07T09:52:59.145Z", creditCardAuthResult.getExpirationDate());
		Assert.assertEquals("323232", creditCardAuthResult.getPostalCode());
		Assert.assertEquals("453525", creditCardAuthResult.getProcessorCode());
		Assert.assertEquals("********", creditCardAuthResult.getReferenceNumber());
		Assert.assertEquals("200", creditCardAuthResult.getResponseCode());
		Assert.assertEquals("responseMessage", creditCardAuthResult.getResponseMessage());
		Assert.assertEquals("trackData", creditCardAuthResult.getTrackData());
	}

	@Test
	public void forAgreementTest_getterSetter() {

		ForAgreement forAgreement = new ForAgreement();
		forAgreement.setAgreementID(********);
		forAgreement.setAgreementName("testagreementName");

		assertEquals(********, forAgreement.getAgreementID(), 0);
		assertEquals("testagreementName", forAgreement.getAgreementName());
	}

	@Test
	public void glCodeInfoTest_getterSetter() {
		GlCodeInfo glCodeInfo = new GlCodeInfo();
		glCodeInfo.setCode("32424");
		glCodeInfo.setDescription("testdescription");
		glCodeInfo.setId(165);

		assertEquals("32424", glCodeInfo.getCode());
		assertEquals("testdescription", glCodeInfo.getDescription());
		assertEquals(165, glCodeInfo.getId(), 0);
	}

	@Test
	public void invoiceTest_getterSetter() {
		Invoice invoice = new Invoice();

		invoice.setBalance(new BigDecimal("2324543"));
		invoice.setClientAccountId(********);
		invoice.setComments("testComments");
		invoice.setId(4353);
		invoice.setInvoiceStatus("testinvoiceStatus");
		invoice.setMessage("testMessage");
		invoice.setPartyRoleId(537982);
		invoice.setPaymentDueDate("2019-03-07T09:52:59.145Z");
		invoice.setTargetDate("2019-03-08T09:52:59.145Z");
		invoice.setTargetLocation(location);
		invoice.setTaxExemptId(********);
		invoice.setTotal("145541");
		invoice.setTransactions(transactions);
		invoice.setTaxExemptionName("testTaxExemptionName");

		Assert.assertEquals(new BigDecimal("2324543"), invoice.getBalance());
		Assert.assertEquals(********, invoice.getClientAccountId(), 0);
		Assert.assertEquals("testComments", invoice.getComments());
		Assert.assertEquals(4353, invoice.getId(), 0);
		Assert.assertEquals("testinvoiceStatus", invoice.getInvoiceStatus());
		Assert.assertEquals("testMessage", invoice.getMessage());
		Assert.assertEquals(537982, invoice.getPartyRoleId(), 0);
		Assert.assertEquals("2019-03-07T09:52:59.145Z", invoice.getPaymentDueDate());
		Assert.assertEquals("2019-03-08T09:52:59.145Z", invoice.getTargetDate());
		Assert.assertEquals("121", invoice.getTargetLocation().getCode());
		Assert.assertEquals(********, invoice.getTaxExemptId(), 0);
		Assert.assertEquals("145541", invoice.getTotal());
		Assert.assertEquals("100", invoice.getTransactions().get(0).getAmount());
		Assert.assertEquals("testTaxExemptionName", invoice.getTaxExemptionName());
	}

	@Test
	public void itemTest_getterSetter() {

		Item item = new Item();
		item.setCode("4535");
		item.setId(21312);
		item.setMessage("testMessage");
		item.setName("testName");
		item.setPrices(prices);

		assertEquals("4535", item.getCode());
		assertEquals(21312, item.getId(), 0);
		assertEquals("testMessage", item.getMessage());
		assertEquals("testName", item.getName());
		assertEquals("45364", item.getPrices()[0].getBusinessUnitCode());
		assertEquals(824, item.getPrices()[0].getPrice());
		assertEquals(367224, item.getPrices()[0].getSourceId(), 0);
		assertEquals("testsourceName", item.getPrices()[0].getSourceName());
		assertEquals("testStatdate", item.getPrices()[0].getStartDateEx());
	}

	@Test
	public void lockerDetailsTestt_getterSetter() {
		LockerDetails lockerDetails = new LockerDetails();
		lockerDetails.setLockerArea("lockerArea");
		lockerDetails.setLockerBank("lockerBank");
		lockerDetails.setLockerCategory("lockerCategory");
		lockerDetails.setLockerNumber(********);
		lockerDetails.setLockerSize("32");

		Assert.assertEquals("lockerArea", lockerDetails.getLockerArea());
		Assert.assertEquals("lockerBank", lockerDetails.getLockerBank());
		Assert.assertEquals("lockerCategory", lockerDetails.getLockerCategory());
		Assert.assertEquals(********, lockerDetails.getLockerNumber(), 0);
		Assert.assertEquals("32", lockerDetails.getLockerSize());
	}

	@Test
	public void mosoApiExceptionResponseTest_getterSetter() {
		MosoApiExceptionResponse mosoApiExceptionResponse = new MosoApiExceptionResponse();
		mosoApiExceptionResponse.setData("testData");
		mosoApiExceptionResponse.setMessage("testmessage");

		assertEquals("testData", mosoApiExceptionResponse.getData());
		assertEquals("testmessage", mosoApiExceptionResponse.getMessage());
	}

	@Test
	public void mosoMemberDataVoTest_getterSetter() {

		MosoMemberDataVo mosoMemberDataVo = new MosoMemberDataVo();

		mosoMemberDataVo.setCountryCode("5");
		mosoMemberDataVo.setHomeFacilityId("121");
		mosoMemberDataVo.setMosoMemberId("********");

		Assert.assertEquals("5", mosoMemberDataVo.getCountryCode());
		Assert.assertEquals("121", mosoMemberDataVo.getHomeFacilityId());
		Assert.assertEquals("********", mosoMemberDataVo.getMosoMemberId());
		Assert.assertEquals("MosoMemberDataOutput{memberId='********', homeFacilityId='121', countryCode=5'}",
				mosoMemberDataVo.toString());
	}

	@Test
	public void newInvoiceItemTest_getterSetter() {
		NewInvoiceItem newInvoiceItem = new NewInvoiceItem();
		newInvoiceItem.setComments("testComments");
		newInvoiceItem.setDiscountCodes(discountCodes);
		newInvoiceItem.setGiftCard(giftCardInfo);
		newInvoiceItem.setIsItem(true);
		newInvoiceItem.setLockerDetails(lockerDetails);
		newInvoiceItem.setPrice(new BigDecimal("3235432"));
		newInvoiceItem.setQuantity(3);
		newInvoiceItem.setSalesPersonId("1331");

		Assert.assertEquals("testComments", newInvoiceItem.getComments());
		Assert.assertEquals(new BigDecimal("123321"), newInvoiceItem.getDiscountCodes().get(0).getAmount());
		Assert.assertEquals("92320", newInvoiceItem.getGiftCard().getGiftCardNumber());
		Assert.assertEquals(true, newInvoiceItem.getIsItem());
		Assert.assertEquals("locker area", newInvoiceItem.getLockerDetails().getLockerArea());
		Assert.assertEquals(new BigDecimal("3235432"), newInvoiceItem.getPrice());
		Assert.assertEquals(3, newInvoiceItem.getQuantity(), 0);
		Assert.assertEquals("1331", newInvoiceItem.getSalesPersonId());
		Assert.assertEquals(new BigDecimal("236334"), giftCardInfo.getAmount());
		Assert.assertEquals("2019-03-07T09:52:59.145Z", giftCardInfo.getExpirationDate());
		Assert.assertEquals(45353, discountCodeSlim.getDiscountCodeId(), 0);
		Assert.assertEquals(111, discountCodeSlim.getItemID(), 0);
		Assert.assertEquals(101, discountCodeSlim.getBundleId(), 0);
	}

	@Test
	public void noteTest_getterSetter() {
		Note note = new Note();
		note.setCheckinAlert(true);
		note.setContactTypeId(23425426);
		note.setContent("testContent");
		note.setErrorMessage("testerrorMessage");
		note.setId("101");
		note.setMessage("testMessage");
		note.setNoteTypeId(123432);

		assertEquals(true, note.isCheckinAlert());
		assertEquals(23425426, note.getContactTypeId(), 0);
		assertEquals("testContent", note.getContent());
		assertEquals("testerrorMessage", note.getErrorMessage());
		assertEquals("101", note.getId());
		assertEquals("testMessage", note.getMessage());
		assertEquals(123432, note.getNoteTypeId(), 0);
	}

	@Test
	public void redeemInvoiceResult_getterSetter() {
		RedeemInvoiceResult redeemInvoiceResult = new RedeemInvoiceResult();
		redeemInvoiceResult.setInvoiceId("235345343");
		assertEquals("235345343", redeemInvoiceResult.getInvoiceId());

		InvoiceResult invoiceResult = new InvoiceResult();

		invoiceResult.setCorrelationId("98439u4rhf934j9");
		assertEquals("98439u4rhf934j9", invoiceResult.getCorrelationId());
	}

	@Test
	public void returnInvoiceTest_getterSetter() {
		ReturnInvoice returnInvoice = new ReturnInvoice();

		returnInvoice.setCancellationReasonComment("testCancellationreason");
		returnInvoice.setCancellationReasonID(32432);
		returnInvoice.setPayments(paymentTenders);

		Assert.assertEquals("testCancellationreason", returnInvoice.getCancellationReasonComment());
		Assert.assertEquals(32432, returnInvoice.getCancellationReasonID(), 0);
		Assert.assertEquals("testAdditionalInfo", returnInvoice.getPayments().get(0).getAdditionalInfo());
	}

	@Test
	public void suspensionResponseTest_getterSetter() {
		SuspensionResponse suspensionResponse = new SuspensionResponse();
		suspensionResponse.setSuspensionReasonName("Regular");
		suspensionResponse.setFreezeRecurringFeeItem("freezeRecurringFeeItem");
		suspensionResponse.setFreezeRecurringFeeAmount(645);
		suspensionResponse.setFreezeRecurringFeeStartDate("test");

		assertEquals("Regular", suspensionResponse.getSuspensionReasonName());
		assertEquals("freezeRecurringFeeItem", suspensionResponse.getFreezeRecurringFeeItem());
		assertEquals(645, suspensionResponse.getFreezeRecurringFeeAmount());
		assertEquals("test", suspensionResponse.getFreezeRecurringFeeStartDate());
	}

	@Test
	public void taxRateTest_getterSetter() {

		TaxRate taxRate = new TaxRate();
		TaxGroup taxGroup = new TaxGroup();
		taxGroup.setId(342);

		taxRate.setId(342432);
		taxRate.setRate(42525436);
		taxRate.setTaxGroup(taxGroup);

		assertEquals(342432, taxRate.getId(), 0);
		assertEquals(42525436, taxRate.getRate());
		assertEquals(342, taxRate.getTaxGroup().getId(), 0);
	}

	@Test
	public void transactionTest_getterSetter() {
		Transaction transaction = new Transaction();
		transaction.setAmount("100");
		transaction.setBundleGroupId(3020);
		transaction.setBundleId(111);
		transaction.setComments("testcomments");
		transaction.setDescription("testDescription");
		transaction.setGroupId(002);
		transaction.setId(102);
		transaction.setInvoiceId(20032904);
		transaction.setQuantity(23);
		transaction.setTargetDate("2019-03-07T09:52:59.145Z");
		transaction.setTargetLocation(location);
		transaction.setTransactionItemId(32131);
		transaction.setTransactionType("testTransactionType");
		transaction.setUnitPrice("30");

		Assert.assertEquals("100", transaction.getAmount());
		Assert.assertEquals(3020, transaction.getBundleGroupId(), 0);
		Assert.assertEquals(111, transaction.getBundleId(), 0);
		Assert.assertEquals("testcomments", transaction.getComments());
		Assert.assertEquals("testDescription", transaction.getDescription());
		Assert.assertEquals(002, transaction.getGroupId(), 0);
		Assert.assertEquals(102, transaction.getId(), 0);
		Assert.assertEquals(20032904, transaction.getInvoiceId(), 0);
		Assert.assertEquals(23, transaction.getQuantity(), 0);
		Assert.assertEquals("2019-03-07T09:52:59.145Z", transaction.getTargetDate());
		Assert.assertEquals("121", transaction.getTargetLocation().getCode());
		Assert.assertEquals(32131, transaction.getTransactionItemId(), 0);
		Assert.assertEquals("testTransactionType", transaction.getTransactionType());
		Assert.assertEquals("30", transaction.getUnitPrice());
		Assert.assertEquals("testLocation", locations.getName());
	}
}
