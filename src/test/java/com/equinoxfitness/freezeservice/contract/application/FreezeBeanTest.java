package com.equinoxfitness.freezeservice.contract.application;

import static org.junit.Assert.*;

import java.math.BigDecimal;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.equinoxfitness.freezeservice.contract.CreditCard;
import com.equinoxfitness.freezeservice.contract.CreditCardTransaction;
import com.equinoxfitness.freezeservice.contract.EligibilityLackReason;
import com.equinoxfitness.freezeservice.contract.TransactionTender;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeBeanTest {

	private CreditCardTransaction creditCardTransaction;
	private CreditCard creditCard;

	@Before
	public void setup() {
		creditCardTransaction = new CreditCardTransaction();
		creditCard = new CreditCard();
		creditCard.setCreditCardExpiryDate("testExpiryDate");
		creditCard.setCreditCardNumber("100435897389492");
		creditCard.setCreditCardType(3);
		creditCard.setCvvNumber("325");
		creditCard.setDebitCard(false);
		creditCard.setLastFourCreditCardDigits("7455");
		creditCard.setNameOnCard("testNameOnCard");

		creditCardTransaction.setAuthorizationAmount(new BigDecimal("35252542"));
		creditCardTransaction.setAuthorizationCode("r435363");
		creditCardTransaction.setCreditCard(creditCard);
		creditCardTransaction.setIsCreditCardPresent("true");
	}

	@Test
	public void transactionTenderTest_getterSetter() {
		TransactionTender transactionTender = new TransactionTender();
		transactionTender.setAdditionalInfo("additionalInfo");
		transactionTender.setCreditCardTransaction(creditCardTransaction);
		transactionTender.setTenderAmount(new BigDecimal("553636"));
		transactionTender.setTenderTypeID(2);

		assertEquals("additionalInfo", transactionTender.getAdditionalInfo());
		assertEquals(new BigDecimal("35252542"), transactionTender.getCreditCardTransaction().getAuthorizationAmount());
		assertEquals("r435363", transactionTender.getCreditCardTransaction().getAuthorizationCode());
		assertEquals("true", transactionTender.getCreditCardTransaction().getIsCreditCardPresent());

		assertEquals("testExpiryDate",
				transactionTender.getCreditCardTransaction().getCreditCard().getCreditCardExpiryDate());
		assertEquals("100435897389492",
				transactionTender.getCreditCardTransaction().getCreditCard().getCreditCardNumber());
		assertEquals(3, transactionTender.getCreditCardTransaction().getCreditCard().getCreditCardType(), 0);
		assertEquals("325", transactionTender.getCreditCardTransaction().getCreditCard().getCvvNumber());
		assertEquals(false, transactionTender.getCreditCardTransaction().getCreditCard().isDebitCard());
		assertEquals("7455",
				transactionTender.getCreditCardTransaction().getCreditCard().getLastFourCreditCardDigits());
		assertEquals("testNameOnCard", transactionTender.getCreditCardTransaction().getCreditCard().getNameOnCard());

		assertEquals(new BigDecimal("553636"), transactionTender.getTenderAmount());
		assertEquals(2, transactionTender.getTenderTypeID(), 0);
	}

	@Test
	public void eligibilityLackReason_getterSetter() {
		EligibilityLackReason eligibilityLackReason = new EligibilityLackReason();
		eligibilityLackReason.setByPassable(true);
		eligibilityLackReason.setReasonMessage("reasonMessage");

		assertEquals(true, eligibilityLackReason.isByPassable());
		assertEquals("reasonMessage", eligibilityLackReason.getReasonMessage());
	}
}
