package com.equinoxfitness.freezeservice.contract.sfdc;

import static org.junit.Assert.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SFDCBeanTest {

	private List<ClubRecord> clubrecords;
	private ClubRecord clubRecord;
	private Attributes attributes;
	private List<Record> records;
	private Record record;
	private Contact__r contact__r;

	@Before
	public void setup() {
		clubrecords = new ArrayList<ClubRecord>();
		clubRecord = new ClubRecord();
		clubRecord.setId("232");
		clubrecords.add(clubRecord);

		attributes = new Attributes();
		attributes.setType("testType");
		attributes.setUrl("testURL");

		records = new ArrayList<Record>();
		record = new Record();
		contact__r = new Contact__r();
		contact__r.setAttributes(attributes);
		contact__r.setId("114");
		record.setAttributes(attributes);
		records.add(record);
	}

	@Test
	public void authTokenDataTest_getterSetter() {
		AuthTokenData authTokenData = new AuthTokenData();
		authTokenData.setAccessToken("accessToken");
		authTokenData.setId("id");
		authTokenData.setInstanceUrl("instanceUrl");
		authTokenData.setIssuedAt("issuedAt");
		authTokenData.setSignature("signature");
		authTokenData.setTokenType("tokenType");

		assertEquals("accessToken", authTokenData.getAccessToken());
		assertEquals("id", authTokenData.getId());
		assertEquals("instanceUrl", authTokenData.getInstanceUrl());
		assertEquals("issuedAt", authTokenData.getIssuedAt());
		assertEquals("signature", authTokenData.getSignature());
		assertEquals("tokenType", authTokenData.getTokenType());
		System.out.println("sa" + authTokenData.toString());
	}

	@Test
	public void caseTest_getterSetter() throws ParseException {
		Case caseObj = new Case();
		caseObj.setCategories__c("categories__c");
		caseObj.setClub("testclub");
		caseObj.setContactId("o0343fdkdsd");
		caseObj.setDescription("testdescription");
		caseObj.setDoNotContact(true);
		caseObj.setDuration("2");
		caseObj.setFeedbackSource("testfeedbackSource");

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		Date freezeEndDate = formatter.parse("2019-03-07T09:52:59.145Z");
		caseObj.setFreezeEndDate(freezeEndDate);

		caseObj.setFreezeFee(30.0);
		caseObj.setFreezeReason("regular");

		Date freezeStartDate = formatter.parse("2019-03-01T09:52:59.145Z");
		caseObj.setFreezeStartDate(freezeStartDate);

		caseObj.setId("1121");
		caseObj.setIsException(false);
		caseObj.setMemberId("100024322");
		caseObj.setOrigin("testOrigin");
		caseObj.setReason("testreason");
		caseObj.setRecordTypeId("testrecordTypeId");
		caseObj.setRegionlaManagerEmail("<EMAIL>");
		caseObj.setStatus("Active");
		caseObj.setSubCategories("testsubCategories");
		caseObj.setSubject("subject");

		assertEquals("categories__c", caseObj.getCategories__c());
		assertEquals("testclub", caseObj.getClub());
		assertEquals("o0343fdkdsd", caseObj.getContactId());
		assertEquals("testdescription", caseObj.getDescription());
		assertEquals(true, caseObj.isDoNotContact());
		assertEquals("2", caseObj.getDuration());
		assertEquals("testfeedbackSource", caseObj.getFeedbackSource());
		assertEquals(freezeEndDate, caseObj.getFreezeEndDate());
		assertEquals(30.0, caseObj.getFreezeFee(), 0.0);
		assertEquals("regular", caseObj.getFreezeReason());
		assertEquals(freezeStartDate, caseObj.getFreezeStartDate());
		assertEquals("1121", caseObj.getId());

		assertEquals(false, caseObj.isException);
		assertEquals("100024322", caseObj.getMemberId());
		assertEquals("testOrigin", caseObj.getOrigin());
		assertEquals("testreason", caseObj.getReason());
		assertEquals("testrecordTypeId", caseObj.getRecordTypeId());
		assertEquals("<EMAIL>", caseObj.getRegionlaManagerEmail());
		assertEquals("Active", caseObj.getStatus());
		assertEquals("testsubCategories", caseObj.getSubCategories());
		assertEquals("subject", caseObj.getSubject());
		assertEquals(false, caseObj.getIsException());
	}

	@Test
	public void clubData_getterSetter() {
		ClubData clubData = new ClubData();
		clubData.setDone(true);
		clubData.setRecords(clubrecords);
		clubData.setTotalSize(323);

		assertEquals(true, clubData.getDone());
		assertEquals("232", clubData.getRecords().get(0).getId());
		assertEquals(323, clubData.getTotalSize(), 0);
	}

	@Test
	public void clubRecordTest_getterSetter() {
		ClubRecord clubRecord = new ClubRecord();
		clubRecord.setAttributes(attributes);
		clubRecord.setId("234");

		assertEquals("testType", clubRecord.getAttributes().getType());
		assertEquals("234", clubRecord.getId());
	}

	@Test
	public void contactDataTest_getterSetter() {
		ContactData contactData = new ContactData();
		contactData.setDone(true);
		contactData.setRecords(records);
		contactData.setTotalSize(121);

		assertEquals(true, contactData.getDone());
		assertEquals("testType", contactData.getRecords().get(0).getAttributes().getType());
		assertEquals("testURL", contactData.getRecords().get(0).getAttributes().getUrl());

		assertEquals("testType", contactData.getRecords().get(0).getAttributes().getType());
		//assertEquals("114", contactData.getRecords().get(0).getId());
		assertEquals(121, contactData.getTotalSize(), 0);
	}

	@Test
	public void createCaseInput_getterSetter() {
		CreateCaseInput createCaseInput = new CreateCaseInput();

		Case[] cases = new Case[1];
		cases[0] = new Case();
		cases[0].setCategories__c("categories__c");
		createCaseInput.setCases(cases);

		assertEquals("categories__c", createCaseInput.getCases()[0].getCategories__c());
	}

	@Test
	public void createCaseResponseTest_getterSetter() {
		CreateCaseResponse response = new CreateCaseResponse();
		List<Object> errors = new ArrayList<Object>();
		errors.add("testData");

		response.setErrors(errors);
		response.setId("testId");
		response.setSuccess(true);

		assertEquals("testData", response.getErrors().get(0));
		assertEquals("testId", response.getId());
		assertEquals(true, response.getSuccess());
	}
}
