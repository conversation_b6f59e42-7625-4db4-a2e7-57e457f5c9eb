package com.equinoxfitness.freezeservice.cucumbercontroller.steps;

import static org.junit.Assert.assertEquals;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import com.equinoxfitness.commons.output.Version;
import com.equinoxfitness.freezeservice.cucumbercontroller.CucumberRoot;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;

public class GetVersionController extends CucumberRoot {

	private ResponseEntity<Version> responseEntity;

	@When("^client makes call to \"([^\"]*)\"$")
	public void client_makes_call_to(String arg1) throws Throwable {
		responseEntity = template.getForEntity("/v1/version", Version.class);
	}

	@Then("^the output of getVersion is success$")
	public void the_output_of_getVersion_is_success() throws Throwable {
		assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
	}
}
