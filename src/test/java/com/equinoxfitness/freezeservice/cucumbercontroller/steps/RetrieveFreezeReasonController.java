package com.equinoxfitness.freezeservice.cucumbercontroller.steps;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput;
import com.equinoxfitness.freezeservice.cucumbercontroller.CucumberRoot;
import cucumber.api.java.en.Given;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;

public class RetrieveFreezeReasonController extends CucumberRoot {

	private ResponseEntity<RetrieveFreezeReasonOutput> responseEntity;
	private String mosoMemberId;
	private String freezeReason;
	private int duration;

	@Given("^The input mosoMemberId as \"([^\"]*)\"$")
	public void the_input_mosoMemberId_as(String arg1) throws Throwable {
		mosoMemberId = arg1;
	}

	@Given("^The input mosoMemberId as null value$")
	public void the_input_mosoMemberId_as_null_value() throws Throwable {
		mosoMemberId = " ";
	}

	@Given("^the input duration as (\\d+)$")
	public void the_input_duration_as(int arg1) throws Throwable {
		duration = arg1;
	}

	@Given("^the freezeReason with the value \"([^\"]*)\"$")
	public void the_freezeReason_with_the_value(String arg1) throws Throwable {
		freezeReason = arg1;
	}

	@Given("^the freezeReason having null value$")
	public void the_freezeReason_having_null_value() throws Throwable {
		freezeReason = " ";
	}

	@Given("^The input duration having null value$")
	public void the_input_duration_having_null_value() throws Throwable {
		duration = 0;
	}

	@When("^client makes call to GET \"([^\"]*)\"$")
	public void client_makes_call_to_GET(String arg1) throws Throwable {
		String url = "/v1/freeze-info/" + mosoMemberId + "/?freezeReason=" + freezeReason + "&duration=" + duration;
		responseEntity = template.getForEntity(url, RetrieveFreezeReasonOutput.class);
	}

	@Then("^the output RetrieveFreezeReasonOutput is success$")
	public void the_output_RetrieveFreezeReasonOutput_is_success() throws Throwable {
		assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
	}

	@Then("^the output RetrieveFreezeReasonOutput is not success$")
	public void the_output_RetrieveFreezeReasonOutput_is_not_success() throws Throwable {
		assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
	}

	@Then("^RetrieveFreezeReasonOutput error message is (.*)$")
	public void retrievefreezereasonoutput_error_message_is_MEMBER_ID_REQUIRED(String errorMessage) throws Throwable {
		ExceptionMessage[] exceptionMessages = responseEntity.getBody().getMessages();
		assertTrue(exceptionMessages.length > 0);
		assertEquals(errorMessage, exceptionMessages[0].getErrorMessage());
	}
}
