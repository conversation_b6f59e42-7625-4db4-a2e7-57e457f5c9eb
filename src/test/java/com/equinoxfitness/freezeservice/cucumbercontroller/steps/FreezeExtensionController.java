package com.equinoxfitness.freezeservice.cucumbercontroller.steps;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput;
import com.equinoxfitness.freezeservice.cucumbercontroller.CucumberRoot;

import cucumber.api.java.en.Given;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;

public class FreezeExtensionController extends CucumberRoot {

	private ResponseEntity<FreezeExtensionOutput> responseEntity;
	String memberId;
	String facilityId;
	String freezeReason;
	Date freezeExtensionEndDate;
	int extensionDurationMonths;
	boolean waiveOffExtensionFee;
	String notesType;
	String notes;
	String contactType;

	@Given("^the input memberId  as \"([^\"]*)\"$")
	public void the_input_memberId_as(String memberId) throws Throwable {
		this.memberId = memberId;
	}

	@Given("^the input facilityId  as \"([^\"]*)\"$")
	public void the_input_facilityId_as(String facilityId) throws Throwable {
		this.facilityId = facilityId;
	}

	@Given("^the input freezeReason as (\\d+)$")
	public void the_input_freezeReason_as(String freezeReason) throws Throwable {
		this.freezeReason = freezeReason;
	}

	@Given("^the input freezeExtensionEndDate as \"([^\"]*)\"$")
	public void the_input_freezeExtensionEndDate_as(String freezeExtensionEndDate) throws Throwable {
		if (!freezeExtensionEndDate.isEmpty()) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
			String dateInString = freezeExtensionEndDate;
			Date date = formatter.parse(dateInString);
			this.freezeExtensionEndDate = date;
		}
	}

	@Given("^the input extensionDurationMonths as \"([^\"]*)\"$")
	public void the_input_extensionDurationMonths_as(String extensionDurationMonths) throws Throwable {
		if (!extensionDurationMonths.isEmpty())
			this.extensionDurationMonths = Integer.parseInt(extensionDurationMonths);
	}

	@Given("^the input waiveOffExtensionFee as \"([^\"]*)\"$")
	public void the_input_waiveOffExtensionFee_as(String waiveOffExtensionFee) throws Throwable {
		this.waiveOffExtensionFee = Boolean.parseBoolean(waiveOffExtensionFee);
	}

	@Given("^the input notesType as \"([^\"]*)\"$")
	public void the_input_notesType_as(String notesType) throws Throwable {
		this.notesType = notesType;
	}

	@Given("^the input notes as \"([^\"]*)\"$")
	public void the_input_notes_as(String notes) throws Throwable {
		this.notes = notes;
	}

	@Given("^the input contactType as \"([^\"]*)\"$")
	public void the_input_contactType_as(String contactType) throws Throwable {
		this.contactType = contactType;
	}

	@When("^client makes call to POST v(\\d+)/freeze-extension/$")
	public void client_makes_call_to_POST_v_freeze_extension(int arg1) throws Throwable {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);

		Map<String, Object> freezeExtensionMap = new HashMap<>();
		freezeExtensionMap.put("memberId", memberId);
		freezeExtensionMap.put("facilityId", facilityId);
		freezeExtensionMap.put("freezeReason", freezeReason);
		freezeExtensionMap.put("freezeExtensionEndDate", freezeExtensionEndDate);
		freezeExtensionMap.put("extensionDurationMonths", String.valueOf(extensionDurationMonths));
		freezeExtensionMap.put("waiveOffExtensionFee", waiveOffExtensionFee);
		freezeExtensionMap.put("notesType", notesType);
		freezeExtensionMap.put("notes", notes);
		freezeExtensionMap.put("contactType", contactType);

		HttpEntity<?> entity = new HttpEntity<>(freezeExtensionMap, headers);
		responseEntity = template.exchange("/v1/freeze-extension/", HttpMethod.POST, entity,
				FreezeExtensionOutput.class);
	}

	@Then("^the client receives status failure$")
	public void the_client_receives_status_failure() throws Throwable {
		Assert.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
	}

	@Then("^the output FreezeExtensionOutput is success$")
	public void the_output_RetrieveFreezeReasonOutput_is_success() throws Throwable {
		assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
	}

	@Then("^FreezeExtensionOutput error message is (.*)$")
	public void freezeextensionoutput_error_message_is_MEMBER_ID_REQUIRED(String errorMessage) throws Throwable {
		ExceptionMessage[] exceptionMessages = responseEntity.getBody().getMessages();
		assertTrue(exceptionMessages.length > 0);
		assertEquals(errorMessage, exceptionMessages[0].getErrorMessage());
	}
}
