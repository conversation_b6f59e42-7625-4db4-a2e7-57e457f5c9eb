/**
 * 
 */
package com.equinoxfitness.freezeservice.cucumbercontroller.steps;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput;
import com.equinoxfitness.freezeservice.cucumbercontroller.CucumberRoot;

import cucumber.api.java.en.Given;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;

/**
 * <AUTHOR>
 *
 */
public class FreezeEligibilityController extends CucumberRoot {

	private ResponseEntity<CheckFreezeEligiblityOutput> responseEntity;

	String memberId;
	String freezeReason;
	Date reuestedDate;
	Date startDate;
	Date endDate;
	int durationMonths;
	int durationDays;

	@Given("^the input memberId as \"([^\"]*)\"$")
	public void the_input_memberId_as(String memberId) throws Throwable {
		this.memberId = memberId;
	}

	@Given("^the input durationDays as (\\d+)$")
	public void the_input_durationDays_as(int durationDays) throws Throwable {
		this.durationDays = durationDays;
	}

	@Given("^the input durationMonths as (\\d+)$")
	public void the_input_durationMonths_as(int durationMonths) throws Throwable {
		this.durationMonths = durationMonths;
	}

	@Given("^the input endDate as \"([^\"]*)\"$")
	public void the_input_endDate_as(String endDate) throws Throwable {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		String dateInString = endDate;
		Date date = formatter.parse(dateInString);
		this.endDate = date;
	}

	@Given("^the input freezeReason as \"([^\"]*)\"$")
	public void the_input_freezeReason_as(String freezeReason) throws Throwable {
		this.freezeReason = freezeReason;
	}

	@Given("^the input requestedDate as \"([^\"]*)\"$")
	public void the_input_requestedDate_as(String requestedDate) throws Throwable {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		String dateInString = requestedDate;
		Date date = formatter.parse(dateInString);
		this.reuestedDate = date;
	}

	@Given("^the input startDate as \"([^\"]*)\"$")
	public void the_input_startDate_as(String startDate) throws Throwable {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		String dateInString = startDate;
		Date date = formatter.parse(dateInString);
		this.startDate = date;
	}

	@When("^client makes call to POST v(\\d+)/freeze-eligible$")
	public void client_makes_call_to_POST_v_freeze_eligible(int arg1) throws Throwable {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);

		Map<String, Object> freezeEligibleMap = new HashMap<>();
		freezeEligibleMap.put("memberId", memberId);
		freezeEligibleMap.put("freezeReason", freezeReason);
		freezeEligibleMap.put("durationMonths", String.valueOf(durationMonths));
		freezeEligibleMap.put("durationDays", String.valueOf(durationDays));
		freezeEligibleMap.put("endDate", endDate);
		freezeEligibleMap.put("requestedDate", reuestedDate);
		freezeEligibleMap.put("startDate", startDate);

		HttpEntity<?> entity = new HttpEntity<>(freezeEligibleMap, headers);
		responseEntity = template.exchange("/v1/freeze-eligible", HttpMethod.POST, entity,
				CheckFreezeEligiblityOutput.class);
	}

	@Then("^the output FreezeEligibilityOutput is success$")
	public void the_output_RetrieveFreezeReasonOutput_is_success() throws Throwable {
		assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
	}

	@Then("^the client receives status as failure$")
	public void the_client_receives_status_as_failure() throws Throwable {
		Assert.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
	}

	@Then("^FreezeEligibilityOutput error message is (.*)$")
	public void output_is_CheckFreezeEligiblityOutput(String errorMessage) throws Throwable {
		ExceptionMessage[] exceptionMessages = responseEntity.getBody().getMessages();
		assertTrue(exceptionMessages.length > 0);
		assertEquals(errorMessage, exceptionMessages[0].getErrorMessage());
	}
}
