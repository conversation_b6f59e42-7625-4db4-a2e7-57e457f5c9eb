package com.equinoxfitness.freezeservice.cucumbercontroller.steps;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.freezeservice.contract.FreezeMemberResponse;
import com.equinoxfitness.freezeservice.cucumbercontroller.CucumberRoot;
import cucumber.api.java.en.Given;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;

public class FreezeMembershipController extends CucumberRoot {
	private ResponseEntity<FreezeMemberResponse> responseEntity;
	SimpleDateFormat dateFormat = new SimpleDateFormat();

	private String memberId;
	private String freezeReasonId;
	private int durationMonths;
	private String startDate;
	private String endDate;
	private String source;
	private String FacilityId;
	private String skipValidations;

	@Given("^The input MemberId as (.*)$")
	public void the_input_MemberId_as(String memberId) throws Throwable {
		this.memberId = memberId;
	}

	@Given("^the FreezeReasonId with the value (.*)$")
	public void the_FreezeReasonId_with_the_value_Regular(String freezeReasonId) throws Throwable {
		this.freezeReasonId = freezeReasonId;
	}

	@Given("^the input DurationMonths as (.*)$")
	public void the_input_DurationMonths_as(int durationMonths) throws Throwable {
		this.durationMonths = durationMonths;
	}

	@Given("^the input StartDate as (.*)$")
	public void the_input_StartDate_as_T_Z(String startDate) throws Throwable {
		this.startDate = startDate;
	}

	@Given("^the input EndDate as (.*)$")
	public void the_input_EndDate_as_T_Z(String endDate) throws Throwable {
		this.endDate = endDate;
	}

	@Given("^the input Source as(.*)$")
	public void the_input_Source_as_Web(String source) throws Throwable {
		this.source = source;
	}

	@Given("^the input FacilityId as(.*)$")
	public void the_input_FacilityId_as_FacilityId(String FacilityId) throws Throwable {
		this.FacilityId = FacilityId;
	}

	@Given("^thie input isSkipValidations as (.*)$")
	public void thie_input_isSkipValidations_as_true(String isSkipValidations) throws Throwable {
		skipValidations = isSkipValidations;
	}

	@Given("^The input MemberId is having null value$")
	public void the_input_MemberId_is_having_null_value() throws Throwable {
		memberId = null;
	}

	@Given("^the FreezeReasonId is having null value$")
	public void the_FreezeReasonId_is_having_null_value() throws Throwable {
		freezeReasonId = null;
	}

	@Given("^the input DurationMonths having zero or less than zero$")
	public void the_input_DurationMonths_having_zero_or_less_than_zero() throws Throwable {
		durationMonths = 0;
	}

	@Given("^the input StartDate having null value$")
	public void the_input_StartDate_having_null_value() throws Throwable {
		startDate = null;
	}

	@Given("^the input EndDate having null value$")
	public void the_input_EndDate_having_null_value() throws Throwable {
		endDate = null;
	}

	@When("^client makes call to POST /v1/freeze-membership/$")
	public void client_makes_call_to_POST_v_freeze_membership() throws Throwable {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);

		Map<Object, Object> memberIdVariables = new HashMap<>();
		memberIdVariables.put("memberId", memberId);
		memberIdVariables.put("freezeReasonId", freezeReasonId);
		memberIdVariables.put("durationMonths", durationMonths);
		memberIdVariables.put("startDate", startDate);
		memberIdVariables.put("endDate", endDate);
		memberIdVariables.put("source", source);
		memberIdVariables.put("FacilityId", FacilityId);
		memberIdVariables.put("skipValidations", skipValidations);

		HttpEntity<?> entity = new HttpEntity<>(memberIdVariables, headers);
		responseEntity = template.exchange("/v1/freeze-membership/", HttpMethod.POST, entity,
				FreezeMemberResponse.class);
	}

	@Then("^the output FreezeMemberResponse is success$")
	public void the_output_FreezeMemberResponse_is_success() throws Throwable {
		assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
	}

	@Then("^the output freezeMembership is not success$")
	public void the_output_freezeMembership_is_not_success() throws Throwable {
		assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
	}

	@Then("^freezeMembership error message is (.*)$")
	public void freezemembership_error_message_is_MEMBER_ID_REQUIRED(String errorMessage) throws Throwable {
		ExceptionMessage[] exceptionMessages = responseEntity.getBody().getMessages();
		assertTrue(exceptionMessages.length > 0);
		assertEquals(errorMessage, exceptionMessages[0].getErrorMessage());
	}
}
