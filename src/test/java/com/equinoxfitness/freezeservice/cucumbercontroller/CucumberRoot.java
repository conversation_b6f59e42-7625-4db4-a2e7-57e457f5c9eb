package com.equinoxfitness.freezeservice.cucumbercontroller;

import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ContextConfiguration;

import cucumber.api.CucumberOptions;
import cucumber.api.junit.Cucumber;

/**
 * Created by MRomeh.
 */
@ContextConfiguration
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(Cucumber.class)
@CucumberOptions(features = "src/test/resources", plugin = { "json:target/Reports/cucumber-report.json" })
public class CucumberRoot {

	@Autowired
	protected TestRestTemplate template;
}
