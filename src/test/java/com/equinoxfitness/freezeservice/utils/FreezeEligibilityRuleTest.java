package com.equinoxfitness.freezeservice.utils;

import static org.junit.Assert.assertEquals;

import java.text.ParseException;
import java.text.SimpleDateFormat;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeEligibilityRuleTest {

	@InjectMocks
	FreezeEligibilityRule freezeEligibilityRule;

	private MemberAgreementDetail memberAgreementDetail;
	private SimpleDateFormat agreementDateFormatter;
	private CheckFreezeEligibilityInput checkFreezeEligibilityInput;
	private SimpleDateFormat checkFreezeEligibilityInputDateFormatter;

	@Before
	public void setUp() throws Exception {

		// Member Agreement dummy data
		agreementDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

		memberAgreementDetail = new MemberAgreementDetail();
		memberAgreementDetail.setAgreementId("1045");
		memberAgreementDetail.setAgreementType("perpetual");
		memberAgreementDetail.setCancellationDate(
				new java.sql.Date((agreementDateFormatter.parse("2019-06-19 00:00:00.000").getTime())));

		memberAgreementDetail
				.setEndDate(new java.sql.Date((agreementDateFormatter.parse("2021-06-19 00:00:00.000").getTime())));
		memberAgreementDetail.setFreezeReason(null);
		memberAgreementDetail.setFreezeStartTime(null);
		memberAgreementDetail.setFreezeStatus(null);
		memberAgreementDetail.setHomeFacilityId("011");
		memberAgreementDetail.setMemberAgreementId("2524712");
		memberAgreementDetail.setMemberId("1003268500");

		memberAgreementDetail.setObligationDate(
				new java.sql.Date((agreementDateFormatter.parse("2019-06-19 00:00:00.000").getTime())));
		memberAgreementDetail
				.setStartDate(new java.sql.Date((agreementDateFormatter.parse("2019-06-19 00:00:00.000").getTime())));
		memberAgreementDetail.setBillingMethod("TestBillingMethod");
		memberAgreementDetail.setMembershipClass("test");
		memberAgreementDetail.setContractStatus("test");
		// Test data
		checkFreezeEligibilityInputDateFormatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		checkFreezeEligibilityInput = new CheckFreezeEligibilityInput();
		checkFreezeEligibilityInput.setDurationDays(0);
		checkFreezeEligibilityInput.setDurationMonths(2);
		checkFreezeEligibilityInput
				.setEndDate(checkFreezeEligibilityInputDateFormatter.parse("2018-12-22T07:20:19.920Z"));
		checkFreezeEligibilityInput.setFreezeReason("Regular");
		checkFreezeEligibilityInput.setInvokeAsync(true);
		checkFreezeEligibilityInput.setMemberId("1000639346");
		checkFreezeEligibilityInput
				.setRequestedDate(checkFreezeEligibilityInputDateFormatter.parse("2018-12-20T07:20:19.920Z"));
		// checkFreezeEligibilityInput.setSourceSystemID(0);
		checkFreezeEligibilityInput
				.setStartDate(checkFreezeEligibilityInputDateFormatter.parse("2018-12-20T07:20:19.920Z"));
		checkFreezeEligibilityInput.setThreadId("string");
	}

	@Test
	public void testFreezeEligibility_IfMemberAlreadyHaveRequestedForFreeze() throws ParseException {

		memberAgreementDetail.setFreezeEndTime(
				new java.sql.Date((agreementDateFormatter.parse("2022-12-10 00:00:00.000").getTime())));
		memberAgreementDetail.setFreezeStatus("active");

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = freezeEligibilityRule
				.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		assertEquals(true, checkFreezeEligiblityOutput.isEligibleForFreeze());
		//assertEquals("Member already have requested for freeze", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void testFreezeEligibility_forCorporateMember() {
		memberAgreementDetail.setMembershipClass("Corporate");
		memberAgreementDetail.setFreezeEndTime(null);

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = freezeEligibilityRule
				.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		assertEquals(false, checkFreezeEligiblityOutput.isEligibleForFreeze());
		assertEquals("Corporate Members are not allowed to freeze", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void testFreezeEligibility_forPayrollDeductedMember() {
		memberAgreementDetail.setMembershipClass("Member");
		memberAgreementDetail.setFreezeEndTime(null);
		memberAgreementDetail.setBillingMethod("Payroll Deducted");

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = freezeEligibilityRule
				.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		assertEquals(false, checkFreezeEligiblityOutput.isEligibleForFreeze());
		assertEquals("Payroll Deducted Members are not allowed to freeze", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void testFreezeEligibility_IfContractStatusPending() {
		memberAgreementDetail.setMembershipClass("Member");
		memberAgreementDetail.setFreezeEndTime(null);
		memberAgreementDetail.setContractStatus("PENDING");

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = freezeEligibilityRule
				.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		assertEquals(false, checkFreezeEligiblityOutput.isEligibleForFreeze());
		assertEquals("Pending/Cancelled/Expired/On Hold Member are not eligible for freeze.",
				exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void testFreezeEligibility_MembershipClassRule() {
		memberAgreementDetail.setMembershipClass("Member");
		memberAgreementDetail.setFreezeEndTime(null);
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Prospect");

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = freezeEligibilityRule
				.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		assertEquals(false, checkFreezeEligiblityOutput.isEligibleForFreeze());
		assertEquals("Prospect/Short term/Complementary Members are not allowed to freeze.",
				exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void testFreezeEligibility_EquinoxFamilyMembershipRule() {
		memberAgreementDetail.setMembershipClass("Member");
		memberAgreementDetail.setFreezeEndTime(null);
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Employee benefit - select");

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = freezeEligibilityRule
				.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		assertEquals(false, checkFreezeEligiblityOutput.isEligibleForFreeze());
		assertEquals("Equinox Friends and Family Members are not allowed to freeze.",
				exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void testFreezeEligibility_ValidCase() throws ParseException {
		memberAgreementDetail.setFreezeEndTime(
				new java.sql.Date((agreementDateFormatter.parse("2018-06-16 00:00:00.000").getTime())));
		checkFreezeEligibilityInput
				.setStartDate(checkFreezeEligibilityInputDateFormatter.parse("2018-06-17T00:00:00.000Z"));
		memberAgreementDetail.setMembershipClass("Member");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("All Access");
		memberAgreementDetail.setFreezeReason("REGULAR");
		memberAgreementDetail.setFreezeStatus("Pending");
		checkFreezeEligibilityInput.setFreezeReason("REGULAR");

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = freezeEligibilityRule
				.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		assertEquals(true, checkFreezeEligiblityOutput.isEligibleForFreeze());
	}

	@Test
	public void testFreezeEligibility_OneFreezePerContractualYearRule() throws ParseException {
		memberAgreementDetail.setFreezeEndTime(
				new java.sql.Date((agreementDateFormatter.parse("2018-08-17 00:00:00.000").getTime())));
		memberAgreementDetail.setFreezeStartTime(
				new java.sql.Date((agreementDateFormatter.parse("2018-06-15 00:00:00.000").getTime())));
		checkFreezeEligibilityInput
				.setStartDate(checkFreezeEligibilityInputDateFormatter.parse("2018-06-17T00:00:00.000Z"));
		memberAgreementDetail.setMembershipClass("Member");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("All Access");
		memberAgreementDetail.setFreezeReason("REGULAR");
		memberAgreementDetail.setFreezeStatus("Active");
		memberAgreementDetail.setAgreementType("Fixed");

		checkFreezeEligibilityInput.setFreezeReason("REGULAR");

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = freezeEligibilityRule
				.eligibilityRuleCheck(memberAgreementDetail, checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		assertEquals(false, checkFreezeEligiblityOutput.isEligibleForFreeze());
		assertEquals("One freeze allowed per contractual year", exceptionMessage[0].getErrorMessage());

	}
}
