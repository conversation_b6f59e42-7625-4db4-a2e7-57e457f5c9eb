package com.equinoxfitness.freezeservice.utils;

import static org.junit.Assert.*;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FacilityConversionTest {

	@InjectMocks
	FacilityConversion facilityConversion;

	@Test
	public void facilityConversionTest_validCase() {
		String conversion = facilityConversion.facilityConversion("014");
		assertEquals("112", conversion);
	}

	@Test
	public void facilityConversionTest_InvalidCase() {
		String conversion = facilityConversion.facilityConversion("111");
		assertEquals("111", conversion);
	}
}
