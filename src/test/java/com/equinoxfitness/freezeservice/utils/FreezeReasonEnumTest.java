package com.equinoxfitness.freezeservice.utils;

import static org.junit.Assert.*;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeReasonEnumTest {

	@Test
	public void freezeReasonEnumTest_IfMatch() {
		assertEquals(FreezeReasonEnum.EXTERNAL, FreezeReasonEnum.getFreezeReasonFromValue("External"));
		assertEquals("External", FreezeReasonEnum.EXTERNAL.getReason());

		assertEquals(FreezeReasonEnum.REGULAR, FreezeReasonEnum.getFreezeReasonFromValue("Regular"));
		assertEquals("Regular", FreezeReasonEnum.REGULAR.getReason());

		assertEquals(FreezeReasonEnum.PREGNANCY, FreezeReasonEnum.getFreezeReasonFromValue("Pregnancy"));
		assertEquals("Pregnancy", FreezeReasonEnum.PREGNANCY.getReason());

		assertEquals(FreezeReasonEnum.MEDICAL, FreezeReasonEnum.getFreezeReasonFromValue("Medical"));
		assertEquals("Medical", FreezeReasonEnum.MEDICAL.getReason());
	}

	@Test
	public void freezeReasonEnumTest_IfNotMatch() {
		assertEquals(FreezeReasonEnum.OTHER, FreezeReasonEnum.getFreezeReasonFromValue("Health"));
		assertEquals("", FreezeReasonEnum.OTHER.getReason());
	}
}
