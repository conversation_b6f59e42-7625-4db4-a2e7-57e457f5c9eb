package com.equinoxfitness.freezeservice.utils;

import static org.junit.Assert.assertEquals;

import java.util.Date;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInput;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewFinalizeInvoice;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeServiceHelperTest {

	@InjectMocks
	FreezeServiceHelper freezeServiceHelper;

	private FreezeMembershipInput freezeMembershipInput;
	private MemberAgreementDetail memberAgreementDetail;
	private FreezeExtensionInput freezeExtensionInput;

	@Before
	public void setup() {
		freezeMembershipInput = new FreezeMembershipInput();
		freezeMembershipInput.setDurationMonths(2);
		freezeMembershipInput.setStartDate(new Date());
		freezeMembershipInput.setEndDate(new Date());
		freezeMembershipInput.setRequestedDate(new Date());
		freezeMembershipInput.setMemberId("1");
		memberAgreementDetail = new MemberAgreementDetail();
		memberAgreementDetail.setAgreementId("10");
		memberAgreementDetail.setMemberId("1");
		freezeExtensionInput = new FreezeExtensionInput();
		freezeExtensionInput.setMemberId("23423242");
		freezeExtensionInput.setExtensionDurationMonths(2);
	}

	@Test
	public void setSuspensionRequestTest() throws Exception {
		SuspensionRequest suspensionRequest = freezeServiceHelper.setSuspensionRequest("123", memberAgreementDetail,
				freezeMembershipInput);
		assertEquals(123, suspensionRequest.getFreezeReasonId());
	}

	@Test
	public void setCheckFreezeEligibilityInputTest() {
		CheckFreezeEligibilityInput checkFreezeEligibilityInput = freezeServiceHelper
				.setCheckFreezeEligibilityInput(freezeMembershipInput);
		assertEquals("1", checkFreezeEligibilityInput.getMemberId());
	}

	@Test
	public void setEntityTest() {
		GetSessionOutput sessionOutput = new GetSessionOutput();
		sessionOutput.setAuthTokenValue("authTokenValue");
		sessionOutput.setCookieValue("cookieValue");
	}

	@Test
	public void setCreateAndFinalizeReqTest() {
		NewFinalizeInvoice nNewFinalizeInvoice = freezeServiceHelper.setCreateAndFinalizeReq(30.0, freezeExtensionInput,
				"101", 131243242, "12323");
		assertEquals("12323", nNewFinalizeInvoice.getBusinessUnitCode());
	}

	@Test
	public void setnoteRequestTest() {
		Note note = freezeServiceHelper.setnoteRequest("100243413", "testContent");
		assertEquals(4, note.getContactTypeId(), 0);
	}
}