
package com.equinoxfitness.freezeservice.dao;

import static org.junit.Assert.*;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeServiceDAOTest {

	@InjectMocks
	FreezeServiceDAOImpl freezeServiceDAOImpl;

	@Mock
	JdbcTemplate mosoJdbcTemplate;

	@Before
	public void setUp() throws Exception {
		MockitoAnnotations.initMocks(this);
		ReflectionTestUtils.setField(freezeServiceDAOImpl, "mosoJdbcTemplate", mosoJdbcTemplate);
	}

	@Test
	public void freezeServiceDAOImpl_getSuspensionReasonIdValidCase() {
		List<String> suspensionReasonIds = new ArrayList<String>();
		suspensionReasonIds.add(0, "123");
		Mockito.when(mosoJdbcTemplate.queryForList(Mockito.anyString(), Mockito.any(Object[].class),
				Mockito.any(Class.class))).thenReturn(suspensionReasonIds);

		String queryResult = freezeServiceDAOImpl.getSuspensionReasonId("123456");
		assertEquals("123", queryResult);
	}

	@Test
	public void freezeServiceDAOImpl_getMemberAgreementDetailInValidCase() {
		List<MemberAgreementDetail> agreementDetail = new ArrayList<MemberAgreementDetail>();
		Mockito.when(mosoJdbcTemplate.query(Mockito.anyString(), Mockito.any(Object[].class),
				Mockito.any(RowMapper.class))).thenThrow(Exception.class);

		agreementDetail = (List<MemberAgreementDetail>) freezeServiceDAOImpl.getMemberAgreementDetail("123456");
		assertNull(agreementDetail);
	}

	@Test
	public void freezeServiceDAOImpl_getSuspensionReasonIdInvalidCase() {
		Mockito.when(mosoJdbcTemplate.queryForList(Mockito.anyString(), Mockito.any(Object[].class),
				Mockito.any(Class.class))).thenThrow(Exception.class);

		String queryResult = freezeServiceDAOImpl.getSuspensionReasonId("123456");
		assertEquals("", queryResult);
	}
}
