package com.equinoxfitness.freezeservice;

import java.text.ParseException;
import java.text.SimpleDateFormat;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput;
import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl;
import com.equinoxfitness.freezeservice.utils.FreezeEligibilityRule;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeEligibilityCheckServiceTest {

	@InjectMocks
	FreezeServiceImpl freezeServiceImpl;

	@InjectMocks
	FreezeEligibilityRule freezeEligibilityRule;

	@Mock
	FreezeServiceDAO freezeServiceDAO;

	private CheckFreezeEligibilityInput checkFreezeEligibilityInput;
	private MemberAgreementDetail memberAgreementDetail;
	private SimpleDateFormat agreementDateFormatter;
	private SimpleDateFormat checkFreezeEligibilityInputDateFormatter;

	@Before
	public void setUp() throws Exception {
		checkFreezeEligibilityInputDateFormatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

		// Test data
		checkFreezeEligibilityInput = new CheckFreezeEligibilityInput();
		checkFreezeEligibilityInput.setDurationDays(0);
		checkFreezeEligibilityInput.setDurationMonths(2);
		checkFreezeEligibilityInput
				.setEndDate(checkFreezeEligibilityInputDateFormatter.parse("2018-12-22T07:20:19.920Z"));
		checkFreezeEligibilityInput.setFreezeReason("Regular");
		checkFreezeEligibilityInput.setInvokeAsync(true);
		checkFreezeEligibilityInput.setMemberId("1000639346");
		checkFreezeEligibilityInput
				.setRequestedDate(checkFreezeEligibilityInputDateFormatter.parse("2018-12-20T07:20:19.920Z"));
		
		checkFreezeEligibilityInput
				.setStartDate(checkFreezeEligibilityInputDateFormatter.parse("2018-12-20T07:20:19.920Z"));
		checkFreezeEligibilityInput.setThreadId("string");

		// dummy agreement data
		agreementDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

		memberAgreementDetail = new MemberAgreementDetail();
		memberAgreementDetail.setAgreementId("1045");
		memberAgreementDetail.setAgreementType("perpetual");
		memberAgreementDetail.setCancellationDate(
				new java.sql.Date((agreementDateFormatter.parse("2019-06-19 00:00:00.000").getTime())));
		memberAgreementDetail
				.setEndDate(new java.sql.Date((agreementDateFormatter.parse("2019-06-19 00:00:00.000").getTime())));
		memberAgreementDetail.setFreezeEndTime(null);
		memberAgreementDetail.setFreezeReason(null);
		memberAgreementDetail.setFreezeStartTime(null);
		memberAgreementDetail.setFreezeStatus(null);
		memberAgreementDetail.setHomeFacilityId("011");
		memberAgreementDetail.setMemberAgreementId("2524712");
		memberAgreementDetail.setMemberId("1003268500");
		memberAgreementDetail.setMemberStatus("Active");
		memberAgreementDetail.setObligationDate(
				new java.sql.Date((agreementDateFormatter.parse("2019-06-19 00:00:00.000").getTime())));
		memberAgreementDetail
				.setStartDate(new java.sql.Date((agreementDateFormatter.parse("2019-06-19 00:00:00.000").getTime())));

	}

	@Test
	public void testFreezeEligibilityMethod_WhenMemberAgreementDetailIsNotPresent() {

		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Mockito.anyString())).thenReturn(null);
		checkFreezeEligiblityOutput = freezeServiceImpl.checkFreezeEligibility(checkFreezeEligibilityInput);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("MemberAgreementDetail should not be null.", exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	@Test
	public void testFreezeEligibilityRules_WhenMembershipClassIsCorporate() {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();
		Boolean hasCOF = false;
		memberAgreementDetail.setMembershipClass("Corporate");

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, hasCOF);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("Corporate Members are not allowed to freeze", exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	@Test
	public void testFreezeEligibilityRuleWhen_ContractStatusIsPending() {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("PENDING");
		memberAgreementDetail.setBillingMethod("testBillingMethod");
		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("Pending/Cancelled/Expired/On Hold Member are not eligible for freeze.",
				exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	@Test
	public void testFreezeEligibilityRule_WhenMembershipClassIsProspect() {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Prospect");
		memberAgreementDetail.setAgreementType("Fixed");
		memberAgreementDetail.setBillingMethod("testBillingMethod");

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("Prospect/Short term/Complementary Members are not allowed to freeze.",
				exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	// @Test
	public void testFreezeEligibilityRule_WhenFreezeStartDateShouldNotAtLeast5DaysFromExpirationDate()
			throws ParseException {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Member");

		memberAgreementDetail
				.setEndDate(new java.sql.Date((agreementDateFormatter.parse("2019-06-19 11:00:00.000").getTime())));

		memberAgreementDetail.setAgreementType("Fixed");
		checkFreezeEligibilityInput
				.setStartDate(checkFreezeEligibilityInputDateFormatter.parse("2019-06-20T07:20:19.920Z"));

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("The Freeze Start date should be at least 5 days from the expiration date.",
				exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	// EquinoxFamilyMembershipRule
	@Test
	public void testFreezeEligibilityRule_ForEquinoxFamilyMembership() throws ParseException {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Employee benefit - select");

		memberAgreementDetail.setEndDate(null);

		memberAgreementDetail.setAgreementType("Fixed");
		memberAgreementDetail.setBillingMethod("testBillingMethod");

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("Equinox Friends and Family Members are not allowed to freeze.",
				exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	// FreezeDurationRule
	// Regular case
	// @Test
	public void testFreezeEligibility_FreezeDurationRuleRegularCase() throws ParseException {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Member");

		memberAgreementDetail.setEndDate(null);

		memberAgreementDetail.setAgreementType("Fixed");

		checkFreezeEligibilityInput.setDurationMonths(4);
		checkFreezeEligibilityInput.setFreezeReason("REGULAR");

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("Regular freeze limit is 3 months", exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	// PREGNANCY Case
	// @Test
	public void testFreezeEligibility_FreezeDurationRulePregnancyCase() throws ParseException {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Member");

		memberAgreementDetail.setEndDate(null);

		memberAgreementDetail.setAgreementType("Fixed");

		checkFreezeEligibilityInput.setDurationMonths(10);
		checkFreezeEligibilityInput.setFreezeReason("PREGNANCY");

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("Pregnancy freeze limit is 9 months", exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	// MEDICAL Case
	// @Test
	public void testFreezeEligibility_FreezeDurationRuleMedicalCase() throws ParseException {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Member");

		memberAgreementDetail.setEndDate(null);

		memberAgreementDetail.setAgreementType("Fixed");

		checkFreezeEligibilityInput.setDurationMonths(7);
		checkFreezeEligibilityInput.setFreezeReason("MEDICAL");

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("Medical freeze limit is 6 months", exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	// OTHER case
	// @Test
	public void testFreezeEligibility_FreezeDurationRuleOtherCase() throws ParseException {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Member");

		memberAgreementDetail.setEndDate(null);

		memberAgreementDetail.setAgreementType("Fixed");

		checkFreezeEligibilityInput.setDurationMonths(7);
		checkFreezeEligibilityInput.setFreezeReason("OTHER");

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("Freeze eligibilty can only be checked for reasons such as Regular/Medical/Pregnancy",
				exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

	// ContractCancellationRule
	// when cancellation request exists
	// @Test
	public void testFreezeEligibility_WhenCancellationRequestExists() throws ParseException {
		CheckFreezeEligiblityOutput checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		memberAgreementDetail.setMembershipClass("All Access Fitness");
		memberAgreementDetail.setContractStatus("Active");
		memberAgreementDetail.setMembershipClass("Member");

		memberAgreementDetail.setEndDate(null);
		memberAgreementDetail.setMemberStatus("On Freeze");
		memberAgreementDetail.setAgreementType("Fixed");

		checkFreezeEligibilityInput
				.setEndDate(checkFreezeEligibilityInputDateFormatter.parse("2019-06-20T07:20:19.920Z"));

		checkFreezeEligibilityInput.setDurationMonths(7);
		checkFreezeEligibilityInput.setFreezeReason("OTHER");

		checkFreezeEligiblityOutput = freezeEligibilityRule.eligibilityRuleCheck(memberAgreementDetail,
				checkFreezeEligibilityInput, false);

		ExceptionMessage[] exceptionMessage = checkFreezeEligiblityOutput.getMessages();
		Assert.assertEquals("A cancellation request exists.", exceptionMessage[0].getErrorMessage());
		Assert.assertEquals("VALIDATION", exceptionMessage[0].getMessageType());
	}

}
