package com.equinoxfitness.freezeservice;

import static org.junit.Assert.assertEquals;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.commons.output.Facility;
import com.equinoxfitness.commons.output.GetFacilityResponse;
import com.equinoxfitness.commons.service.FacilityService;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInput;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput;
import com.equinoxfitness.freezeservice.contract.moso.Item;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.PriceDetails;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionResponse;
import com.equinoxfitness.freezeservice.contract.moso.invoice.Invoice;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewFinalizeInvoice;
import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.service.FreezeMosoService;
import com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl;
import com.equinoxfitness.freezeservice.utils.FreezeServiceHelper;
import com.equinoxfitness.freezeservice.utils.MosoSessionMediatorForFreeze;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeExtensionServiceTest {

	@InjectMocks
	FreezeServiceImpl freezeServiceImpl;

	@Mock
	FreezeServiceDAO freezeServiceDAO;

	@Mock
	FacilityService facilityService;

	@Mock
	MosoSessionMediatorForFreeze mosoSessionMediator;

	@Mock
	FreezeMosoService mosoService;

	@Mock
	FreezeServiceHelper freezeHelper;

	private FreezeExtensionInput freezeExtensionInput;
	private MemberAgreementDetail memberAgreementDetail;
	private GetFacilityResponse facilityResponse;
	private Facility facility;
	private GetSessionOutput sessionOutput;
	private SuspensionResponse freezeExtResponse;

	@Before
	public void setUp() throws ParseException {

		// Dummy input data
		freezeExtensionInput = new FreezeExtensionInput();
		freezeExtensionInput.setMemberId("23423242");

		// Member Agreement dummy data
		memberAgreementDetail = new MemberAgreementDetail();
		memberAgreementDetail.setAgreementId("4430853653");
		memberAgreementDetail.setMemberId("1003405683");
		memberAgreementDetail.setHomeFacilityId("111");

		// dummy facility response
		facilityResponse = new GetFacilityResponse();
		facilityResponse.setSuccess(true);

		facility = new Facility();
		facility.setClubId("12");
		facility.seteClubFacilityId("123");
		facility.setSourceSystem(1);
		facilityResponse.setResult(facility);

		// dummy session data
		sessionOutput = new GetSessionOutput();
		sessionOutput.setAuthTokenValue("6b3f6cc2-635f-4d32-bfb2-b60191403e39");
		sessionOutput.setCookieValue(
				"AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200");

		// Dummy reezeExtResponse data
		freezeExtResponse = new SuspensionResponse();
		freezeExtResponse.setFreezeStatus("1");
		freezeExtResponse.setFreezeFeeAmount(30.0);
		freezeExtResponse.setFreezeEndDate("2019-03-16T00:00:00.0000000-04:00");
		freezeExtResponse.setFreezeStartDate("2019-03-15T00:00:00.0000000+00:00");
		freezeExtResponse.setFreezeFeeItem("Freeze Fee - 1 Month");
		freezeExtResponse.setFreezeFeeStartDate("2016-11-29T13:43:04.0000000-05:00");
		freezeExtResponse.setMemberFirstName("Marcela");
		freezeExtResponse.setMemberLastName("Castro");
		freezeExtResponse.setSuspendedAgreementName("Corp JPMC HQ Select");
		freezeExtResponse.setSuspensionId(207883);

	}

	@Test
	public void FreezeExtensionServiceTest_IfMemberAgreementDetailNotFound() {

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Matchers.anyString())).thenReturn(null);
		FreezeExtensionOutput freezeExtensionOutput = freezeServiceImpl.freezeExtension(freezeExtensionInput);
		ExceptionMessage[] exceptionMessage = freezeExtensionOutput.getMessages();
		assertEquals("MemberAgreementDetail should not be null.", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void FreezeExtensionServiceTest_IfNoActivePendingFreezeRequestExist() {
		memberAgreementDetail.setFreezeStatus("Cancelled");
		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Matchers.anyString())).thenReturn(memberAgreementDetail);

		FreezeExtensionOutput freezeExtensionOutput = freezeServiceImpl.freezeExtension(freezeExtensionInput);
		ExceptionMessage[] exceptionMessage = freezeExtensionOutput.getMessages();
		assertEquals("No Active/Pending Freeze request exist for member.", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void FreezeExtensionServiceTest_IfFacillityServiceFailed() {
		memberAgreementDetail.setFreezeStatus("Active");
		memberAgreementDetail.setFreezeId("3424");
		facilityResponse.setResult(null);

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Matchers.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(facilityService.getFacilityByIdV2(Mockito.anyString())).thenReturn(facilityResponse);

		FreezeExtensionOutput freezeExtensionOutput = freezeServiceImpl.freezeExtension(freezeExtensionInput);
		ExceptionMessage[] exceptionMessage = freezeExtensionOutput.getMessages();
		assertEquals("FACILITY_SERVICE_FAILED", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void FreezeExtensionServiceTest_IfGetSessionFailed() {

		memberAgreementDetail.setFreezeStatus("Active");
		memberAgreementDetail.setFreezeId("3424");

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Matchers.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(facilityService.getFacilityByIdV2(Mockito.anyString())).thenReturn(facilityResponse);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

		FreezeExtensionOutput freezeExtensionOutput = freezeServiceImpl.freezeExtension(freezeExtensionInput);
		ExceptionMessage[] exceptionMessage = freezeExtensionOutput.getMessages();
		assertEquals("GETSESSION_FAILED", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void FreezeExtensionServiceTest_IfMosoAPIForFreezeExtensionFailed() {

		memberAgreementDetail.setFreezeStatus("Active");
		memberAgreementDetail.setFreezeId("3424");
		SuspensionResponse freezeExtResponse = new SuspensionResponse();
		freezeExtResponse.setMessage("FREEZE_EXTENSION_FAILED");

		// testdata
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Matchers.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(facilityService.getFacilityByIdV2(Mockito.anyString())).thenReturn(facilityResponse);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		Mockito.when(freezeHelper.setEntity(sessionOutput)).thenReturn(entity);
		Mockito.when(
				mosoService.freezeExtension(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(freezeExtResponse);

		FreezeExtensionOutput freezeExtensionOutput = freezeServiceImpl.freezeExtension(freezeExtensionInput);
		ExceptionMessage[] exceptionMessage = freezeExtensionOutput.getMessages();
		assertEquals("FREEZE_EXTENSION_FAILED", exceptionMessage[0].getErrorMessage());
		assertEquals("ERROR", exceptionMessage[0].getMessageType());
	}

	@Test
	public void FreezeExtensionServiceTest_SuceessScenario() throws ParseException {

		memberAgreementDetail.setFreezeStatus("Active");
		memberAgreementDetail.setFreezeId("3424");
		freezeExtensionInput.setWaiveOffExtensionFee(false);
		freezeExtensionInput.setFreezeReason("Regular");
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

		Date date = formatter.parse("2019-06-15T13:07:14.111Z");
		freezeExtensionInput.setFreezeExtensionEndDate(date);

		// testdata
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		Item item = new Item();
		PriceDetails[] priceDetail = new PriceDetails[1];
		priceDetail[0] = new PriceDetails();
		priceDetail[0].setBusinessUnitCode("testBUCode");
		priceDetail[0].setPrice(30.0);
		priceDetail[0].setSourceId(1);
		priceDetail[0].setSourceName("testSourceName");
		priceDetail[0].setStartDateEx("2019-03-15T00:00:00.0000000+00:00");

		item.setPrices(priceDetail);
		item.setCode("21");

		Invoice invoice = new Invoice();
		invoice.setBalance(new BigDecimal(********));
		invoice.setClientAccountId(7398439);
		invoice.setComments("testComments");
		invoice.setId(87943);
		invoice.setInvoiceStatus("success");
		invoice.setMessage("testMessage");
		invoice.setPartyRoleId(*********);
		invoice.setPaymentDueDate("testDate");
		invoice.setTargetDate("targetDate");
		invoice.setTaxExemptId(4254);
		invoice.setTotal("435363");

		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		newFinalizeInvoice.setAccountId(123123);
		newFinalizeInvoice.setBusinessUnitCode("********");
		newFinalizeInvoice.setComments("testComments");
		newFinalizeInvoice.setCurrencyCode("1");
		newFinalizeInvoice.setEmployeeAuthorizationPin("23523");
		newFinalizeInvoice.setMemberId("**********");
		newFinalizeInvoice.setTargetDate("2019-03-15T00:00:00.0000000+00:00");
		newFinalizeInvoice.setTaxExemptId(4353654);

		Note note = new Note();
		note.setCheckinAlert(false);
		note.setContactTypeId(32432);
		note.setContent("testContent");
		note.setErrorMessage("testErrorMessage");
		note.setId("232");
		note.setMessage("testMessage");
		note.setNoteTypeId(3);

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Matchers.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(facilityService.getFacilityByIdV2(Mockito.anyString())).thenReturn(facilityResponse);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		Mockito.when(freezeHelper.setEntity(sessionOutput)).thenReturn(entity);
		Mockito.when(
				mosoService.freezeExtension(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(freezeExtResponse);

		Mockito.when(mosoService.itemSearch("Freeze Fee - Non Refundable", sessionOutput, entity)).thenReturn(item);

		Mockito.when(freezeHelper.setCreateAndFinalizeReq(Mockito.anyDouble(), Mockito.any(FreezeExtensionInput.class),
				Mockito.anyString(), Mockito.anyInt(), Mockito.anyString())).thenReturn(newFinalizeInvoice);

		Mockito.when(mosoService.createAndfinalizeInvoice(Mockito.any(NewFinalizeInvoice.class),
				Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class),Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(invoice);

		Mockito.when(freezeHelper.setnoteRequest(Mockito.anyString(), Mockito.anyString())).thenReturn(note);

		Mockito.when(mosoService.addNote(Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class),
				Mockito.any(Note.class))).thenReturn(note);

		FreezeExtensionOutput freezeExtensionOutput = freezeServiceImpl.freezeExtension(freezeExtensionInput);

		assertEquals("1", freezeExtensionOutput.getFreezeStatus());
	}

	@Test
	public void FreezeExtensionServiceTest_SuceessScenario2() throws ParseException {

		memberAgreementDetail.setFreezeStatus("Active");
		memberAgreementDetail.setFreezeId("3424");
		freezeExtensionInput.setWaiveOffExtensionFee(false);
		freezeExtensionInput.setFreezeReason("Regular");
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

		Date date = formatter.parse("2019-06-15T13:07:14.111Z");
		freezeExtensionInput.setFreezeExtensionEndDate(date);

		// testdata
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		Item item = new Item();
		PriceDetails[] priceDetail = new PriceDetails[1];
		priceDetail[0] = new PriceDetails();
		priceDetail[0].setBusinessUnitCode("testBUCode");
		priceDetail[0].setPrice(30.0);
		priceDetail[0].setSourceId(4);
		priceDetail[0].setSourceName("testSourceName");
		priceDetail[0].setStartDateEx("2019-03-15T00:00:00.0000000+00:00");

		item.setPrices(priceDetail);
		item.setCode("21");

		Invoice invoice = new Invoice();
		invoice.setBalance(new BigDecimal(********));
		invoice.setClientAccountId(7398439);
		invoice.setComments("testComments");
		invoice.setId(87943);
		invoice.setInvoiceStatus("success");
		invoice.setMessage("testMessage");
		invoice.setPartyRoleId(*********);
		invoice.setPaymentDueDate("testDate");
		invoice.setTargetDate("targetDate");
		invoice.setTaxExemptId(4254);
		invoice.setTotal("435363");
		invoice.setMessage("");

		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		newFinalizeInvoice.setAccountId(123123);
		newFinalizeInvoice.setBusinessUnitCode("********");
		newFinalizeInvoice.setComments("testComments");
		newFinalizeInvoice.setCurrencyCode("1");
		newFinalizeInvoice.setEmployeeAuthorizationPin("23523");
		newFinalizeInvoice.setMemberId("**********");
		newFinalizeInvoice.setTargetDate("2019-03-15T00:00:00.0000000+00:00");
		newFinalizeInvoice.setTaxExemptId(4353654);

		Note note = new Note();
		note.setCheckinAlert(false);
		note.setContactTypeId(32432);
		note.setContent("testContent");
		note.setErrorMessage("testErrorMessage");
		note.setId("232");
		note.setMessage("testMessage");
		note.setNoteTypeId(3);

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Matchers.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(facilityService.getFacilityByIdV2(Mockito.anyString())).thenReturn(facilityResponse);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		Mockito.when(freezeHelper.setEntity(sessionOutput)).thenReturn(entity);
		Mockito.when(
				mosoService.freezeExtension(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(freezeExtResponse);

		Mockito.when(mosoService.itemSearch("Freeze Fee - Non Refundable", sessionOutput, entity)).thenReturn(item);

		Mockito.when(freezeHelper.setCreateAndFinalizeReq(Mockito.anyDouble(), Mockito.any(FreezeExtensionInput.class),
				Mockito.anyString(), Mockito.anyInt(), Mockito.anyString())).thenReturn(newFinalizeInvoice);

		Mockito.when(mosoService.createAndfinalizeInvoice(Mockito.any(NewFinalizeInvoice.class),
				Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(invoice);

		Mockito.when(freezeHelper.setnoteRequest(Mockito.anyString(), Mockito.anyString())).thenReturn(note);

		Mockito.when(mosoService.addNote(Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class),
				Mockito.any(Note.class))).thenReturn(note);

		FreezeExtensionOutput freezeExtensionOutput = freezeServiceImpl.freezeExtension(freezeExtensionInput);

		assertEquals("1", freezeExtensionOutput.getFreezeStatus());
	}
}
