package com.equinoxfitness.freezeservice;

import static org.junit.Assert.assertEquals;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.commons.service.EventsService;
import com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput;
import com.equinoxfitness.freezeservice.contract.moso.Effects;
import com.equinoxfitness.freezeservice.contract.moso.FreezeReasonResponse;
import com.equinoxfitness.freezeservice.contract.moso.InvoiceConfigValues;
import com.equinoxfitness.freezeservice.contract.moso.Item;
import com.equinoxfitness.freezeservice.contract.moso.PriceDetails;
import com.equinoxfitness.freezeservice.contract.moso.TaxGroup;
import com.equinoxfitness.freezeservice.contract.moso.TaxRate;
import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.service.FreezeMosoService;
import com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl;
import com.equinoxfitness.freezeservice.utils.FreezeServiceHelper;
import com.equinoxfitness.freezeservice.utils.MosoSessionMediatorForFreeze;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RetrieveFreezeReasonServiceTest {

	@InjectMocks
	FreezeServiceImpl freezeServiceImpl;

	@Mock
	FreezeServiceDAO freezeServiceDAO;

	@Mock
	MosoSessionMediatorForFreeze mosoSessionMediator;

	@Mock
	EventsService mockEventsService;

	@Mock
	FreezeServiceHelper freezeHelper;

	@Mock
	FreezeMosoService mosoService;

	private MemberAgreementDetail memberAgreementDetail;
	private GetSessionOutput sessionOutput;
	private FreezeReasonResponse freezeReasonResponse;
	private Effects effects;

	@Before
	public void setUp() {

		// Member Agreement dummy data
		memberAgreementDetail = new MemberAgreementDetail();
		memberAgreementDetail.setAgreementId("4430853653");
		memberAgreementDetail.setMemberId("1003405683");
		memberAgreementDetail.setHomeFacilityId("111");
		memberAgreementDetail.setCountryCode("111");

		// dummy session data
		sessionOutput = new GetSessionOutput();
		sessionOutput.setAuthTokenValue("6b3f6cc2-635f-4d32-bfb2-b60191403e39");
		sessionOutput.setCookieValue(
				"AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200");

		// dummy freeze reason data
		freezeReasonResponse = new FreezeReasonResponse();
		effects = new Effects();
		freezeReasonResponse.setEffects(effects);
	}

	@Test
	public void retrieveFreezeReasonTest_MemberDetailIsNull() {
		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Mockito.anyString())).thenReturn(null);

		RetrieveFreezeReasonOutput retrieveFreezeReasonOutput = freezeServiceImpl.retrieveFreezeReason("*********",
				"TestReason", 2);

		ExceptionMessage[] exceptionMessage = retrieveFreezeReasonOutput.getMessages();
		assertEquals("MemberAgreementDetail should not be null.", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void retrieveFreezeReasonTest_IfGetSessionFailed() {

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Mockito.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
//		Mockito.doNothing().when(mockEventsService).logEventsInDynamoTrace(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), 
//				Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.any(), Mockito.anyLong());

		RetrieveFreezeReasonOutput retrieveFreezeReasonOutput = freezeServiceImpl.retrieveFreezeReason("*********",
				"TestReason", 2);
		ExceptionMessage[] exceptionMessage = retrieveFreezeReasonOutput.getMessages();
		assertEquals("Get Session failed", exceptionMessage[0].getFriendlyMessage());
	}

	@Test
	public void retrieveFreezeReasonTest_IfGetFreezeDataMosoAPIFailed() {

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Mockito.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		// testdata
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		Mockito.when(freezeHelper.setEntity(sessionOutput)).thenReturn(entity);
		Mockito.when(mosoService.getFreezeData(Mockito.anyString(), Mockito.anyInt(),
				Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class))).thenReturn(null);
//		Mockito.doNothing().when(mockEventsService).logEventsInDynamoTrace(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), 
//				Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.any(), Mockito.anyLong());


		RetrieveFreezeReasonOutput retrieveFreezeReasonOutput = freezeServiceImpl.retrieveFreezeReason("*********",
				"TestReason", 2);
		ExceptionMessage[] exceptionMessage = retrieveFreezeReasonOutput.getMessages();
		assertEquals("Get Freeze Data moso API failed.", exceptionMessage[0].getFriendlyMessage());
	}

	@Test
	public void retrieveFreezeReasonTest_IfNoOneTimeFeeItem() {

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Mockito.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		// testdata
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		Mockito.when(freezeHelper.setEntity(sessionOutput)).thenReturn(entity);
		Mockito.when(mosoService.getFreezeData(Mockito.anyString(), Mockito.anyInt(),
				Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class))).thenReturn(freezeReasonResponse);

		RetrieveFreezeReasonOutput retrieveFreezeReasonOutput = freezeServiceImpl.retrieveFreezeReason("*********",
				"TestReason", 2);
		ExceptionMessage[] exceptionMessage = retrieveFreezeReasonOutput.getMessages();
		assertEquals("NO_ONE_TIME_FEE_ITEM", exceptionMessage[0].getFriendlyMessage());
	}

	@Test
	public void retrieveFreezeReasonTest_IfMosoItemSearchAPIFailed() {

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Mockito.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		// testdata
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		effects.setOneTimeFeeItem("testOneTimeFeeItem");
		freezeReasonResponse.setEffects(effects);

		Item item = new Item();
		item.setMessage("Faied to get data from Moso Item API");

		Mockito.when(freezeHelper.setEntity(sessionOutput)).thenReturn(entity);
		Mockito.when(mosoService.getFreezeData(Mockito.anyString(), Mockito.anyInt(),
				Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class))).thenReturn(freezeReasonResponse);
		Mockito.when(mosoService.itemSearch(Mockito.anyString(), Mockito.any(GetSessionOutput.class),
				Mockito.any(HttpEntity.class))).thenReturn(item);

		RetrieveFreezeReasonOutput retrieveFreezeReasonOutput = freezeServiceImpl.retrieveFreezeReason("*********",
				"TestReason", 2);
		ExceptionMessage[] exceptionMessage = retrieveFreezeReasonOutput.getMessages();
		assertEquals("Faied to get data from Moso Item API", exceptionMessage[0].getFriendlyMessage());
	}
	
	@Test
	public void retrieveFreezeReasonTest_ifMosoGetTaxRateAPIFailed() {
		
		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Mockito.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		// testdata
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		effects.setOneTimeFeeItem("testOneTimeFeeItem");
		freezeReasonResponse.setEffects(effects);
		freezeReasonResponse.setSuspensionReasonDescription("Regular");

		Item item = new Item();
		PriceDetails[] priceDetail = new PriceDetails[1];
		priceDetail[0] = new PriceDetails();
		priceDetail[0].setBusinessUnitCode("testBUCode");
		priceDetail[0].setPrice(30.0);
		priceDetail[0].setSourceId(1);
		priceDetail[0].setSourceName("testSourceName");
		priceDetail[0].setStartDateEx("2019-03-15T00:00:00.0000000+00:00");

		item.setPrices(priceDetail);
		item.setCode("21");

		InvoiceConfigValues invoiceConfig = new InvoiceConfigValues();
		TaxGroup taxGroup = new  TaxGroup();
		taxGroup.setId(7);
		TaxRate[] taxRates = new TaxRate[1];
		taxRates[0]= new TaxRate();
		taxRates[0].setId(123);
		taxRates[0].setRate(90232);
		taxRates[0].setTaxGroup(taxGroup);
		invoiceConfig.setTaxRates(taxRates);
		
		Mockito.when(freezeHelper.setEntity(sessionOutput)).thenReturn(entity);
		Mockito.when(mosoService.getFreezeData(Mockito.anyString(), Mockito.anyInt(),
				Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class))).thenReturn(freezeReasonResponse);
		Mockito.when(mosoService.itemSearch(Mockito.anyString(), Mockito.any(GetSessionOutput.class),
				Mockito.any(HttpEntity.class))).thenReturn(item);
		Mockito.when(mosoService.getTaxRate(Mockito.anyString(), Mockito.anyString(), Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class))).thenReturn(invoiceConfig);
		
		RetrieveFreezeReasonOutput retrieveFreezeReasonOutput = freezeServiceImpl.retrieveFreezeReason("*********",
				"TestReason", 2);
		assertEquals("Regular", retrieveFreezeReasonOutput.getSuspensionReasonName());
	}
	
	@Test
	public void retrieveFreezeReasonTest_SuccessScenario() {
		
		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Mockito.anyString())).thenReturn(memberAgreementDetail);
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		// testdata
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", sessionOutput.getCookieValue());
		HttpEntity<HttpHeaders> entity = new HttpEntity<>(headers);

		effects.setOneTimeFeeItem("testOneTimeFeeItem");
		freezeReasonResponse.setEffects(effects);

		Item item = new Item();
		PriceDetails[] priceDetail = new PriceDetails[1];
		priceDetail[0] = new PriceDetails();
		priceDetail[0].setBusinessUnitCode("testBUCode");
		priceDetail[0].setPrice(30.0);
		priceDetail[0].setSourceId(1);
		priceDetail[0].setSourceName("testSourceName");
		priceDetail[0].setStartDateEx("2019-03-15T00:00:00.0000000+00:00");

		item.setPrices(priceDetail);
		item.setCode("21");

		InvoiceConfigValues invoiceConfig = new InvoiceConfigValues();
		TaxGroup taxGroup = new  TaxGroup();
		taxGroup.setId(7);
		TaxRate[] taxRates = new TaxRate[1];
		taxRates[0]= new TaxRate();
		taxRates[0].setId(123);
		taxRates[0].setRate(90232);
		taxRates[0].setTaxGroup(taxGroup);
		invoiceConfig.setTaxRates(taxRates);
		invoiceConfig.setMessage("TestMessage");
		
		Mockito.when(freezeHelper.setEntity(sessionOutput)).thenReturn(entity);
		Mockito.when(mosoService.getFreezeData(Mockito.anyString(), Mockito.anyInt(),
				Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class))).thenReturn(freezeReasonResponse);
		Mockito.when(mosoService.itemSearch(Mockito.anyString(), Mockito.any(GetSessionOutput.class),
				Mockito.any(HttpEntity.class))).thenReturn(item);
		Mockito.when(mosoService.getTaxRate(Mockito.anyString(), Mockito.anyString(), Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class))).thenReturn(invoiceConfig);
		
		RetrieveFreezeReasonOutput retrieveFreezeReasonOutput = freezeServiceImpl.retrieveFreezeReason("*********",
				"TestReason", 2);
		assertEquals(0.0, retrieveFreezeReasonOutput.getOneTimeFee(),0.01);
	}
}