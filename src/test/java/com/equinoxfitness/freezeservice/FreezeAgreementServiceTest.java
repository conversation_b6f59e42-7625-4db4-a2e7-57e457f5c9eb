package com.equinoxfitness.freezeservice;

import java.util.Date;

import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.test.context.junit4.SpringRunner;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput;
import com.equinoxfitness.freezeservice.contract.FreezeMemberResponse;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV3;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionResponse;
import com.equinoxfitness.freezeservice.contract.sfdc.CreateCaseResponse;
import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.service.FreezeMosoService;
import com.equinoxfitness.freezeservice.service.SFDCService;
import com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl;
import com.equinoxfitness.freezeservice.utils.FacilityConversion;
import com.equinoxfitness.freezeservice.utils.FreezeServiceHelper;
import com.equinoxfitness.freezeservice.utils.MosoSessionMediatorForFreeze;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeAgreementServiceTest {

	@InjectMocks
	FreezeServiceImpl freezeServiceImpl;

	@Mock
	FreezeServiceDAO freezeServiceDAO;

	@Mock
	FreezeServiceHelper freezeHelper;

	@Mock
	FacilityConversion facilityConversion;

	@Mock
	SFDCService sfdcService;

	@Mock
	MosoSessionMediatorForFreeze mosoSessionMediator;

	@Mock
	FreezeMosoService mosoService;

	private FreezeMembershipInputV3 freezeMembershipInput;
	private MemberAgreementDetail memberAgreementDetail;
	private CheckFreezeEligibilityInput checkFreezeEligibilityInput;
	private CheckFreezeEligiblityOutput checkFreezeEligiblityOutput;
	private GetSessionOutput sessionOutput;
	private Date clubOpenDate;
	private Date requestedDate;
	private Date freezeEndDate;
	
	@SuppressWarnings("deprecation")
	@Before
	public void setUp() {

		// Test data
		freezeMembershipInput = new FreezeMembershipInputV3();
		clubOpenDate = new Date(2000, 9, 21);
		requestedDate = new Date(2000, 9, 21);
		freezeEndDate = new Date(2000,11,1);
		freezeMembershipInput.setFacilityId("234");
		freezeMembershipInput.setMosoId("123");
		freezeMembershipInput.setFreezeReason("Covid-Utilization");
		freezeMembershipInput.setClubOpenDate(clubOpenDate);
		freezeMembershipInput.setRequestedDate(requestedDate);

		// Member Agreement dummy data
		memberAgreementDetail = new MemberAgreementDetail();
		memberAgreementDetail.setAgreementId("4430853653");
		memberAgreementDetail.setMemberId("1003405683");
		memberAgreementDetail.setHomeFacilityId("111");

		// freeze eligibility dummy data
		checkFreezeEligibilityInput = new CheckFreezeEligibilityInput();
		//checkFreezeEligibilityInput.setDurationMonths(2);
		checkFreezeEligibilityInput.setEndDate(freezeEndDate);
		checkFreezeEligibilityInput.setFreezeReason("Reason");
		checkFreezeEligibilityInput.setMemberId("1003405683");
		checkFreezeEligibilityInput.setRequestedDate(new Date());
		checkFreezeEligibilityInput.setStartDate(clubOpenDate);

		// freeze eligibility output dummy data
		checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		// session dummy data
		sessionOutput = new GetSessionOutput();
		sessionOutput.setAuthTokenValue("6b3f6cc2-635f-4d32-bfb2-b60191403e39");
		sessionOutput.setCookieValue(
				"AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200");
	}
	
	@Test
	public void freezeMembershipTest_SuccessScenario() throws Exception {
		freezeMembershipInput.setSource("b123");
		freezeMembershipInput.setFreezeReason("Covid-Utilization");
		freezeMembershipInput.setFreezeEndDate(freezeEndDate);
		
		Mockito.when(freezeHelper.setCheckFreezeEligibilityInputV3(Matchers.any(),Matchers.any()))
				.thenReturn(checkFreezeEligibilityInput);
		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMosoId()))
		.thenReturn(memberAgreementDetail);
		Mockito.when(facilityConversion.facilityConversion(Matchers.anyString())).thenReturn("111");
		Mockito.when(freezeServiceDAO.getSuspensionReasonId(Matchers.anyString())).thenReturn("342");
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		SuspensionRequest suspensionRequest = new SuspensionRequest();
		suspensionRequest.setAgreementId("AgreementId");
		suspensionRequest.setComments("Comments");
		suspensionRequest.setFreezeEndDate("Enddate");
		suspensionRequest.setFreezeReasonId(13);
		suspensionRequest.setFreezeStartDate("freezeDate");
		suspensionRequest.setRequestedDate("RequestedDate" + "");
		suspensionRequest.setRoleId(freezeMembershipInput.getMosoId());

		SuspensionResponse suspensionResponse = new SuspensionResponse();
		suspensionResponse.setFreezeFeeAmount(3463);
		Note note = new Note();

		Mockito.when(freezeHelper.setSuspensionRequest(Mockito.anyString(), Mockito.any(MemberAgreementDetail.class),
				Mockito.any(FreezeMembershipInput.class))).thenReturn(suspensionRequest);
		Mockito.when((mosoService.freezeAgreement(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())))
				.thenReturn(suspensionResponse);
		Mockito.when(freezeHelper.setnoteRequest(Mockito.anyString(), Mockito.anyString())).thenReturn(note);
		Mockito.when(mosoService.addNote(Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class),
				Mockito.any(Note.class))).thenReturn(note);
		Mockito.when(sfdcService.createCase(Mockito.any(FreezeMembershipInput.class), Mockito.anyString(),
				Mockito.anyDouble())).thenReturn(new CreateCaseResponse());
		
		FreezeMemberResponse freezeMemberResponse = freezeServiceImpl.freezeMembershipV3(freezeMembershipInput);
		//System.out.println(freezeMemberResponse.getMessages()[0].getFriendlyMessage());
		assertEquals(false, freezeMemberResponse.isRequestReceivedSuccessfully());

	}
}
