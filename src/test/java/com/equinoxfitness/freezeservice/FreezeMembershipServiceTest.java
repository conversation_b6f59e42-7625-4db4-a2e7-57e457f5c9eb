package com.equinoxfitness.freezeservice;

import java.util.Date;

import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.test.context.junit4.SpringRunner;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput;
import com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput;
import com.equinoxfitness.freezeservice.contract.FreezeMemberResponse;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionResponse;
import com.equinoxfitness.freezeservice.contract.sfdc.CreateCaseResponse;
import com.equinoxfitness.freezeservice.dao.FreezeServiceDAO;
import com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail;
import com.equinoxfitness.freezeservice.service.FreezeMosoService;
import com.equinoxfitness.freezeservice.service.SFDCService;
import com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl;
import com.equinoxfitness.freezeservice.utils.FacilityConversion;
import com.equinoxfitness.freezeservice.utils.FreezeServiceHelper;
import com.equinoxfitness.freezeservice.utils.MosoSessionMediatorForFreeze;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeMembershipServiceTest {

	@InjectMocks
	FreezeServiceImpl freezeServiceImpl;

	@Mock
	FreezeServiceDAO freezeServiceDAO;

	@Mock
	FreezeServiceHelper freezeHelper;

	@Mock
	FacilityConversion facilityConversion;

	@Mock
	SFDCService sfdcService;

	@Mock
	MosoSessionMediatorForFreeze mosoSessionMediator;

	@Mock
	FreezeMosoService mosoService;

	private FreezeMembershipInput freezeMembershipInput;
	private MemberAgreementDetail memberAgreementDetail;
	private CheckFreezeEligibilityInput checkFreezeEligibilityInput;
	private CheckFreezeEligiblityOutput checkFreezeEligiblityOutput;
	private GetSessionOutput sessionOutput;
	private Date startDate;
	private Date endDate;

	@SuppressWarnings("deprecation")
	@Before
	public void setUp() {

		// Test data
		freezeMembershipInput = new FreezeMembershipInput();
		startDate = new Date(2000, 11, 21);
		endDate = new Date(2001, 11, 21);
		freezeMembershipInput.setFacilityId("234");
		freezeMembershipInput.setMemberId("123");
		freezeMembershipInput.setFreezeReasonId("Pregnancy");
		freezeMembershipInput.setDurationMonths(2);
		freezeMembershipInput.setStartDate(startDate);
		freezeMembershipInput.setEndDate(endDate);

		// Member Agreement dummy data
		memberAgreementDetail = new MemberAgreementDetail();
		memberAgreementDetail.setAgreementId("4430853653");
		memberAgreementDetail.setMemberId("1003405683");
		memberAgreementDetail.setHomeFacilityId("111");

		// freeze eligibility dummy data
		checkFreezeEligibilityInput = new CheckFreezeEligibilityInput();
		checkFreezeEligibilityInput.setDurationMonths(2);
		checkFreezeEligibilityInput.setEndDate(endDate);
		checkFreezeEligibilityInput.setFreezeReason("Reason");
		checkFreezeEligibilityInput.setMemberId("Memberid");
		checkFreezeEligibilityInput.setRequestedDate(new Date());
		checkFreezeEligibilityInput.setStartDate(startDate);

		// freeze eligibility output dummy data
		checkFreezeEligiblityOutput = new CheckFreezeEligiblityOutput();

		// session dummy data
		sessionOutput = new GetSessionOutput();
		sessionOutput.setAuthTokenValue("6b3f6cc2-635f-4d32-bfb2-b60191403e39");
		sessionOutput.setCookieValue(
				"AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200");
	}

	@Test
	public void freezeMembershipTest_MemberDetailIsNull() throws Exception {
		freezeMembershipInput.setSource("b123");
		freezeMembershipInput.setSkipValidations(true);

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(Matchers.anyString())).thenReturn(null);
		FreezeMemberResponse freezeMemberResponse = freezeServiceImpl.freezeMembership(freezeMembershipInput);
		ExceptionMessage[] exceptionMessage = freezeMemberResponse.getMessages();

		assertEquals("output of freezeMemberResponse assertion", freezeMemberResponse.isRequestReceivedSuccessfully(),
				false);
		assertEquals("MemberAgreementDetail should not be null.", exceptionMessage[0].getErrorMessage());
	}

	@Test
	public void freezeMembershipTest_IfMemberIsNotEligibleForFreeze() throws Exception {
		freezeMembershipInput.setSource("b123");
		freezeMembershipInput.setSkipValidations(false);
		checkFreezeEligiblityOutput.setEligibleForFreeze(false);

		ExceptionMessage exceptionMessage = new ExceptionMessage();
		ExceptionMessage[] checkFreezeEligiblityExceptionMessage = new ExceptionMessage[1];
		exceptionMessage.setErrorMessage("Pending/Cancelled/Expired/On Hold Member are not eligible for freeze.");
		exceptionMessage.setMessageType("VALIDATION");
		checkFreezeEligiblityExceptionMessage[0] = exceptionMessage;

		checkFreezeEligiblityOutput.setMessages(checkFreezeEligiblityExceptionMessage);
		FreezeServiceImpl spy = Mockito.spy(freezeServiceImpl);

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMemberId()))
				.thenReturn(memberAgreementDetail);
		Mockito.when(freezeHelper.setCheckFreezeEligibilityInput(Matchers.any()))
				.thenReturn(checkFreezeEligibilityInput);
		Mockito.when(facilityConversion.facilityConversion(Matchers.anyString())).thenReturn("111");

		Mockito.doReturn(checkFreezeEligiblityOutput).when(spy).checkFreezeEligibility(checkFreezeEligibilityInput);

		FreezeMemberResponse freezeMemberResponse = spy.freezeMembership(freezeMembershipInput);
		ExceptionMessage[] actualExceptionMessage = freezeMemberResponse.getMessages();

		assertEquals("output of freezeMemberResponse assertion", freezeMemberResponse.isRequestReceivedSuccessfully(),
				false);
		assertEquals("Pending/Cancelled/Expired/On Hold Member are not eligible for freeze.",
				actualExceptionMessage[0].getErrorMessage());
	}

	@Test
	public void freezeMembershipTest_IfSuccessfullyCreateExceptionCaseInSF() throws Exception {
		freezeMembershipInput.setSource("b123");
		freezeMembershipInput.setSkipValidations(true);
		freezeMembershipInput.setFreezeReasonId("Pregnancy");
		freezeMembershipInput.setSource("Web");

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMemberId()))
				.thenReturn(memberAgreementDetail);
		Mockito.when(freezeHelper.setCheckFreezeEligibilityInput(Matchers.any()))
				.thenReturn(checkFreezeEligibilityInput);
		Mockito.when(facilityConversion.facilityConversion(Matchers.anyString())).thenReturn("111");
		Mockito.when(sfdcService.createCase(Matchers.anyObject(), Matchers.anyString(), Matchers.anyDouble()))
				.thenReturn(new CreateCaseResponse());
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
		.thenReturn(sessionOutput);

		FreezeMemberResponse freezeMemberResponse = freezeServiceImpl.freezeMembership(freezeMembershipInput);

		assertEquals(true, freezeMemberResponse.isRequestReceivedSuccessfully());
	}

	@Test
	public void freezeMembershipTest_IfFreezeReasonIdNull() throws Exception {
		freezeMembershipInput.setSource("b123");
		freezeMembershipInput.setSkipValidations(true);
		freezeMembershipInput.setWaveFreezeFee(true);
		freezeMembershipInput.setFreezeReasonId("Regular");

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMemberId()))
				.thenReturn(memberAgreementDetail);
		Mockito.when(freezeHelper.setCheckFreezeEligibilityInput(Matchers.any()))
				.thenReturn(checkFreezeEligibilityInput);
		Mockito.when(facilityConversion.facilityConversion(Matchers.anyString())).thenReturn("111");
		Mockito.when(freezeServiceDAO.getSuspensionReasonId(Matchers.anyString())).thenReturn(null);

		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
		.thenReturn(sessionOutput);
		FreezeMemberResponse freezeMemberResponse = freezeServiceImpl.freezeMembership(freezeMembershipInput);
		ExceptionMessage[] actualExceptionMessage = freezeMemberResponse.getMessages();
		assertEquals("Freeze reasonID is null", actualExceptionMessage[0].getErrorMessage());
	}

	@Test
	public void freezeMembershipTest_IfGetSessionFailed() throws Exception {
		freezeMembershipInput.setSource("b123");
		freezeMembershipInput.setSkipValidations(true);
		freezeMembershipInput.setWaveFreezeFee(true);
		freezeMembershipInput.setFreezeReasonId("Regular");

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMemberId()))
				.thenReturn(memberAgreementDetail);
		Mockito.when(freezeHelper.setCheckFreezeEligibilityInput(Matchers.any()))
				.thenReturn(checkFreezeEligibilityInput);
		Mockito.when(facilityConversion.facilityConversion(Matchers.anyString())).thenReturn("111");
		Mockito.when(freezeServiceDAO.getSuspensionReasonId(Matchers.anyString())).thenReturn("342");
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

		FreezeMemberResponse freezeMemberResponse = freezeServiceImpl.freezeMembership(freezeMembershipInput);
		ExceptionMessage[] actualExceptionMessage = freezeMemberResponse.getMessages();
		assertEquals("Get Session failed", actualExceptionMessage[0].getFriendlyMessage());
	}

	@Test
	public void freezeMembershipTest_IfFreezeMemberAgreementAPICallFailed() throws Exception {
		freezeMembershipInput.setSource("b123");
		freezeMembershipInput.setSkipValidations(true);
		freezeMembershipInput.setWaveFreezeFee(true);
		freezeMembershipInput.setFreezeReasonId("Regular");
		freezeMembershipInput.setDurationMonths(2);
		freezeMembershipInput.setEndDate(null);

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMemberId()))
				.thenReturn(memberAgreementDetail);
		Mockito.when(freezeHelper.setCheckFreezeEligibilityInput(Matchers.any()))
				.thenReturn(checkFreezeEligibilityInput);
		Mockito.when(facilityConversion.facilityConversion(Matchers.anyString())).thenReturn("111");
		Mockito.when(freezeServiceDAO.getSuspensionReasonId(Matchers.anyString())).thenReturn("342");
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		SuspensionRequest suspensionRequest = new SuspensionRequest();
		suspensionRequest.setAgreementId("AgreementId");
		suspensionRequest.setComments("Comments");
		suspensionRequest.setFreezeEndDate("Enddate");
		suspensionRequest.setFreezeReasonId(1);
		suspensionRequest.setFreezeStartDate("freezeDate");
		suspensionRequest.setRequestedDate("RequestedDate" + "");
		suspensionRequest.setRoleId(freezeMembershipInput.getMemberId());

		SuspensionResponse suspensionResponse = new SuspensionResponse();
		suspensionResponse.setMessage("FREEZE_MEMBERSHIP_FAILED");

		Mockito.when(freezeHelper.setSuspensionRequest(Mockito.anyString(), Mockito.any(MemberAgreementDetail.class),
				Mockito.any(FreezeMembershipInput.class))).thenReturn(suspensionRequest);
		Mockito.when((mosoService.freezeAgreement(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())))
				.thenReturn(suspensionResponse);

		FreezeMemberResponse freezeMemberResponse = freezeServiceImpl.freezeMembership(freezeMembershipInput);
		ExceptionMessage[] actualExceptionMessage = freezeMemberResponse.getMessages();
		assertEquals("Exception", actualExceptionMessage[0].getMessageType());
	}

	@Test
	public void freezeMembershipTest_SuccessScenario() throws Exception {
		freezeMembershipInput.setSource("b123");
		freezeMembershipInput.setSkipValidations(true);
		freezeMembershipInput.setWaveFreezeFee(true);
		freezeMembershipInput.setFreezeReasonId("Regular");

		Mockito.when(freezeServiceDAO.getMemberAgreementDetail(freezeMembershipInput.getMemberId()))
				.thenReturn(memberAgreementDetail);
		Mockito.when(freezeHelper.setCheckFreezeEligibilityInput(Matchers.any()))
				.thenReturn(checkFreezeEligibilityInput);
		Mockito.when(facilityConversion.facilityConversion(Matchers.anyString())).thenReturn("111");
		Mockito.when(freezeServiceDAO.getSuspensionReasonId(Matchers.anyString())).thenReturn("342");
		Mockito.when(mosoSessionMediator.getSession(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(sessionOutput);

		SuspensionRequest suspensionRequest = new SuspensionRequest();
		suspensionRequest.setAgreementId("AgreementId");
		suspensionRequest.setComments("Comments");
		suspensionRequest.setFreezeEndDate("Enddate");
		suspensionRequest.setFreezeReasonId(1);
		suspensionRequest.setFreezeStartDate("freezeDate");
		suspensionRequest.setRequestedDate("RequestedDate" + "");
		suspensionRequest.setRoleId(freezeMembershipInput.getMemberId());

		SuspensionResponse suspensionResponse = new SuspensionResponse();
		suspensionResponse.setFreezeFeeAmount(3463);
		Note note = new Note();

		Mockito.when(freezeHelper.setSuspensionRequest(Mockito.anyString(), Mockito.any(MemberAgreementDetail.class),
				Mockito.any(FreezeMembershipInput.class))).thenReturn(suspensionRequest);
		Mockito.when((mosoService.freezeAgreement(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())))
				.thenReturn(suspensionResponse);
		Mockito.when(freezeHelper.setnoteRequest(Mockito.anyString(), Mockito.anyString())).thenReturn(note);
		Mockito.when(mosoService.addNote(Mockito.any(GetSessionOutput.class), Mockito.any(HttpEntity.class),
				Mockito.any(Note.class))).thenReturn(note);
		Mockito.when(sfdcService.createCase(Mockito.any(FreezeMembershipInput.class), Mockito.anyString(),
				Mockito.anyDouble())).thenReturn(new CreateCaseResponse());

		FreezeMemberResponse freezeMemberResponse = freezeServiceImpl.freezeMembership(freezeMembershipInput);

		assertEquals(true, freezeMemberResponse.isRequestReceivedSuccessfully());

	}
}
