package com.equinoxfitness.freezeservice;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.io.InputStream;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.sfdc.AuthTokenData;
import com.equinoxfitness.freezeservice.contract.sfdc.Case;
import com.equinoxfitness.freezeservice.contract.sfdc.ContactData;
import com.equinoxfitness.freezeservice.contract.sfdc.CreateCaseResponse;
import com.equinoxfitness.freezeservice.service.impl.SFDCServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SFDCServiceTest {

	@InjectMocks
	SFDCServiceImpl sfdcService;

	@Mock
	ObjectMapper objectMapper;

	private FreezeMembershipInput freezeMembershipInput;

	@Before
	public void setup() {
		
		// Dummy input data
		freezeMembershipInput = new FreezeMembershipInput();
		freezeMembershipInput.setDurationMonths(2);
		freezeMembershipInput.setFacilityId("101");
		freezeMembershipInput.setMemberId("10023323");
		freezeMembershipInput.setFreezeReasonId("Regular");
		freezeMembershipInput.setSource("testSources");

		ReflectionTestUtils.setField(sfdcService, "authTokenPass", "Eqxci");
		ReflectionTestUtils.setField(sfdcService, "authTokenSecurityToken", "cilMZSUWpR7BaxeJ8yjPYhqq");
		ReflectionTestUtils.setField(sfdcService, "authTokenUserName", "<EMAIL>");
		ReflectionTestUtils.setField(sfdcService, "authTokenLoginUrl",
				"https://test.salesforce.com/services/oauth2/token?grant_type=password");
		ReflectionTestUtils.setField(sfdcService, "authTokenClientId",
				"3MVG9sLbBxQYwWqtI3hcR1Odn1.1LZsLLCt4RmawwxtWVG2nzdpNa64nW.DmZ4l3sbiXzUeXEWKMLcUqnHPxV");
		ReflectionTestUtils.setField(sfdcService, "authTokenClientSecret", "5584639974805146944");	
	}

	@Test
	public void createCase_ifFailedToGetToken() throws Exception {
		SFDCServiceImpl spy = Mockito.spy(sfdcService);

		Mockito.doReturn(null).when(spy).getSfdcSecurityToken();
		CreateCaseResponse createResponse = spy.createCase(freezeMembershipInput, "2113", 30.0);
		assertNull(createResponse);
	}

	@Test
	public void getClubId_ifFailedToGetToken() throws Exception {
		SFDCServiceImpl spy = Mockito.spy(sfdcService);
		Mockito.doReturn(null).when(spy).getSfdcSecurityToken();
		String response = spy.getClubId("111");
		assertEquals("", response);
	}

	@Test
	public void getContactId_ifFailedToGetToken() throws Exception {
		SFDCServiceImpl spy = Mockito.spy(sfdcService);
		Mockito.doReturn(null).when(spy).getSfdcSecurityToken();
		String response = spy.getContactId("1003254363");
		assertEquals("", response);
	}

	@Test
	public void getContactId_ExceptionCase() throws Exception {
		AuthTokenData token = new AuthTokenData();
		token.setAccessToken("984hdsfj34543");
		token.setId("3532");
		token.setInstanceUrl("https://testInstaceUrl/");
		token.setIssuedAt("testIssue");
		token.setSignature("testSignature");
		token.setTokenType("testTokenType");
	
		SFDCServiceImpl spy = Mockito.spy(sfdcService);
		Mockito.doReturn(token).when(spy).getSfdcSecurityToken();

		Mockito.doReturn(null).when(objectMapper).readValue(Mockito.any(InputStream.class),
				Mockito.eq(ContactData.class));
		String response = spy.getContactId("1003254363");
		assertEquals("", response);
	}
	
	@Test
	public void getClubId_ExceptionCase() throws Exception {
		
		AuthTokenData token = new AuthTokenData();
		token.setAccessToken("984hdsfj34543");
		token.setId("3532");
		token.setInstanceUrl("https://testInstaceUrl/");
		token.setIssuedAt("testIssue");
		token.setSignature("testSignature");
		token.setTokenType("testTokenType");
		
		SFDCServiceImpl spy = Mockito.spy(sfdcService);
		Mockito.doReturn(token).when(spy).getSfdcSecurityToken();

		Mockito.doReturn(token).when(objectMapper).readValue(Mockito.any(InputStream.class),
				Mockito.eq(ContactData.class));
		String response = spy.getClubId("111");
		assertEquals("", response);
	}
	
	@Test
	public void getSfdcSecurityToken_ExceptionCase() throws Exception {
		AuthTokenData token = sfdcService.getSfdcSecurityToken();
		assertNull(token);
	}

	@Test
	public void createCase_ExceptionCase() throws Exception {
		
		AuthTokenData token = new AuthTokenData();
		token.setAccessToken("984hdsfj34543");
		token.setId("3532");
		token.setInstanceUrl("https://testInstaceUrl/");
		token.setIssuedAt("testIssue");
		token.setSignature("testSignature");
		token.setTokenType("testTokenType");
		
		SFDCServiceImpl spy = Mockito.spy(sfdcService);
		Mockito.doReturn(token).when(spy).getSfdcSecurityToken();
		Mockito.doReturn("testClub").when(spy).getClubId(Mockito.anyString());
		Mockito.doReturn("testContactId").when(spy).getContactId(Mockito.anyString());
		
		Mockito.doReturn(token).when(objectMapper).readValue(Mockito.any(InputStream.class),
				Mockito.eq(ContactData.class));
		CreateCaseResponse response = spy.createCase(freezeMembershipInput, "2113", 30.0);
		assertNull(response);
	}
}
