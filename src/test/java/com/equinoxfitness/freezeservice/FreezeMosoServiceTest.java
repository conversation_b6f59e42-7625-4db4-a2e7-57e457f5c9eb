package com.equinoxfitness.freezeservice;

import java.nio.charset.Charset;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import org.junit.Assert;
import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.equinoxfitness.common.moso.contract.GetSessionOutput;
import com.equinoxfitness.commons.output.LogResponse;
import com.equinoxfitness.commons.service.EventsService;
import com.equinoxfitness.freezeservice.contract.FreezeExtensionInput;
import com.equinoxfitness.freezeservice.contract.moso.FreezeReasonResponse;
import com.equinoxfitness.freezeservice.contract.moso.InvoiceConfigValues;
import com.equinoxfitness.freezeservice.contract.moso.Item;
import com.equinoxfitness.freezeservice.contract.moso.Note;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionRequest;
import com.equinoxfitness.freezeservice.contract.moso.SuspensionResponse;
import com.equinoxfitness.freezeservice.contract.moso.TaxRate;
import com.equinoxfitness.freezeservice.contract.moso.invoice.Invoice;
import com.equinoxfitness.freezeservice.contract.moso.invoice.NewFinalizeInvoice;
import com.equinoxfitness.freezeservice.service.FreezeMosoService;
import com.equinoxfitness.freezeservice.service.impl.FreezeMosoServiceImpl;
import com.equinoxfitness.freezeservice.utils.MosoSessionMediatorForFreeze;;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FreezeMosoServiceTest {

	@InjectMocks
	FreezeMosoService freezeMosoService = new FreezeMosoServiceImpl();

	@Mock
	RestTemplate restTemplate;

	@Mock
	MosoSessionMediatorForFreeze mosoSessionMediator;
	
	@Mock
	EventsService eventService;

	private GetSessionOutput sessionOut;
	private HttpHeaders headers;
	private HttpEntity<HttpHeaders> entity;
	private FreezeReasonResponse freezeReason;

	@Before
	public void setUp() {
		// dummy session data
		sessionOut = new GetSessionOutput();
		sessionOut.setCustomFiled("field");
		sessionOut.setAuthTokenValue("value");
		sessionOut.setCookieValue("cookieValue");
		headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Cookie", "cookie");

		entity = new HttpEntity<>(headers);

		freezeReason = new FreezeReasonResponse();
		freezeReason.setObligationEffect("effects");
		freezeReason.setSuspensionReasonID(2);
		freezeReason.setSuspensionReasonDescription("Medical");
	}

	@Test
	public void getFreezeDataTest_validCase() throws Exception {

		FreezeReasonResponse[] freezeReasonResponse = { freezeReason };

		ResponseEntity<FreezeReasonResponse[]> response = Mockito.mock(ResponseEntity.class);

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(response);

		Mockito.when(response.getBody()).thenReturn(freezeReasonResponse);
		FreezeReasonResponse result = freezeMosoService.getFreezeData("Regular", 2, sessionOut, entity);
		assertEquals("Medical", result.getSuspensionReasonDescription());
	}

	@Test
	public void getFreezeData_invalidCase() {

		ResponseEntity<FreezeReasonResponse[]> response = Mockito.mock(ResponseEntity.class);

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.<HttpMethod>eq(HttpMethod.GET),
				Mockito.<HttpEntity<?>>any(), Mockito.<Class<FreezeReasonResponse[]>>any(),
				Mockito.<Map<String, String>>any()))
				.thenThrow(new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR, null, null,
						("{\"Message\":\"Moso API error\"}").getBytes(),
						Charset.defaultCharset()));

		FreezeReasonResponse result = freezeMosoService.getFreezeData("Regular", 2, sessionOut, entity);
		assertEquals(true, result.getMessage().contains("Moso API"));
	}

	@Test
	public void itemSearchTest_validCase() {
		Item itemobj = new Item();
		itemobj.setId(101);
		Item[] item = { itemobj };

		ResponseEntity<Item[]> response = Mockito.mock(ResponseEntity.class);
		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(response);
		Mockito.when(response.getBody()).thenReturn(item);
		Item itemResponse = freezeMosoService.itemSearch("itemName", sessionOut, entity);
		assertEquals(101, itemResponse.getId(), 0);
	}

	@Test
	public void itemSearchTest_IfItemMosoApiResponseNull() {
		Item itemobj = new Item();
		itemobj.setId(101);
		Item[] item = { itemobj };

		ResponseEntity<Item[]> response = Mockito.mock(ResponseEntity.class);

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(null);

		Mockito.when(response.getBody()).thenReturn(item);
		Item itemResponse = freezeMosoService.itemSearch("itemName", sessionOut, entity);
		assertEquals("Item API response null", itemResponse.getMessage());
	}

	@Test
	public void itemSearchTest_IfItemMosoApiThrowException() {
		Item itemobj = new Item();
		itemobj.setId(101);

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap()))
				.thenThrow(new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR, null, null,
						("{\"Message\":\"Moso API error\"}").getBytes(),
						Charset.defaultCharset()));

		Item itemResponse = freezeMosoService.itemSearch("itemName", sessionOut, entity);
		assertEquals(true, itemResponse.getMessage().contains("Moso API"));
	}

	@Test
	public void getTaxRateTest_validCase() {
		InvoiceConfigValues invoiceValues = new InvoiceConfigValues();
		TaxRate taxRate = new TaxRate();
		taxRate.setId(1);

		TaxRate[] taxRates = { taxRate };
		invoiceValues.setTaxRates(taxRates);

		ResponseEntity<InvoiceConfigValues> response = Mockito.mock(ResponseEntity.class);
		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(response);
		Mockito.when(response.getBody()).thenReturn(invoiceValues);
		InvoiceConfigValues invoiceValuesResponse = freezeMosoService.getTaxRate("BU", "BU_Type", sessionOut, entity);
		assertEquals(null, invoiceValuesResponse.getMessage());
	}

	@Test
	public void getTaxRateTest_ifMosoAPIResponseNull() {
		InvoiceConfigValues invoiceValues = new InvoiceConfigValues();
		TaxRate[] taxRates = {};
		invoiceValues.setTaxRates(taxRates);

		ResponseEntity<InvoiceConfigValues> response = Mockito.mock(ResponseEntity.class);

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(response);
		Mockito.when(response.getBody()).thenReturn(invoiceValues);
		InvoiceConfigValues invoiceValuesResponse = freezeMosoService.getTaxRate("BU", "BU_Type", sessionOut, entity);
		assertEquals("Item API response null", invoiceValuesResponse.getMessage());
	}

	@Test
	public void getTaxRateTest_ifMosoAPIThrowException() {
		InvoiceConfigValues invoiceValues = new InvoiceConfigValues();
		TaxRate[] taxRates = {};
		invoiceValues.setTaxRates(taxRates);

		ResponseEntity<InvoiceConfigValues> response = Mockito.mock(ResponseEntity.class);
		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap()))
				.thenThrow(new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR, null, null,
						("{\"Message\":\"Moso API error\"}").getBytes(),
						Charset.defaultCharset()));

		Mockito.when(response.getBody()).thenReturn(invoiceValues);
		InvoiceConfigValues invoiceValuesResponse = freezeMosoService.getTaxRate("BU", "BU_Type", sessionOut, entity);
		assertEquals(true, invoiceValuesResponse.getMessage().contains("Moso API"));
	}

	@Test
	public void createAndfinalizeInvoice_ifMosoAPIThrowException() {
		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		newFinalizeInvoice.setAccountId(324242);

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap()))
				.thenThrow(new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR, null, null,
						("{\"Message\":\"Moso API error\"}").getBytes(),
						Charset.defaultCharset()));

		Mockito.when(eventService.logEventsInDynamoTrace(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), 
				Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.any(), Mockito.anyLong())).thenReturn(new LogResponse());
		Invoice result = freezeMosoService.createAndfinalizeInvoice(newFinalizeInvoice, sessionOut, entity, "1234", 1, "115");
		assertEquals(true, result.getMessage().contains("Moso API"));
	}

	@Test
	public void createAndfinalizeInvoice_validCase() {
		NewFinalizeInvoice newFinalizeInvoice = new NewFinalizeInvoice();
		newFinalizeInvoice.setAccountId(324242);

		Invoice invoice = new Invoice();
		invoice.setId(101);
		invoice.setInvoiceStatus("Active");

		ResponseEntity<Invoice> response = Mockito.mock(ResponseEntity.class);
		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(response);
		Mockito.when(response.getBody()).thenReturn(invoice);
		Mockito.when(eventService.logEventsInDynamoTrace(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), 
				Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.any(), Mockito.anyLong())).thenReturn(new LogResponse());
		Invoice result = freezeMosoService.createAndfinalizeInvoice(newFinalizeInvoice, sessionOut, entity, "1234", 1, "115");
		assertEquals("Active", result.getInvoiceStatus());
	}

	@Test
	public void freezeAgreement_ifMosoAPIThrowException() {
		String correlationId = "123-3455-2344";
		String accountCode = "1234";
		int countryCode = 1;
		SuspensionRequest suspensionRequest = new SuspensionRequest();
		suspensionRequest.setAgreementId("*********");

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap()))
				.thenThrow(new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR, null, null,
						("{\"Message\":\"Moso API error\"}").getBytes(),
						Charset.defaultCharset()));
		Mockito.when(eventService.logEventsInDynamoTrace(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), 
				Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.any(), Mockito.anyLong())).thenReturn(new LogResponse());
		SuspensionResponse result = freezeMosoService.freezeAgreement(suspensionRequest, entity, correlationId, accountCode, countryCode);
		assertEquals(true, result.getMessage().contains("Moso API"));
	}

	@Test
	public void freezeAgreement_validCase() {
		String correlationId = "123-3455-2344";
		SuspensionRequest suspensionRequest = new SuspensionRequest();
		suspensionRequest.setAgreementId("*********");
		String accountCode = "1234";
		int countryCode = 1;
		SuspensionResponse[] suspensionResponse = new SuspensionResponse[1];
		suspensionResponse[0] = new SuspensionResponse();
		suspensionResponse[0].setFreezeFeeAmount(300);
		suspensionResponse[0].setMemberFirstName("Rio");

		ResponseEntity<SuspensionResponse[]> response = Mockito.mock(ResponseEntity.class);
		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(response);
		Mockito.when(response.getBody()).thenReturn(suspensionResponse);
		Mockito.when(eventService.logEventsInDynamoTrace(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), 
				Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.any(), Mockito.anyLong())).thenReturn(new LogResponse());
		SuspensionResponse result = freezeMosoService.freezeAgreement(suspensionRequest, entity, correlationId, accountCode, countryCode);
		assertEquals(300, result.getFreezeFeeAmount());
	}

	@Test
	public void addNote_ifMosoAPIThrowException() {
		Note note = new Note();
		note.setContactTypeId(347528);

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap()))
				.thenThrow(new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR, null, null,
						("{\"Message\":\"Moso API error\"}").getBytes(), 
						Charset.defaultCharset()));

		Note result = freezeMosoService.addNote(sessionOut, entity, note);
		assertEquals(true, result.getMessage().contains("Moso API"));
	}

	@Test
	public void addNote_validCase() {
		Note note = new Note();
		note.setContactTypeId(347528);

		ResponseEntity<Note> response = Mockito.mock(ResponseEntity.class);

		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(response);
		Mockito.when(response.getBody()).thenReturn(note);
		Note result = freezeMosoService.addNote(sessionOut, entity, note);
		assertEquals(347528, result.getContactTypeId(), 0);
	}

	@Test
	public void freezeExtension_validCase() throws ParseException {
		FreezeExtensionInput freezeExtensionInput = new FreezeExtensionInput();
		freezeExtensionInput.setFreezeReason("Regular");
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		String dateInString = "2019-03-07T09:52:59.145Z";
		Date date = formatter.parse(dateInString);
		freezeExtensionInput.setFreezeExtensionEndDate(date);

		SuspensionResponse suspensionResponse = new SuspensionResponse();
		suspensionResponse.setFreezeFeeAmount(300);
		suspensionResponse.setMemberFirstName("Rio");

		ResponseEntity<SuspensionResponse> response = Mockito.mock(ResponseEntity.class);
		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap())).thenReturn(response);
		Mockito.when(response.getBody()).thenReturn(suspensionResponse);
		SuspensionResponse result = freezeMosoService.freezeExtension(freezeExtensionInput, sessionOut, entity, "1332",
				new java.sql.Date(System.currentTimeMillis()));
		assertEquals(300, result.getFreezeFeeAmount());
	}

	@Test
	public void freezeExtension_ifMosoAPIThrowException() throws ParseException {
		FreezeExtensionInput freezeExtensionInput = new FreezeExtensionInput();
		freezeExtensionInput.setFreezeReason("Regular");
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		String dateInString = "2019-03-07T09:52:59.145Z";
		Date date = formatter.parse(dateInString);
		freezeExtensionInput.setFreezeExtensionEndDate(date);

		ResponseEntity<SuspensionResponse> response = Mockito.mock(ResponseEntity.class);
		Mockito.when(restTemplate.exchange(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(Class.class),
				Mockito.anyMap()))
				.thenThrow(new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR, null, null,
						("{\"Message\":\"Moso API error\"}").getBytes(), 
						Charset.defaultCharset()));

		SuspensionResponse result = freezeMosoService.freezeExtension(freezeExtensionInput, sessionOut, entity, "1332",
				new java.sql.Date(System.currentTimeMillis()));
		assertEquals(true, result.getMessage().contains("Moso API"));
		;
	}

	@Test
	public void getSession_ifFailedToGetMosoSession() {
		Mockito.when(mosoSessionMediator.getSession(Mockito.anyInt(), Mockito.anyString(), Mockito.anyString()))
				.thenReturn(null);
		HttpEntity<HttpHeaders> result = freezeMosoService.getSession(101, "111", "45365lk343l253");
		Assert.assertNull(result);
	}

	@Test
	public void getSession_validCase() {
		Mockito.when(mosoSessionMediator.getSession(Mockito.anyInt(), Mockito.anyString(), Mockito.anyString()))
				.thenReturn(sessionOut);
		HttpEntity<HttpHeaders> result = freezeMosoService.getSession(101, "111", "45365lk343l253");
		Assert.assertNotNull(result.getHeaders());
	}
}
