package com.equinoxfitness.freezeservice.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Date;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.equinoxfitness.commons.exception.ExceptionMessage;
import com.equinoxfitness.freezeservice.FreezeServiceApplication;
import com.equinoxfitness.freezeservice.contract.FreezeMemberResponse;
import com.equinoxfitness.freezeservice.contract.FreezeMembershipInput;
import com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput;
import com.equinoxfitness.freezeservice.service.FreezeService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
@ContextConfiguration(classes = FreezeServiceApplication.class)
public class FreezeServiceControllerTest {

	@InjectMocks
	private FreezeServiceController freezeServiceController = new FreezeServiceController();

	@Autowired
	private MockMvc mockmvc;

	@Autowired
	private WebApplicationContext webApplicationContext;

	@MockBean
	FreezeService freezeService;

	private RetrieveFreezeReasonOutput response;
	private FreezeMemberResponse freezeMemberResponse;
	private FreezeMembershipInput freezeMembershipInput;
	private ObjectMapper objectMapper;
	private ExceptionMessage message;

	@Before
	public void setUp() throws JsonProcessingException {
		mockmvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
		response = new RetrieveFreezeReasonOutput();
		response.setOneTimeFee(1.0);
		response.setSuspensionReasonName("TestReasonName");

		freezeMemberResponse = new FreezeMemberResponse();
		freezeMemberResponse.setRequestReceivedSuccessfully(true);

		freezeMembershipInput = new FreezeMembershipInput();
		Date startDate = new Date(2000, 11, 21);
		Date endDate = new Date(2001, 11, 21);

		freezeMembershipInput.setFacilityId("234");
		freezeMembershipInput.setMemberId("123");
		freezeMembershipInput.setFreezeReasonId("1234");
		freezeMembershipInput.setDurationMonths(2);
		freezeMembershipInput.setStartDate(startDate);
		freezeMembershipInput.setEndDate(endDate);
		freezeMembershipInput.setSource("source");
		objectMapper = new ObjectMapper();
		objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
		message = new ExceptionMessage();
		message.setMessageID(1);
	}

	@Test
	public void retrieveFreezeReasonTest() throws Exception {

		Mockito.when(freezeService.retrieveFreezeReason(Matchers.anyString(), Matchers.anyString(), Matchers.anyInt()))
				.thenReturn(response);
		mockmvc.perform(MockMvcRequestBuilders.get("/v1/freeze-info/{mosoMemberId}/", "1000639346")
				.param("freezeReason", "Regular").param("duration", "2").contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().isOk());
	}

	@Test
	public void retrieveFreezeReasonTest_memeberIDasnull() throws Exception {
		Mockito.when(freezeService.retrieveFreezeReason(Matchers.anyString(), Matchers.anyString(), Matchers.anyInt()))
				.thenReturn(response);
		mockmvc.perform(MockMvcRequestBuilders.get("/v1/freeze-info/{mosoMemberId}/", " ")
				.param("freezeReason", "Regular").param("duration", "2").contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void retrieveFreezeReasonTest_freezereasonasnull() throws Exception {
		Mockito.when(freezeService.retrieveFreezeReason(Matchers.anyString(), Matchers.anyString(), Matchers.anyInt()))
				.thenReturn(response);
		mockmvc.perform(MockMvcRequestBuilders.get("/v1/freeze-info/{mosoMemberId}/", "1000639346")
				.param("freezeReason", " ").param("duration", "2").contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void retrieveFreezeReasonTest_durationasnull() throws Exception {
		Mockito.when(freezeService.retrieveFreezeReason(Matchers.anyString(), Matchers.anyString(), Matchers.anyInt()))
				.thenReturn(response);
		mockmvc.perform(MockMvcRequestBuilders.get("/v1/freeze-info/{mosoMemberId}/", "1000639346")
				.param("freezeReason", "Regular").param("duration", "0").contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void retrieveFreezeReasonTest_facilityIdnull() throws Exception {
		Mockito.when(freezeService.retrieveFreezeReason(Matchers.anyString(), Matchers.anyString(), Matchers.anyInt()))
				.thenReturn(response);
		mockmvc.perform(MockMvcRequestBuilders.get("/v1/freeze-info/{mosoMemberId}/", "1000639346")
				.param("freezeReason", "Regular").param("duration", "0").contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void freezeMembershipTest() throws Exception {

		String arrayToJson = objectMapper.writeValueAsString(freezeMembershipInput);
		Mockito.when(freezeService.freezeMembership(Matchers.any())).thenReturn(freezeMemberResponse);
		this.mockmvc
				.perform(post("/v1/freeze-membership/").contentType(MediaType.APPLICATION_JSON).content(arrayToJson))
				.andExpect(status().isOk());
	}

	@Test
	public void freezeMembershipTest_MemberIdnull() throws Exception {
		freezeMembershipInput.setMemberId(null);
		String arrayToJson = objectMapper.writeValueAsString(freezeMembershipInput);
		Mockito.when(freezeService.freezeMembership(Matchers.any())).thenReturn(freezeMemberResponse);
		this.mockmvc
				.perform(post("/v1/freeze-membership/").contentType(MediaType.APPLICATION_JSON).content(arrayToJson))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void freezeMembershipTest_ReasonIdnull() throws Exception {
		freezeMembershipInput.setFreezeReasonId(null);
		String arrayToJson = objectMapper.writeValueAsString(freezeMembershipInput);
		Mockito.when(freezeService.freezeMembership(Matchers.any())).thenReturn(freezeMemberResponse);
		this.mockmvc
				.perform(post("/v1/freeze-membership/").contentType(MediaType.APPLICATION_JSON).content(arrayToJson))
				.andExpect(status().is4xxClientError());
	}

	// @Test
	public void freezeMembershipTest_durationnull() throws Exception {
		freezeMembershipInput.setDurationMonths(0);
		String arrayToJson = objectMapper.writeValueAsString(freezeMembershipInput);
		Mockito.when(freezeService.freezeMembership(Matchers.any())).thenReturn(freezeMemberResponse);
		this.mockmvc
				.perform(post("/v1/freeze-membership/").contentType(MediaType.APPLICATION_JSON).content(arrayToJson))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void freezeMembershipTest_startDatenull() throws Exception {
		freezeMembershipInput.setStartDate(null);
		String arrayToJson = objectMapper.writeValueAsString(freezeMembershipInput);
		Mockito.when(freezeService.freezeMembership(Matchers.any())).thenReturn(freezeMemberResponse);
		this.mockmvc
				.perform(post("/v1/freeze-membership/").contentType(MediaType.APPLICATION_JSON).content(arrayToJson))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void freezeMembershipTest_endDatenull() throws Exception {
		freezeMembershipInput.setEndDate(null);
		freezeMembershipInput.setDurationMonths(0);
		String arrayToJson = objectMapper.writeValueAsString(freezeMembershipInput);
		Mockito.when(freezeService.freezeMembership(Matchers.any())).thenReturn(freezeMemberResponse);
		this.mockmvc
				.perform(post("/v1/freeze-membership/").contentType(MediaType.APPLICATION_JSON).content(arrayToJson))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void freezeMembershipTest_sourcenull() throws Exception {
		freezeMembershipInput.setSource(null);
		String arrayToJson = objectMapper.writeValueAsString(freezeMembershipInput);
		Mockito.when(freezeService.freezeMembership(Matchers.any())).thenReturn(freezeMemberResponse);
		this.mockmvc
				.perform(post("/v1/freeze-membership/").contentType(MediaType.APPLICATION_JSON).content(arrayToJson))
				.andExpect(status().is4xxClientError());
	}

	@Test
	public void freezeMembershipTest_messagesnull() throws Exception {
		String arrayToJson = objectMapper.writeValueAsString(freezeMembershipInput);

		ExceptionMessage[] messages = { message };
		freezeMemberResponse.setMessages(messages);
		Mockito.when(freezeService.freezeMembership(Matchers.any())).thenReturn(freezeMemberResponse);
		this.mockmvc
				.perform(post("/v1/freeze-membership/").contentType(MediaType.APPLICATION_JSON).content(arrayToJson))
				.andExpect(status().is4xxClientError());
	}
}
