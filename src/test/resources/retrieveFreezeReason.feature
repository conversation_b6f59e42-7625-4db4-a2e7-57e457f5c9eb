@tag
Feature: This is used to obtain suspension reason name and one time freeze fee 
         based on duration for which freeze is applied
 
@tag1
 Scenario: Valid request
		   		        Given The input mosoMemberId as "1003224259"
		   		        And the freezeReason with the value "Regular"
		   		        And the input duration as 1
		              When client makes call to GET "/v1/freeze-info/{mosoMemberId}/"
		             Then the output RetrieveFreezeReasonOutput is success
		              
		              
@tag2
 Scenario: Invalid request with mosoMemberId having null value
		   		        Given The input mosoMemberId as null value
		   		        And the freezeReason with the value "Regular"
		   		        And the input duration as 1
		              When client makes call to GET "/v1/freeze-info/{mosoMemberId}/"
		             Then the output RetrieveFreezeReasonOutput is not success
					       And RetrieveFreezeReasonOutput error message is MEMBER_ID_REQUIRED
					       
					       
@tag3
 Scenario: Invalid request with freezeReason having null value
		   		         Given The input mosoMemberId as "1000639346"
		   		        And the freezeReason having null value
		   		        And the input duration as 1
		              When client makes call to GET "/v1/freeze-info/{mosoMemberId}/"
		             Then the output RetrieveFreezeReasonOutput is not success
					       And RetrieveFreezeReasonOutput error message is FREEZE_REASON_BLANK
		
@tag4
 Scenario: Invalid request with duration having null value
		   		        Given The input mosoMemberId as "1000639346"
		   		        And the freezeReason with the value "Regular"
		   		        And The input duration having null value
		              When client makes call to GET "/v1/freeze-info/{mosoMemberId}/"
		             Then the output RetrieveFreezeReasonOutput is not success
					       And RetrieveFreezeReasonOutput error message is FREEZE_DURATION_REQUIRED

@tag5
 Scenario: valid request but member agreement is not found
		   		        Given The input mosoMemberId as "100063934"
		   		        And the freezeReason with the value "Regular"
		   		        And the input duration as 1
		              When client makes call to GET "/v1/freeze-info/{mosoMemberId}/"
		             Then the output RetrieveFreezeReasonOutput is not success
					       And RetrieveFreezeReasonOutput error message is MemberAgreementDetail should not be null.	
					       