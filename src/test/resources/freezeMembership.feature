@tag
Feature: 
 		             
@tag1
 Scenario: InValid request with MemberId having null value
		   		        Given The input MemberId is having null value
		   		        And the FreezeReasonId with the value Regular
		   		        And the input DurationMonths as 1
		   		        And the input StartDate as 2019-01-08T05:41:15.471Z
		   		        And the input EndDate as 2019-02-09T05:41:15.471Z
		   		        And the input Source as Web
		   		        And the input FacilityId as 252
		   						And thie input isSkipValidations as true
		   		       When client makes call to POST /v1/freeze-membership/
		   		       Then the output freezeMembership is not success
		             And freezeMembership error message is MEMBER_ID_REQUIRED
		             
		             
@tag2
 Scenario: InValid request with FreezeReasonId having null value
		   		        Given The input MemberId as 1002845724
		   		        And the FreezeReasonId is having null value
		   		        And the input DurationMonths as 1
		   		        And the input StartDate as 2019-01-08T05:41:15.471Z
		   		        And the input EndDate as 2019-02-09T05:41:15.471Z
		   		        And the input Source as Web
		   		        And the input FacilityId as 252
		   						And thie input isSkipValidations as true
		   		       When client makes call to POST /v1/freeze-membership/
		   		       Then the output freezeMembership is not success
		             And freezeMembership error message is FREEZE_REASON_BLANK
		         
@tag3
 Scenario: InValid request with startDate having null value
		   		        Given The input MemberId as 1002845724
		   		        And the FreezeReasonId with the value Regular
		   		        And the input DurationMonths as 1
		   		        And the input StartDate having null value
		   		        And the input EndDate as 2019-02-09T05:41:15.471Z
		   		        And the input Source as Web
		   		        And the input FacilityId as 252
		   						And thie input isSkipValidations as true
		   		       When client makes call to POST /v1/freeze-membership/
		   		       Then the output freezeMembership is not success
		             And freezeMembership error message is START_DATE_EMPTY		
		             
		             
@tag4
 Scenario: InValid request with endDate having null value
		   		        Given The input MemberId as 1002845724
		   		        And the FreezeReasonId with the value Regular
		   		        And the input DurationMonths as 0
		   		        And the input StartDate as 2019-01-08T05:41:15.471Z
		   		        And the input EndDate having null value
		   		        And the input Source as Web
		   		        And the input FacilityId as 252
		   						And thie input isSkipValidations as true
		   		       When client makes call to POST /v1/freeze-membership/
		   		       Then the output freezeMembership is not success
		             And freezeMembership error message is END_DATE_AND_MONTH_DURATION_BLANK		        	                    