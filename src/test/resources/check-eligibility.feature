@tag
Feature: freeze-eligible service integration test
  
 
 @tag1
  Scenario: Request when member agreement detail is not present in tenant database
    Given the input memberId as "1234"
    And the input durationDays as 0
    And the input durationMonths as 2
    And the input endDate as "2018-12-22T07:20:19.920Z"
    And the input freezeReason as "Regular"
    And the input requestedDate as "2018-12-20T07:20:19.920Z"
    And the input startDate as "2018-12-20T07:20:19.920Z"
    When client makes call to POST v1/freeze-eligible
    Then the client receives status as failure
		Then FreezeEligibilityOutput error message is MemberAgreementDetail should not be null.
		
		@tag2
  Scenario: Request when member is corporate member
    Given the input memberId as "1003268500"
    And the input durationDays as 0
    And the input durationMonths as 2
    And the input endDate as "2018-12-22T07:20:19.920Z"
    And the input freezeReason as "Regular"
    And the input requestedDate as "2018-12-20T07:20:19.920Z"
    When client makes call to POST v1/freeze-eligible
    Then the client receives status as failure
		Then FreezeEligibilityOutput error message is Corporate Members are not allowed to freeze
		
		 @tag3
  Scenario: Request when member is Equinox Friends and Family Member
    Given the input memberId as "1003834668"
    And the input durationDays as 0
    And the input durationMonths as 2
    And the input endDate as "2018-12-22T07:20:19.920Z"
    And the input freezeReason as "Regular"
    And the input requestedDate as "2018-12-20T07:20:19.920Z"
    And the input startDate as "2018-12-20T07:20:19.920Z"
    When client makes call to POST v1/freeze-eligible
    Then the client receives status as failure
		Then FreezeEligibilityOutput error message is Equinox Friends and Family Members are not allowed to freeze.
		
		 @tag4
  Scenario: Request when member agreement is expired
    Given the input memberId as "1000071296"
    And the input durationDays as 0
    And the input durationMonths as 2
    And the input endDate as "2018-12-22T07:20:19.920Z"
    And the input freezeReason as "Regular"
    And the input requestedDate as "2018-12-20T07:20:19.920Z"
    And the input startDate as "2018-12-20T07:20:19.920Z"
    When client makes call to POST v1/freeze-eligible
    Then the client receives status as failure
		Then FreezeEligibilityOutput error message is Pending/Cancelled/Expired/On Hold Member are not eligible for freeze.
		
		 @tag5
  Scenario: Request when member has Short term agreement
    Given the input memberId as "1002938816"
    And the input durationDays as 0
    And the input durationMonths as 2
    And the input endDate as "2018-12-22T07:20:19.920Z"
    And the input freezeReason as "Regular"
    And the input requestedDate as "2018-12-20T07:20:19.920Z"
    And the input startDate as "2018-12-20T07:20:19.920Z"
    When client makes call to POST v1/freeze-eligible
    Then the client receives status as failure
		Then FreezeEligibilityOutput error message is Pending/Cancelled/Expired/On Hold Member are not eligible for freeze.
		
		@tag6
  Scenario: valid request
    Given the input memberId as "1003721372"
    And the input durationDays as 0
    And the input durationMonths as 2
    And the input endDate as "2018-12-22T07:20:19.920Z"
    And the input freezeReason as "Regular"
    And the input requestedDate as "2018-12-20T07:20:19.920Z"
    And the input startDate as "2018-12-20T07:20:19.920Z"
    When client makes call to POST v1/freeze-eligible
    Then the output FreezeEligibilityOutput is success
    
    @tag7
  Scenario: Invalid request when member id is null
    Given the input memberId as ""
    And the input durationDays as 0
    And the input durationMonths as 2
    And the input endDate as "2018-12-22T07:20:19.920Z"
    And the input freezeReason as "Regular"
    And the input requestedDate as "2018-12-20T07:20:19.920Z"
    And the input startDate as "2018-12-20T07:20:19.920Z"
    When client makes call to POST v1/freeze-eligible
    Then the client receives status as failure
    Then FreezeEligibilityOutput error message is MEMBER_ID_REQUIRED