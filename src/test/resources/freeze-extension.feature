@tag
Feature: freeze-extension service integration test

 @tag1
  Scenario: Request when required parameter memberid is empty
   	Given the input memberId  as ""
    And the input facilityId  as "252"
    And the input freezeReason as "Regular" 	
    And the input freezeExtensionEndDate as "2019-03-07T09:52:59.145Z"
    And the input extensionDurationMonths as "1"
		And the input waiveOffExtensionFee as "true"
    And the input notesType as "testNoteType"
    And the input notes as "testNotes"
    And the input contactType as "Phone"
		
		When client makes call to POST v1/freeze-extension/
    Then the client receives status failure
		And FreezeExtensionOutput error message is MEMBER_ID_REQUIRED	
		
@tag2
  Scenario: Request when MemberAgreementDetail is not present in database
   	Given the input memberId  as "12345613"
    And the input facilityId  as "252"
    And the input freezeReason as "Regular" 	
    And the input freezeExtensionEndDate as "2019-03-07T09:52:59.145Z"
    And the input extensionDurationMonths as "1"
		And the input waiveOffExtensionFee as "true"
    And the input notesType as "testNoteType"
    And the input notes as "testNotes"
    And the input contactType as "Phone"
		
		When client makes call to POST v1/freeze-extension/
    Then the client receives status failure
    And FreezeExtensionOutput error message is MemberAgreementDetail should not be null.
		
@tag3
  Scenario: Request when No Active/Pending Freeze request exist for member
   	Given the input memberId  as "1000730366"
    And the input facilityId  as "252"
    And the input freezeReason as "Regular" 	
    And the input freezeExtensionEndDate as "2019-03-07T09:52:59.145Z"
    And the input extensionDurationMonths as "1"
		And the input waiveOffExtensionFee as "true"
    And the input notesType as "testNoteType"
    And the input notes as "testNotes"
    And the input contactType as "Phone"
		
		When client makes call to POST v1/freeze-extension/
    Then the client receives status failure
		
	@tag4
  Scenario: Request when required parameter extensionDurationMonths is empty
   	Given the input memberId  as "1000730366"
    And the input facilityId  as "252"
    And the input freezeReason as "Regular" 	
    And the input freezeExtensionEndDate as "2019-03-07T09:52:59.145Z"
    And the input extensionDurationMonths as ""
		And the input waiveOffExtensionFee as "true"
    And the input notesType as "testNoteType"
    And the input notes as "testNotes"
    And the input contactType as "Phone"
		
		When client makes call to POST v1/freeze-extension/
    Then the client receives status failure
		And FreezeExtensionOutput error message is FREEZE_EXTENSION_MONTH_VALIDATION
		
		@tag5
  Scenario: Request when required parameter getFreezeExtensionEndDate is empty
   	Given the input memberId  as "1000730366"
    And the input facilityId  as "252"
    And the input freezeReason as "Regular" 	
    And the input freezeExtensionEndDate as ""
    And the input extensionDurationMonths as "1"
		And the input waiveOffExtensionFee as "true"
    And the input notesType as "testNoteType"
    And the input notes as "testNotes"
    And the input contactType as "Phone"
		
		When client makes call to POST v1/freeze-extension/
    Then the client receives status failure
		And FreezeExtensionOutput error message is FREEZE_EXTENSION_ENDDATE_VALIDATION
		
		@tag6
  Scenario: Request when required parameter FacilityId is empty
   	Given the input memberId  as "1000730366"
    And the input facilityId  as ""
    And the input freezeReason as "Regular" 	
    And the input freezeExtensionEndDate as "2019-03-07T09:52:59.145Z"
    And the input extensionDurationMonths as "1"
		And the input waiveOffExtensionFee as "true"
    And the input notesType as "testNoteType"
    And the input notes as "testNotes"
    And the input contactType as "Phone"
		
		When client makes call to POST v1/freeze-extension/
    Then the client receives status failure
		And FreezeExtensionOutput error message is FACILITYID_REQUIRED
