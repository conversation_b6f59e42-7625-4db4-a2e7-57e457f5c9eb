# Pull Request Title

Below is a template for you to use when creating a new PR. Please review our guide to [elements of a good PR review](https://equinoxfitness.atlassian.net/wiki/spaces/DO/pages/2352349225/Elements+of+a+Good+Code+Review) to make sure you're familiar with our PR standards. Fill out the details below and clean up the description to remove any unused template values.

---

## *< Tag any individuals who you think should review this PR outside of the default reviewers for this repository, ex. @someone :wave: this is Ready for Review! >*

## What and why

### *< Provide What and Why details below. Please clean this up and removed andy unused bullet points. >*

- Provide a link to an existing Jira ticket or Slack conversation.
- What is the change doing and why is the change needed?
- What is the context related to the change? Include as much detail here as possible.
- Does your code introduce any new dependencies?
- Provide links to [graphs](https://graphs.equinoxfitness.com/), logs, or diagrams that can help a reviewer understand your change.

## Testing

### *< Testing details below. Please clean this up and removed andy unused bullet points. >*

- How did you test your change?
- If you want others to test your change, provide detailed instructions on how to run the test.
- What were the results of your test?
