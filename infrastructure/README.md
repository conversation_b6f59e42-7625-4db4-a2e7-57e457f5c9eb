## NOTES
1. For everyone to be able to plan / apply terraform using the centralized state file, we will need to figure out how to manage roles for humans vs services

For Service: 
        * Everything is managed from a single role "eqx-jenkins" or "eqx-terraform".
        * This service can pass in the role for the child account: -var=role_arn=arn:aws:iam::************:role/px-service-automation
        * The default role "eqx-jenkins" or "eqx-terraform" has access to the s3 bucket
        * Jenkins would run:
```
terraform init -backend-config="key=platform/develop/ci_cd_poc_1/************/terraform.tfstate"  
terraform plan -out tfplan -var=environment=develop -var=role_arn=arn:aws:iam::************:role/px-service-automation -var=app_version=latest

backend "s3" {
  bucket         = "eqx-infrastructure-terraform"
  region         = "us-east-1"
  dynamodb_table = "eqx_infrastructure_terraform"
  encrypt        = true
}

provider "aws" {
  region = "us-east-1"

  assume_role {
    role_arn = var.role_arn
  }
}

Default credentials are used to access the s3 bucket / dynamodb table
Assumes the passed in role to plan/apply in the application account
```

User Access:
        * User logons with SSO
        * User passes a --backend-config profile with the init
```
terraform init -backend-config="key=platform/develop/ci_cd_poc_1/************/terraform.tfstate" --backend-config=profile="default-equinox" -reconfigure 
terraform plan -out tfplan -var=environment=develop  -var=app_version=latest        
```

We will need likely need some helper functions to help make sure the commands are always run correctly.

For Testing purposes:
1. Log into AWS SSO
2. Log into the core develop account.  This is the account we want to deploy the thing to.
3. Make sure you have a profile for "eqx-fitness-prod"
4. To Plan, run: make tfplan
5. To Apply, run: make tfapply

This runs the following commands:
  terraform init -backend-config="key=platform/************/develop/poc-cicd/terraform.tfstate" --backend-config=profile="eqx-fitness-prod" -reconfigure 
  terraform plan -out tfplan -var=environment=develop  -var=app_version=latest


  
<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> 1.6.3 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.35.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_application"></a> [application](#module\_application) | ../../application_modules/ecs_default | n/a |

## Resources

| Name | Type |
|------|------|
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_app_version"></a> [app\_version](#input\_app\_version) | n/a | `string` | `"latest"` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | n/a | `string` | `"develop"` | no |
| <a name="input_role_arn"></a> [role\_arn](#input\_role\_arn) | n/a | `string` | `null` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_application_module_output"></a> [application\_module\_output](#output\_application\_module\_output) | n/a |
| <a name="output_local_config"></a> [local\_config](#output\_local\_config) | n/a |
<!-- END_TF_DOCS -->