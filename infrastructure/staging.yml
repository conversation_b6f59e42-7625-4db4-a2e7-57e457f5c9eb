---
environment: staging
memory: 1024
desired_count: 1
enable_autoscaling: false
launch_type: EC2
region: us-east-1
lb_reference: hypergate_alb_arn
container_environmental_variables:
  - name: SPRING_PROFILES_ACTIVE
    value: staging
container_secrets:
  - name: salesforce.contentversion.freeze.folder.id
    valueFrom: salesforce.contentversion.freeze.folder.id
  - name: spring.datasource.tenantEquinox.url
    valueFrom: spring.datasource.tenantEquinox.url
  - name: spring.datasource.tenantEquinox.username
    valueFrom: spring.datasource.tenantEquinox.username
  - name: spring.datasource.tenantEquinox.password
    valueFrom: spring.datasource.tenantEquinox.password
  - name: spring.datasource.webdb.url
    valueFrom: spring.datasource.webdb.url
  - name: spring.datasource.webdb.username
    valueFrom: spring.datasource.webdb.username
  - name: spring.datasource.webdb.password
    valueFrom: spring.datasource.webdb.password
  - name: facility.apikey
    valueFrom: facility.apikey
  - name: freeze.moso.user
    valueFrom: freeze.moso.user
  - name: freeze.moso.password
    valueFrom: freeze.moso.password
  - name: sfdc.authtoken.pass
    valueFrom: sfdc.authtoken.pass
  - name: sfdc.authtoken.security.token
    valueFrom: sfdc.authtoken.security.token
  - name: sfdc.authtoken.client.id
    valueFrom: sfdc.authtoken.client.id
  - name: sfdc.authtoken.client.secret
    valueFrom: sfdc.authtoken.client.secret
  - name: token.hypergate.apikey
    valueFrom: token.hypergate.apikey