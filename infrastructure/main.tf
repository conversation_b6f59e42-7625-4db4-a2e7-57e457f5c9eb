variable "app_version" {
  type    = string
  default = "latest"
}
variable "role_arn" {
  type    = string
  default = null
}
variable "environment" {
  type    = string
  default = "develop"
}
variable "region" {
  type    = string
  default = "us-east-1"
}
terraform {
  required_version = "~> 1.8.5"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "s3" {
    bucket         = "eqx-infrastructure-terraform"
    region         = "us-east-1"
    dynamodb_table = "eqx_infrastructure_terraform"
    encrypt        = true
    assume_role = {
      role_arn = "arn:aws:iam::813561490937:role/terraform-backend-role"
    }
  }
}
provider "aws" {
  region = var.region
  alias  = "app"
  assume_role {
    role_arn = var.role_arn
  }
  default_tags {
    tags = {
      iac_path     = "${local.config["code_url"]}/infrastructure"
      code_url     = local.config["code_url"]
      cicd_url     = local.cicd_url
      environment  = var.environment
      state_file   = local.config["state_file"]
      owner        = local.config["owner"]
      application  = local.config["app"]
      project_cost = local.config["project_cost"]
      company      = local.config["company"]
    }
  }
}
provider "aws" {
  region = var.region
  alias  = "fitness_prod"
  assume_role {
    role_arn = "arn:aws:iam::813561490937:role/terraform-dns-role"
  }
  default_tags {
    tags = {
      iac_path     = "${local.config["code_url"]}/infrastructure"
      code_url     = local.config["code_url"]
      cicd_url     = local.cicd_url
      environment  = var.environment
      state_file   = local.config["state_file"]
      owner        = local.config["owner"]
      application  = local.config["app"]
      project_cost = local.config["project_cost"]
      company      = local.config["company"]
    }
  }
}
locals {
  environment_config = yamldecode(file("${var.environment}.yml"))
  app_config         = yamldecode(file("../config.yml"))
  jenkins_config     = yamldecode(file("jenkins.yml"))
  config = merge(
    local.environment_config,
    local.app_config,
    local.jenkins_config
  )
  cicd_url_array = split("/job/", local.config["cicd_url"])
  cicd_url       = trimsuffix(local.config["cicd_url"], "/job/${local.cicd_url_array[length(local.cicd_url_array) - 1]}")
}
module "application" {
  source = "github.com/equinox-platformx/terraform-modules.git//application_modules/ecs_default"
  providers = {
    aws.alt_dns = aws.fitness_prod
    aws.app     = aws.app
  }
  config_yaml = local.config
  app_version = var.app_version
  environment = var.environment
}
output "application_module_output" {
  value = module.application
}