apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: hypergate-freeze
  title: hypergate-freeze
  description: hypergate freeze sevice
  annotations:
    backstage.io/code-coverage: scm-only
    github.com/project-slug: equinox-platformx/${{ (parameters.repoURL | parseRepoUrl).repo }}
    jenkins.io/job-full-name: hypergate/hypergate.freeze
  tags:
    - java
    - springboot
    - hypergate-service
spec:
  type: service
  system: system:default/engage
  lifecycle: production
  owner: contech_backend
