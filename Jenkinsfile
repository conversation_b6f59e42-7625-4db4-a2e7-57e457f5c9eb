#!groovy
@Library('eqx-shared-libraries')

// All variables that will be used throughout stages needs to be pre-defined
String version
String appName
String owner
String accountId
String deployEnvironment
String awsRole
String stateFile
String ecrRepoUrl
String awsRegion
String projectFile = "pom.xml"

pipeline {
    agent none
    options {
        buildDiscarder(logRotator(numToKeepStr: '30'))
        disableConcurrentBuilds()
        timeout(time: 6, unit: 'HOURS')
        ansiColor('xterm')
    }

    parameters {
        booleanParam(name: 'ROLLBACK', defaultValue: false, description: 'Enable rollback to a previous deployment')
        string(name: 'ROLLBACK_BUILD_NUMBER', defaultValue: '', description: 'Build number to rollback to (used only if rollback is true)')
    }

    stages {
        stage('Initialize') {
            agent { label 'linux-terraform' }
            when {
                beforeAgent true
                anyOf { branch 'develop'; branch 'main'; branch 'master'; branch 'release'; changeRequest() }
            }
            steps {
                script {
                    echo "Starting Initialize"
        
                    if (params.ROLLBACK) {
                        if (!params.ROLLBACK_BUILD_NUMBER?.trim()) {
                            error("ROLLBACK_BUILD_NUMBER is required when ROLLBACK is enabled.")
                        }
                        echo "Rollback triggered. Fetching version from build #${params.ROLLBACK_BUILD_NUMBER}"
                        version = "${params.ROLLBACK_BUILD_NUMBER}"
                        deployEnvironment = environmentFromBranch(env.BRANCH_NAME)
                        currentBuild.displayName = "ROLLBACK-${version}"
                        echo "Rolling back to version: ${version}"
                    } else {
                        if (env.CHANGE_ID != null) {
                            version = VersionNumber(versionNumberString: 'pr${CHANGE_ID}.${BUILD_YEAR}.${BUILD_MONTH}.${BUILD_DAY}.${BUILDS_TODAY}', skipFailedBuilds:false)
                            deployEnvironment = environmentFromBranch(env.CHANGE_TARGET)
                        } else if (env.BRANCH_NAME != 'master' && env.BRANCH_NAME != 'main') {
                            version = VersionNumber(versionNumberString:'v${BUILD_YEAR}.${BUILD_MONTH}.${BUILD_DAY}.${BUILDS_TODAY}', skipFailedBuilds:false)
                            deployEnvironment = environmentFromBranch(env.BRANCH_NAME)
                        } else {
                            version = VersionNumber(versionNumberString: 'v2.1.${BUILD_NUMBER, X}', skipFailedBuilds:false)
                            deployEnvironment = environmentFromBranch(env.BRANCH_NAME)
                        }
                        currentBuild.displayName = version
                    }
        
                    println "Pipeline Version='${version}'"
                    println "Deploy Environment is ${deployEnvironment} based on branch/pr"
        
                    appName = (((GIT_URL.tokenize('/')[-1]).replace(".git","")).replaceAll("[^a-zA-Z0-9]+","-"))
        
                    def config_file = readYaml file: './config.yml'
                    owner = checkConfigProperty(config_file,"owner")
                    checkConfigProperty(config_file,"company")
                    checkConfigProperty(config_file,"project_cost")
                    def awsAccount = checkConfigProperty(config_file,"aws_account")
                    def environment_config_file = readYaml file: "./infrastructure/${deployEnvironment}.yml"
                    awsRegion = checkConfigProperty(environment_config_file,"region")
        
                    accountId = awsAccountId(awsAccount,deployEnvironment)
                    stateFile = "${owner}/${accountId}/${deployEnvironment}/${appName}/terraform.tfstate"
                    awsRole = "arn:aws:iam::${accountId}:role/px-service-automation"
                }
            }
        }
        stage('Artifact Build') {
            agent { label 'linux' }
            tools { maven 'maven-3.5.0' }
            when {
                beforeAgent true
                allOf {
                    not { expression { return params.ROLLBACK } }
                    anyOf { branch 'develop'; branch 'main'; branch 'master'; branch 'release'; changeRequest() }
                }
            }
            steps {
                echo "Building Artifact"
                dir("${WORKSPACE}") {
                    sh("""
                        find . -type f -name pom.xml | xargs sed -i 's/0.0.0/'${version}'/g'
                    """)
                    mavenBuild(version, appName, deployEnvironment, projectFile, "-DskipTests")
                }
            }
        }

        stage('SonarQube Scan') {
            agent { label 'linux-jdk-21' }
            tools { maven 'maven-3.9.6' }
            when {
                beforeAgent true
                allOf {
                    not { expression { return params.ROLLBACK } }
                    anyOf { branch 'develop'; branch 'main'; branch 'master'; branch 'release'; changeRequest() }
                }
            }
            steps {
                script {
                    if (env.CHANGE_TARGET != null) {
                        sonarScanMaven(appName: appName, environment: deployEnvironment, referenceBranch: env.CHANGE_TARGET, version: version)
                    } else {
                        sonarScanMaven(appName: appName, environment: deployEnvironment, version: version)
                    }
                }
            }
        }

        stage('ECR Creation') {
            agent { label 'linux-terraform' }
            when {
                beforeAgent true
                allOf {
                    not { expression { return params.ROLLBACK } }
                    anyOf { branch 'develop'; branch 'main'; branch 'master'; branch 'release'; changeRequest() }
                }
            }
            steps {
                script {
                    generateJenkinsYaml("$stateFile","$appName","$awsRole")
                    echo "Calling terraformCreateECRV2 to create ECR"
                    ecrRepoUrl = terraformCreateECRV2(stateFile, deployEnvironment, awsRole, version)
                }
            }
        }

        stage('Docker Build') {
            agent { label 'linux-docker' }
            when {
                beforeAgent true
                allOf {
                    not { expression { return params.ROLLBACK } }
                    anyOf { branch 'develop'; branch 'main'; branch 'master'; branch 'release'; changeRequest() }
                }
            }
            steps {
                echo "Docker Build, ECR Push, etc..."
                dockerBuild(appName, deployEnvironment, version, ecrRepoUrl, awsRegion)
            }
        }

        stage('PR Testing') {
            agent { label 'linux-terraform' }
            when {
                beforeAgent true
                changeRequest()
            }
            steps {
                echo "Terraform plan & Optional Apply on Terraform PRs"
                script {
                    // Run a terraform plan of the environment
                    echo "Calling terraformPlanV2 to run a terraform plan"
                    generateJenkinsYaml("$stateFile","$appName","$awsRole")
                    terraformPlanV2(stateFile, deployEnvironment, awsRole, version)
                    // Option to deploy PR with timeout
                    terraformPRDeployV2(stateFile, "develop", accountId ,awsRole, version)
                    terraformPRDeployV2(stateFile, "staging", accountId, awsRole, version)
                }
            }
        }

        stage('Develop Deploy') {
            agent { label 'linux-terraform' }
            when {
                beforeAgent true
                branch 'develop'
            }
            steps {
                script {
                    echo "Calling terraformDeployV2 to deploy application"
                    generateJenkinsYaml("$stateFile","$appName","$awsRole")
                    terraformDeployV2(stateFile, deployEnvironment, awsRole, version)
                }
            }
        }

        stage('Staging Deploy') {
            agent { label 'linux-terraform' }
            when {
                beforeAgent true
                branch 'release'
            }
            steps {
                script {
                    echo "Calling terraformDeployV2 to deploy application"
                    generateJenkinsYaml("$stateFile","$appName","$awsRole")
                    terraformDeployV2(stateFile, deployEnvironment, awsRole, version)
                }
            }
        }

        stage('Approve Production') {
            agent none
            when {
                beforeAgent true
                anyOf { branch 'main'; branch 'master' }
            }
            steps {
                timeout(time: 1, unit: 'DAYS') {
                    input('Deploy to Production?')
                }
            }
        }

        stage('Production Deploy') {
            agent { label 'linux-terraform' }
            when {
                beforeAgent true
                anyOf { branch 'main'; branch 'master' }
            }
            steps {
                script {
                    echo "Calling terraformDeployV2 to deploy application"
                    generateJenkinsYaml("$stateFile","$appName","$awsRole")
                    terraformDeployV2(stateFile, deployEnvironment, awsRole, version)
                }
            }
        }

        stage('Github Tag') {
            agent { label 'linux' }
            when {
                beforeAgent true
                anyOf { branch 'main'; branch 'master' }
            }
            steps {
                script {
                    githubPush(version)
                }
            }
        }
    }
}