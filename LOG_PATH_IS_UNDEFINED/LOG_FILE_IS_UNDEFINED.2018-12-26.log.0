2018-12-26 11:15:40,870 1882 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Wed Dec 26 11:15:40 IST 2018]; root of context hierarchy
2018-12-26 11:15:41,117 2129 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-26 11:15:41,387 2399 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-26 11:15:41,468 2480 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$3405d00e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-26 12:31:23,292 1687 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Wed Dec 26 12:31:23 IST 2018]; root of context hierarchy
2018-12-26 12:31:23,488 1883 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-26 12:31:23,633 2028 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-26 12:31:23,696 2091 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-26 12:32:15,346 1513 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Wed Dec 26 12:32:15 IST 2018]; root of context hierarchy
2018-12-26 12:32:15,492 1659 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-26 12:32:15,680 1847 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-26 12:32:15,734 1901 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
