FILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINED2025-07-30 08:56:41,461 6977 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
"2025-07-30 08:56:41,513 7029 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
"2025-07-30 08:56:41,526 7042 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-stag,staging
"2025-07-30 08:56:41,564 7080 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@650eab8: startup date [Wed Jul 30 08:56:41 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80
"2025-07-30 08:56:43,936 9452 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'restTemplate' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=paymentServiceImpl; factoryMethodName=restTemplate; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/equinoxfitness/freezeservice/service/impl/PaymentServiceImpl.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=freezeServiceApplication; factoryMethodName=restTemplate; initMethodName=null; destroyMethodName=(inferred); defined in com.equinoxfitness.freezeservice.FreezeServiceApplication]
"2025-07-30 08:56:44,377 9893 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
"2025-07-30 08:56:45,103 10619 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=a75ea9a2-3566-3255-9984-2df8fb9b1af0
"2025-07-30 08:56:45,209 10725 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
"2025-07-30 08:56:45,341 10857 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$e3bb48fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 08:56:45,381 10897 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
"2025-07-30 08:56:45,583 11099 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3f2383fb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 08:56:45,796 11312 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$5b3d86f8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 08:56:47,211 12727 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8000 (http)
"2025-07-30 08:56:47,222 12738 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
"2025-07-30 08:56:47,222 12738 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
"2025-07-30 08:56:47,485 13001 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[.[localhost].[/freeze] - Initializing Spring embedded WebApplicationContext
"2025-07-30 08:56:47,486 13002 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5922 ms
"2025-07-30 08:56:48,558 14074 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
"2025-07-30 08:56:48,560 14076 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
"2025-07-30 08:56:48,564 14080 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
"2025-07-30 08:56:48,565 14081 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
"2025-07-30 08:56:48,565 14081 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
"2025-07-30 08:56:48,565 14081 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
"2025-07-30 08:56:50,234 15750 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
"2025-07-30 08:56:50,286 15802 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
"2025-07-30 08:57:54,494 80010 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 08:57:54,507 80023 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
"2025-07-30 08:57:54,507 80023 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
"2025-07-30 08:57:59,160 84676 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/version],methods=[GET]}" onto public com.equinoxfitness.commons.output.Version com.equinoxfitness.freezeservice.controller.FreezeServiceController.getVersion()
"2025-07-30 08:57:59,162 84678 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/concierge/freeze/email],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.EngageFreezeEmailIOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(com.equinoxfitness.freezeservice.contract.FreezeEmailInput)
"2025-07-30 08:57:59,163 84679 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
"2025-07-30 08:57:59,164 84680 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-info/{mosoMemberId}/],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int) throws java.lang.Exception
"2025-07-30 08:57:59,165 84681 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembership(com.equinoxfitness.freezeservice.contract.FreezeMembershipInput) throws java.lang.Exception
"2025-07-30 08:57:59,166 84682 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-extension/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
"2025-07-30 08:57:59,167 84683 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibilityV2(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2) throws java.lang.Exception
"2025-07-30 08:57:59,168 84684 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v3/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibilityV3(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2) throws java.lang.Exception
"2025-07-30 08:57:59,169 84685 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-extension/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtensionV2(com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV2) throws java.lang.Exception
"2025-07-30 08:57:59,170 84686 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV2(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2) throws java.lang.Exception
"2025-07-30 08:57:59,171 84687 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v4/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV4(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2) throws java.lang.Exception
"2025-07-30 08:57:59,171 84687 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v5/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV5(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV4) throws java.lang.Exception
"2025-07-30 08:57:59,172 84688 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-info/{mosoId}/],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReasonV2(java.lang.String,java.lang.String,int) throws java.lang.Exception
"2025-07-30 08:57:59,173 84689 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/agreements/freeze],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeAgreementsV3(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV3)
"2025-07-30 08:57:59,174 84690 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/{salesForceId}],methods=[DELETE],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.UnfreezeMembershipResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.unfreezeMember(java.lang.String,java.lang.String)
"2025-07-30 08:57:59,175 84691 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/unfreeze/{mosoId}],methods=[PUT],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.unFreezeInSalesforce(java.lang.String)
"2025-07-30 08:57:59,176 84692 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/freeze/email],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmail(com.equinoxfitness.freezeservice.contract.FreezeEmailInput)
"2025-07-30 08:57:59,177 84693 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/freeze/case],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.createMemberFreezeCase(com.equinoxfitness.freezeservice.contract.MemberFreezeCase)
"2025-07-30 08:57:59,180 84696 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
"2025-07-30 08:57:59,192 84708 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
"2025-07-30 08:57:59,193 84709 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
"2025-07-30 08:57:59,195 84711 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
"2025-07-30 08:57:59,210 84726 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
"2025-07-30 08:57:59,211 84727 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
"2025-07-30 08:58:00,380 85896 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.oxm.jaxb.Jaxb2Marshaller - Creating JAXBContext with context path [castiron.header.getsession:castiron.header.output.getsession:com.equinox.fitness.service.ci.getsession.v1:com.equinox.fitness.utility.v1]
"2025-07-30 08:58:00,652 86168 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 08:58:00,671 86187 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Loaded SessionMediator...
"2025-07-30 08:58:00,672 86188 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Default URI: https://equinoxstaging.mosocloud.com:443/api/2/session/start
"2025-07-30 08:58:00,783 86299 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 08:58:00,787 86303 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Loaded SessionMediator...
"2025-07-30 08:58:00,787 86303 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Default URI: null
"2025-07-30 08:58:03,284 88800 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@650eab8: startup date [Wed Jul 30 08:56:41 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80
"2025-07-30 08:58:03,876 89392 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 08:58:03,877 89393 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 08:58:03,999 89515 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in globalExceptionHandler
"2025-07-30 08:58:03,999 89515 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
"2025-07-30 08:58:04,163 89679 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 08:58:07,546 93062 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@650eab8: startup date [Wed Jul 30 08:56:41 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80] and template loader path [classpath:/templates/]
"2025-07-30 08:58:07,547 93063 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
"2025-07-30 08:58:10,810 96326 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
"2025-07-30 08:58:10,811 96327 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
"2025-07-30 08:58:10,815 96331 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
"2025-07-30 08:58:10,815 96331 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
"2025-07-30 08:58:11,449 96965 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
"2025-07-30 08:58:11,477 96993 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
"2025-07-30 08:58:11,478 96994 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
"2025-07-30 08:58:11,485 97001 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
"2025-07-30 08:58:11,496 97012 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
"2025-07-30 08:58:11,511 97027 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
"2025-07-30 08:58:11,533 97049 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=650eab8,type=ConfigurationPropertiesRebinder]
"2025-07-30 08:58:13,901 99417 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
"2025-07-30 08:58:13,917 99433 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
"2025-07-30 08:58:13,932 99448 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
"2025-07-30 08:58:14,741 100257 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8000"]
"2025-07-30 08:58:14,750 100266 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8000"]
"2025-07-30 08:58:14,759 100275 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
"2025-07-30 08:58:14,768 100284 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8000 (http)
"2025-07-30 08:58:14,789 100305 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 100.08 seconds (JVM running for 101.207)
"2025-07-30 09:01:15,264 280780 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [] INFO  o.a.c.c.C.[.[localhost].[/freeze] - Initializing Spring FrameworkServlet 'dispatcherServlet'
"2025-07-30 09:01:15,265 280781 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
"2025-07-30 09:01:15,398 280914 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 132 ms
"2025-07-30 09:01:15,424 280940 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [] INFO  c.e.c.aspect.ClientPlatformAspect - device from headers is null 
"2025-07-30 09:01:15,424 280940 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
"2025-07-30 09:01:15,425 280941 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 4bb02034-9570-4e5a-9b8f-6339463b41a4 
"2025-07-30 09:01:15,425 280941 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No x-request-id found in Header. Generated 521e3ad4-890c-4514-9589-5c0c242c3de8 
"2025-07-30 09:01:15,477 280993 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [4bb02034-9570-4e5a-9b8f-6339463b41a4] INFO  c.e.f.c.FreezeServiceController - Send freeze link to member from engage com.equinoxfitness.freezeservice.contract.FreezeEmailInput@2cd09875
"2025-07-30 09:01:35,589 301105 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [4bb02034-9570-4e5a-9b8f-6339463b41a4] INFO  c.e.f.s.impl.FreezeServiceImpl - Requested clubId: 149, timezone: America/New_York, and club_localtime is 2025-07-29T23:31:27.926-04
"2025-07-30 09:01:41,701 307217 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [4bb02034-9570-4e5a-9b8f-6339463b41a4] INFO  c.e.commons.aspect.TimeLoggerAspect - Timer Advice URL ::/freeze/v1/concierge/freeze/email, method ::POST StatusCode ::200 ResponseTime ::26275
"2025-07-30 09:01:41,703 307219 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [4bb02034-9570-4e5a-9b8f-6339463b41a4] INFO  c.e.h.p.c.FreezeServiceController - [ResponseEntity com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(FreezeEmailInput) - 26277ms]
"2025-07-30 09:01:41,725 307241 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-1] [] [4bb02034-9570-4e5a-9b8f-6339463b41a4] ERROR c.e.c.e.a.ExceptionControllerAdvice - Exception occurred due to: 
"org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:204)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:348)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:129)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:92)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:79)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:194)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:169)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:91)
	at org.springframework.data.redis.core.DefaultHashOperations.entries(DefaultHashOperations.java:220)
	at com.equinoxfitness.redisUtil.RedisTemplate.getRedisHashOpsData(RedisTemplate.java:63)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.getTokenByData(FreezeServiceImpl.java:2111)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.sendFreezeEmailLink(FreezeServiceImpl.java:2574)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(FreezeServiceController.java:1251)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$FastClassBySpringCGLIB$$60e58a08.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.TimeLoggerAspect.controllerMethods(TimeLoggerAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.CorrelationAspect.controllerMethods(CorrelationAspect.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.ClientPlatformAspect.controllerMethods(ClientPlatformAspect.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$EnhancerBySpringCGLIB$$e19a552d.sendMemberFreezeEmailFromEngage(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:16)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:194)
	... 104 common frames omitted
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 107 common frames omitted
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 114 common frames omitted
2025-07-30 09:53:12,248 3397764 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [] INFO  c.e.c.aspect.ClientPlatformAspect - device from headers is null 
"2025-07-30 09:53:12,250 3397766 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
"2025-07-30 09:53:12,251 3397767 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 4806d71d-ff79-49bd-b677-e91af0b0c2f6 
"2025-07-30 09:53:12,252 3397768 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [] INFO  c.e.c.aspect.CorrelationAspect - No x-request-id found in Header. Generated 35a3e929-1ccd-4305-b476-95299ace9798 
"2025-07-30 09:53:12,253 3397769 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [4806d71d-ff79-49bd-b677-e91af0b0c2f6] INFO  c.e.f.c.FreezeServiceController - Send freeze link to member from engage com.equinoxfitness.freezeservice.contract.FreezeEmailInput@1f5bff4
"2025-07-30 09:53:21,592 3407108 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [4806d71d-ff79-49bd-b677-e91af0b0c2f6] INFO  c.e.f.s.impl.FreezeServiceImpl - Requested clubId: 149, timezone: America/New_York, and club_localtime is 2025-07-30T00:23:20.785-04
"2025-07-30 09:53:26,348 3411864 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [4806d71d-ff79-49bd-b677-e91af0b0c2f6] INFO  c.e.commons.aspect.TimeLoggerAspect - Timer Advice URL ::/freeze/v1/concierge/freeze/email, method ::POST StatusCode ::200 ResponseTime ::14095
"2025-07-30 09:53:26,349 3411865 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [4806d71d-ff79-49bd-b677-e91af0b0c2f6] INFO  c.e.h.p.c.FreezeServiceController - [ResponseEntity com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(FreezeEmailInput) - 14096ms]
"2025-07-30 09:53:26,352 3411868 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-4] [] [4806d71d-ff79-49bd-b677-e91af0b0c2f6] ERROR c.e.c.e.a.ExceptionControllerAdvice - Exception occurred due to: 
"org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:204)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:348)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:129)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:92)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:79)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:194)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:169)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:91)
	at org.springframework.data.redis.core.DefaultHashOperations.entries(DefaultHashOperations.java:220)
	at com.equinoxfitness.redisUtil.RedisTemplate.getRedisHashOpsData(RedisTemplate.java:63)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.getTokenByData(FreezeServiceImpl.java:2111)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.sendFreezeEmailLink(FreezeServiceImpl.java:2574)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(FreezeServiceController.java:1251)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$FastClassBySpringCGLIB$$60e58a08.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.TimeLoggerAspect.controllerMethods(TimeLoggerAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.CorrelationAspect.controllerMethods(CorrelationAspect.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.ClientPlatformAspect.controllerMethods(ClientPlatformAspect.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$EnhancerBySpringCGLIB$$e19a552d.sendMemberFreezeEmailFromEngage(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:16)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:194)
	... 104 common frames omitted
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 107 common frames omitted
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 114 common frames omitted
FILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINED2025-07-30 09:59:11,217 7374 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
"2025-07-30 09:59:11,289 7446 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
"2025-07-30 09:59:11,305 7462 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-stag,staging
"2025-07-30 09:59:11,346 7503 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@f0e995e: startup date [Wed Jul 30 09:59:11 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80
"2025-07-30 09:59:13,968 10125 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'restTemplate' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=paymentServiceImpl; factoryMethodName=restTemplate; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/equinoxfitness/freezeservice/service/impl/PaymentServiceImpl.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=freezeServiceApplication; factoryMethodName=restTemplate; initMethodName=null; destroyMethodName=(inferred); defined in com.equinoxfitness.freezeservice.FreezeServiceApplication]
"2025-07-30 09:59:14,421 10578 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
"2025-07-30 09:59:15,239 11396 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=a75ea9a2-3566-3255-9984-2df8fb9b1af0
"2025-07-30 09:59:15,355 11512 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
"2025-07-30 09:59:15,503 11660 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$6edbbb3c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 09:59:15,546 11703 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
"2025-07-30 09:59:15,767 11924 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ca43f63d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 09:59:16,006 12163 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e65df93a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 09:59:17,610 13767 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8000 (http)
"2025-07-30 09:59:17,618 13775 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
"2025-07-30 09:59:17,619 13776 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
"2025-07-30 09:59:17,934 14091 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[.[localhost].[/freeze] - Initializing Spring embedded WebApplicationContext
"2025-07-30 09:59:17,934 14091 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6588 ms
"2025-07-30 09:59:19,133 15290 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
"2025-07-30 09:59:19,134 15291 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
"2025-07-30 09:59:19,137 15294 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
"2025-07-30 09:59:19,137 15294 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
"2025-07-30 09:59:19,137 15294 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
"2025-07-30 09:59:19,138 15295 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
"2025-07-30 09:59:21,202 17359 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
"2025-07-30 09:59:21,285 17442 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
"2025-07-30 10:00:27,255 83412 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 10:00:27,268 83425 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
"2025-07-30 10:00:27,269 83426 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
"2025-07-30 10:00:32,225 88382 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/version],methods=[GET]}" onto public com.equinoxfitness.commons.output.Version com.equinoxfitness.freezeservice.controller.FreezeServiceController.getVersion()
"2025-07-30 10:00:32,227 88384 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
"2025-07-30 10:00:32,228 88385 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-info/{mosoMemberId}/],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int) throws java.lang.Exception
"2025-07-30 10:00:32,229 88386 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembership(com.equinoxfitness.freezeservice.contract.FreezeMembershipInput) throws java.lang.Exception
"2025-07-30 10:00:32,230 88387 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-extension/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
"2025-07-30 10:00:32,231 88388 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibilityV2(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2) throws java.lang.Exception
"2025-07-30 10:00:32,232 88389 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v3/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibilityV3(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2) throws java.lang.Exception
"2025-07-30 10:00:32,233 88390 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-extension/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtensionV2(com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV2) throws java.lang.Exception
"2025-07-30 10:00:32,234 88391 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV2(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2) throws java.lang.Exception
"2025-07-30 10:00:32,236 88393 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v4/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV4(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2) throws java.lang.Exception
"2025-07-30 10:00:32,237 88394 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v5/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV5(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV4) throws java.lang.Exception
"2025-07-30 10:00:32,238 88395 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-info/{mosoId}/],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReasonV2(java.lang.String,java.lang.String,int) throws java.lang.Exception
"2025-07-30 10:00:32,239 88396 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/agreements/freeze],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeAgreementsV3(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV3)
"2025-07-30 10:00:32,240 88397 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/{salesForceId}],methods=[DELETE],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.UnfreezeMembershipResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.unfreezeMember(java.lang.String,java.lang.String)
"2025-07-30 10:00:32,241 88398 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/unfreeze/{mosoId}],methods=[PUT],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.unFreezeInSalesforce(java.lang.String)
"2025-07-30 10:00:32,242 88399 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/freeze/email],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmail(com.equinoxfitness.freezeservice.contract.FreezeEmailInput)
"2025-07-30 10:00:32,243 88400 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/freeze/case],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.createMemberFreezeCase(com.equinoxfitness.freezeservice.contract.MemberFreezeCase)
"2025-07-30 10:00:32,244 88401 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/concierge/freeze/email],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.EngageFreezeEmailIOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(com.equinoxfitness.freezeservice.contract.FreezeEmailInput)
"2025-07-30 10:00:32,247 88404 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
"2025-07-30 10:00:32,259 88416 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
"2025-07-30 10:00:32,260 88417 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
"2025-07-30 10:00:32,261 88418 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
"2025-07-30 10:00:32,276 88433 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
"2025-07-30 10:00:32,277 88434 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
"2025-07-30 10:00:33,555 89712 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.oxm.jaxb.Jaxb2Marshaller - Creating JAXBContext with context path [castiron.header.getsession:castiron.header.output.getsession:com.equinox.fitness.service.ci.getsession.v1:com.equinox.fitness.utility.v1]
"2025-07-30 10:00:33,830 89987 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 10:00:33,851 90008 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Loaded SessionMediator...
"2025-07-30 10:00:33,851 90008 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Default URI: https://equinoxstaging.mosocloud.com:443/api/2/session/start
"2025-07-30 10:00:33,973 90130 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 10:00:33,976 90133 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Loaded SessionMediator...
"2025-07-30 10:00:33,977 90134 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Default URI: null
"2025-07-30 10:00:36,855 93012 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@f0e995e: startup date [Wed Jul 30 09:59:11 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80
"2025-07-30 10:00:37,506 93663 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 10:00:37,506 93663 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 10:00:37,638 93795 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in globalExceptionHandler
"2025-07-30 10:00:37,639 93796 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
"2025-07-30 10:00:37,820 93977 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 10:00:41,572 97729 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@f0e995e: startup date [Wed Jul 30 09:59:11 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80] and template loader path [classpath:/templates/]
"2025-07-30 10:00:41,573 97730 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
"2025-07-30 10:00:45,285 101442 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
"2025-07-30 10:00:45,285 101442 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
"2025-07-30 10:00:45,291 101448 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
"2025-07-30 10:00:45,291 101448 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
"2025-07-30 10:00:46,020 102177 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
"2025-07-30 10:00:46,052 102209 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
"2025-07-30 10:00:46,053 102210 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
"2025-07-30 10:00:46,060 102217 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
"2025-07-30 10:00:46,072 102229 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
"2025-07-30 10:00:46,089 102246 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
"2025-07-30 10:00:46,115 102272 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=f0e995e,type=ConfigurationPropertiesRebinder]
"2025-07-30 10:00:48,685 104842 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
"2025-07-30 10:00:48,790 104947 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
"2025-07-30 10:00:48,807 104964 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
"2025-07-30 10:00:49,758 105915 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8000"]
"2025-07-30 10:00:49,768 105925 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8000"]
"2025-07-30 10:00:49,778 105935 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
"2025-07-30 10:00:49,790 105947 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8000 (http)
"2025-07-30 10:00:49,816 105973 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 105.732 seconds (JVM running for 106.783)
"2025-07-30 10:00:53,506 109663 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  o.a.c.c.C.[.[localhost].[/freeze] - Initializing Spring FrameworkServlet 'dispatcherServlet'
"2025-07-30 10:00:53,506 109663 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
"2025-07-30 10:00:53,659 109816 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 152 ms
"2025-07-30 10:00:53,693 109850 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  c.e.c.aspect.ClientPlatformAspect - device from headers is null 
"2025-07-30 10:00:53,694 109851 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
"2025-07-30 10:00:53,694 109851 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 2aeb9162-9e8f-416c-abfa-a1c116b489b7 
"2025-07-30 10:00:53,695 109852 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - No x-request-id found in Header. Generated ff6d939e-d562-40c1-9196-7f3287deb08a 
"2025-07-30 10:00:53,759 109916 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [2aeb9162-9e8f-416c-abfa-a1c116b489b7] INFO  c.e.f.c.FreezeServiceController - Send freeze link to member from engage com.equinoxfitness.freezeservice.contract.FreezeEmailInput@585633c0
"2025-07-30 10:01:04,425 120582 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [2aeb9162-9e8f-416c-abfa-a1c116b489b7] INFO  c.e.f.s.impl.FreezeServiceImpl - Requested clubId: 149, timezone: America/New_York, and club_localtime is 2025-07-30T00:31:03.705-04
"2025-07-30 10:01:09,310 125467 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [2aeb9162-9e8f-416c-abfa-a1c116b489b7] INFO  c.e.commons.aspect.TimeLoggerAspect - Timer Advice URL ::/freeze/v1/concierge/freeze/email, method ::POST StatusCode ::200 ResponseTime ::15614
"2025-07-30 10:01:09,312 125469 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [2aeb9162-9e8f-416c-abfa-a1c116b489b7] INFO  c.e.h.p.c.FreezeServiceController - [ResponseEntity com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(FreezeEmailInput) - 15616ms]
"2025-07-30 10:01:09,330 125487 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [2aeb9162-9e8f-416c-abfa-a1c116b489b7] ERROR c.e.c.e.a.ExceptionControllerAdvice - Exception occurred due to: 
"org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:204)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:348)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:129)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:92)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:79)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:194)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:169)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:91)
	at org.springframework.data.redis.core.DefaultHashOperations.entries(DefaultHashOperations.java:220)
	at com.equinoxfitness.redisUtil.RedisTemplate.getRedisHashOpsData(RedisTemplate.java:63)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.getTokenByData(FreezeServiceImpl.java:2111)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.sendFreezeEmailLink(FreezeServiceImpl.java:2574)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(FreezeServiceController.java:1251)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$FastClassBySpringCGLIB$$60e58a08.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.TimeLoggerAspect.controllerMethods(TimeLoggerAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.CorrelationAspect.controllerMethods(CorrelationAspect.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.ClientPlatformAspect.controllerMethods(ClientPlatformAspect.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$EnhancerBySpringCGLIB$$444c000f.sendMemberFreezeEmailFromEngage(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:16)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:194)
	... 104 common frames omitted
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 107 common frames omitted
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 114 common frames omitted
FILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINEDFILE_LOG_PATTERN_IS_UNDEFINED2025-07-30 10:11:12,398 7064 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
"2025-07-30 10:11:12,454 7120 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
"2025-07-30 10:11:12,467 7133 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-stag,staging
"2025-07-30 10:11:12,505 7171 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@14f5da2c: startup date [Wed Jul 30 10:11:12 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80
"2025-07-30 10:11:14,981 9647 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'restTemplate' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=paymentServiceImpl; factoryMethodName=restTemplate; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/equinoxfitness/freezeservice/service/impl/PaymentServiceImpl.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=freezeServiceApplication; factoryMethodName=restTemplate; initMethodName=null; destroyMethodName=(inferred); defined in com.equinoxfitness.freezeservice.FreezeServiceApplication]
"2025-07-30 10:11:15,413 10079 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
"2025-07-30 10:11:16,203 10869 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=a75ea9a2-3566-3255-9984-2df8fb9b1af0
"2025-07-30 10:11:16,317 10983 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
"2025-07-30 10:11:16,461 11127 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$6edbbb3c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 10:11:16,499 11165 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
"2025-07-30 10:11:16,714 11380 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ca43f63d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 10:11:16,945 11611 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e65df93a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
"2025-07-30 10:11:18,491 13157 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8000 (http)
"2025-07-30 10:11:18,500 13166 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
"2025-07-30 10:11:18,500 13166 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
"2025-07-30 10:11:18,787 13453 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[.[localhost].[/freeze] - Initializing Spring embedded WebApplicationContext
"2025-07-30 10:11:18,787 13453 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6282 ms
"2025-07-30 10:11:19,965 14631 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
"2025-07-30 10:11:19,967 14633 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
"2025-07-30 10:11:19,969 14635 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
"2025-07-30 10:11:19,969 14635 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
"2025-07-30 10:11:19,969 14635 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
"2025-07-30 10:11:19,970 14636 [freeze] [MTLP-3643.local] [8000] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
"2025-07-30 10:11:21,843 16509 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
"2025-07-30 10:11:21,900 16566 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
"2025-07-30 10:12:26,187 80853 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 10:12:26,199 80865 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
"2025-07-30 10:12:26,200 80866 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
"2025-07-30 10:12:30,861 85527 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/version],methods=[GET]}" onto public com.equinoxfitness.commons.output.Version com.equinoxfitness.freezeservice.controller.FreezeServiceController.getVersion()
"2025-07-30 10:12:30,863 85529 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/concierge/freeze/email],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.EngageFreezeEmailIOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(com.equinoxfitness.freezeservice.contract.FreezeEmailInput)
"2025-07-30 10:12:30,864 85530 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/unfreeze/{mosoId}],methods=[PUT],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.unFreezeInSalesforce(java.lang.String)
"2025-07-30 10:12:30,865 85531 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/freeze/email],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmail(com.equinoxfitness.freezeservice.contract.FreezeEmailInput)
"2025-07-30 10:12:30,866 85532 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/{salesForceId}],methods=[DELETE],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.UnfreezeMembershipResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.unfreezeMember(java.lang.String,java.lang.String)
"2025-07-30 10:12:30,867 85533 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/clubapp/freeze/case],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.equinoxfitness.freezeservice.controller.FreezeServiceController.createMemberFreezeCase(com.equinoxfitness.freezeservice.contract.MemberFreezeCase)
"2025-07-30 10:12:30,868 85534 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/agreements/freeze],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeAgreementsV3(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV3)
"2025-07-30 10:12:30,869 85535 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-info/{mosoId}/],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReasonV2(java.lang.String,java.lang.String,int) throws java.lang.Exception
"2025-07-30 10:12:30,870 85536 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v4/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV4(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2) throws java.lang.Exception
"2025-07-30 10:12:30,872 85538 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v5/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV5(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV4) throws java.lang.Exception
"2025-07-30 10:12:30,873 85539 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
"2025-07-30 10:12:30,874 85540 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-info/{mosoMemberId}/],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int) throws java.lang.Exception
"2025-07-30 10:12:30,875 85541 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembership(com.equinoxfitness.freezeservice.contract.FreezeMembershipInput) throws java.lang.Exception
"2025-07-30 10:12:30,876 85542 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-extension/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
"2025-07-30 10:12:30,877 85543 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibilityV2(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2) throws java.lang.Exception
"2025-07-30 10:12:30,878 85544 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v3/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibilityV3(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInputV2) throws java.lang.Exception
"2025-07-30 10:12:30,879 85545 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-extension/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtensionV2(com.equinoxfitness.freezeservice.contract.FreezeExtensionInputV2) throws java.lang.Exception
"2025-07-30 10:12:30,880 85546 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/freeze-membership/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeMemberResponse> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeMembershipV2(com.equinoxfitness.freezeservice.contract.FreezeMembershipInputV2) throws java.lang.Exception
"2025-07-30 10:12:30,884 85550 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
"2025-07-30 10:12:30,897 85563 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
"2025-07-30 10:12:30,898 85564 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
"2025-07-30 10:12:30,899 85565 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
"2025-07-30 10:12:30,915 85581 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
"2025-07-30 10:12:30,916 85582 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
"2025-07-30 10:12:32,144 86810 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.oxm.jaxb.Jaxb2Marshaller - Creating JAXBContext with context path [castiron.header.getsession:castiron.header.output.getsession:com.equinox.fitness.service.ci.getsession.v1:com.equinox.fitness.utility.v1]
"2025-07-30 10:12:32,428 87094 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 10:12:32,448 87114 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Loaded SessionMediator...
"2025-07-30 10:12:32,449 87115 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Default URI: https://equinoxstaging.mosocloud.com:443/api/2/session/start
"2025-07-30 10:12:32,571 87237 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
"2025-07-30 10:12:32,575 87241 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Loaded SessionMediator...
"2025-07-30 10:12:32,575 87241 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.c.m.s.impl.MosoSessionMediator - Default URI: null
"2025-07-30 10:12:35,320 89986 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@14f5da2c: startup date [Wed Jul 30 10:11:12 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80
"2025-07-30 10:12:35,964 90630 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 10:12:35,964 90630 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 10:12:36,095 90761 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in globalExceptionHandler
"2025-07-30 10:12:36,096 90762 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
"2025-07-30 10:12:36,277 90943 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
"2025-07-30 10:12:39,968 94634 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@14f5da2c: startup date [Wed Jul 30 10:11:12 IST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6a2f6f80] and template loader path [classpath:/templates/]
"2025-07-30 10:12:39,969 94635 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
"2025-07-30 10:12:43,633 98299 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
"2025-07-30 10:12:43,634 98300 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
"2025-07-30 10:12:43,637 98303 [freeze] [MTLP-3643.local] [8000] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
"2025-07-30 10:12:43,638 98304 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
"2025-07-30 10:12:44,313 98979 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
"2025-07-30 10:12:44,344 99010 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
"2025-07-30 10:12:44,345 99011 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
"2025-07-30 10:12:44,353 99019 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
"2025-07-30 10:12:44,364 99030 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
"2025-07-30 10:12:44,380 99046 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
"2025-07-30 10:12:44,404 99070 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=14f5da2c,type=ConfigurationPropertiesRebinder]
"2025-07-30 10:12:46,964 101630 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
"2025-07-30 10:12:46,980 101646 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
"2025-07-30 10:12:46,996 101662 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
"2025-07-30 10:12:47,871 102537 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8000"]
"2025-07-30 10:12:47,880 102546 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8000"]
"2025-07-30 10:12:47,889 102555 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
"2025-07-30 10:12:47,900 102566 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8000 (http)
"2025-07-30 10:12:47,923 102589 [freeze] [MTLP-3643.local] [8000] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 102.368 seconds (JVM running for 103.463)
"2025-07-30 10:13:31,977 146643 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  o.a.c.c.C.[.[localhost].[/freeze] - Initializing Spring FrameworkServlet 'dispatcherServlet'
"2025-07-30 10:13:31,978 146644 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
"2025-07-30 10:13:32,140 146806 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 162 ms
"2025-07-30 10:13:32,173 146839 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  c.e.c.aspect.ClientPlatformAspect - device from headers is null 
"2025-07-30 10:13:32,174 146840 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
"2025-07-30 10:13:32,174 146840 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated e7a1ca65-9908-45fc-8a0b-d13a56072795 
"2025-07-30 10:13:32,174 146840 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - No x-request-id found in Header. Generated 02e1c9c8-d3fd-4be4-b01d-5f9a1453c3de 
"2025-07-30 10:13:32,230 146896 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [e7a1ca65-9908-45fc-8a0b-d13a56072795] INFO  c.e.f.c.FreezeServiceController - Send freeze link to member from engage com.equinoxfitness.freezeservice.contract.FreezeEmailInput@63cfb0f5
"2025-07-30 10:13:41,518 156184 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [e7a1ca65-9908-45fc-8a0b-d13a56072795] INFO  c.e.f.s.impl.FreezeServiceImpl - Requested clubId: 149, timezone: America/New_York, and club_localtime is 2025-07-30T00:43:40.555-04
"2025-07-30 10:13:46,920 161586 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [e7a1ca65-9908-45fc-8a0b-d13a56072795] INFO  c.e.commons.aspect.TimeLoggerAspect - Timer Advice URL ::/freeze/v1/concierge/freeze/email, method ::POST StatusCode ::200 ResponseTime ::14745
"2025-07-30 10:13:46,921 161587 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [e7a1ca65-9908-45fc-8a0b-d13a56072795] INFO  c.e.h.p.c.FreezeServiceController - [ResponseEntity com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(FreezeEmailInput) - 14746ms]
"2025-07-30 10:13:46,937 161603 [freeze] [MTLP-3643.local] [8000] [http-nio-8000-exec-2] [] [e7a1ca65-9908-45fc-8a0b-d13a56072795] ERROR c.e.c.e.a.ExceptionControllerAdvice - Exception occurred due to: 
"org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:204)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:348)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:129)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:92)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:79)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:194)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:169)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:91)
	at org.springframework.data.redis.core.DefaultHashOperations.entries(DefaultHashOperations.java:220)
	at com.equinoxfitness.redisUtil.RedisTemplate.getRedisHashOpsData(RedisTemplate.java:63)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.getTokenByData(FreezeServiceImpl.java:2111)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.sendFreezeEmailLink(FreezeServiceImpl.java:2574)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.sendMemberFreezeEmailFromEngage(FreezeServiceController.java:1251)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$FastClassBySpringCGLIB$$60e58a08.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.TimeLoggerAspect.controllerMethods(TimeLoggerAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.CorrelationAspect.controllerMethods(CorrelationAspect.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.ClientPlatformAspect.controllerMethods(ClientPlatformAspect.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$EnhancerBySpringCGLIB$$59c45f8a.sendMemberFreezeEmailFromEngage(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:16)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:194)
	... 104 common frames omitted
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 107 common frames omitted
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 114 common frames omitted
