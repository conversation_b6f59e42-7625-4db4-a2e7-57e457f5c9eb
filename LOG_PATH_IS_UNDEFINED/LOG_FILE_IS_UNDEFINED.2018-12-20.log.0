2018-12-20 12:42:24,052 1742 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc: startup date [Thu Dec 20 12:42:24 IST 2018]; root of context hierarchy
2018-12-20 12:42:24,235 1925 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 12:42:24,382 2072 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:42:24,451 2141 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:42:26,031 3721 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 12:42:27,226 4916 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 12:42:27,227 4917 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - No active profile set, falling back to default profiles: default
2018-12-20 12:42:27,245 4935 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@6475472c: startup date [Thu Dec 20 12:42:27 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:42:28,454 6144 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 12:42:28,709 6399 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=ce8e6d21-1ada-3a0a-99ac-1668a24b4703
2018-12-20 12:42:28,729 6419 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:42:28,821 6511 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$937da83] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:42:28,893 6583 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 12:42:28,932 6622 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$64a01584] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:42:28,980 6670 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:42:29,410 7100 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 12:42:29,424 7114 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 12:42:29,426 7116 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 12:42:29,706 7396 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 12:42:29,706 7396 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2461 ms
2018-12-20 12:42:29,886 7576 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 12:42:29,888 7578 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 12:42:29,891 7581 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 12:42:29,892 7582 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 12:42:29,892 7582 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 12:42:29,892 7582 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 12:42:29,962 7652 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'freezeServiceController': Unsatisfied dependency expressed through field 'restResponse'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.equinoxfitness.commons.utils.RestResponse' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2018-12-20 12:42:29,964 7654 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 12:42:29,997 7687 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.a.l.AutoConfigurationReportLoggingInitializer - 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
2018-12-20 12:42:30,138 7828 [freeze] [A2ML10676] [8011] [main] [] [] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field restResponse in com.equinoxfitness.freezeservice.controller.FreezeServiceController required a bean of type 'com.equinoxfitness.commons.utils.RestResponse' that could not be found.


Action:

Consider defining a bean of type 'com.equinoxfitness.commons.utils.RestResponse' in your configuration.

2018-12-20 12:46:20,927 1530 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc: startup date [Thu Dec 20 12:46:20 IST 2018]; root of context hierarchy
2018-12-20 12:46:21,092 1695 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 12:46:21,262 1865 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:46:21,314 1917 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:46:23,006 3609 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 12:46:24,169 4772 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 12:46:24,171 4774 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - No active profile set, falling back to default profiles: default
2018-12-20 12:46:24,188 4791 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@6475472c: startup date [Thu Dec 20 12:46:24 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:46:25,220 5823 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 12:46:25,466 6069 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=ce8e6d21-1ada-3a0a-99ac-1668a24b4703
2018-12-20 12:46:25,484 6087 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:46:25,568 6171 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$937da83] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:46:25,655 6258 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 12:46:25,727 6330 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$64a01584] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:46:25,771 6374 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:46:26,098 6701 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 12:46:26,109 6712 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 12:46:26,110 6713 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 12:46:26,365 6968 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 12:46:26,365 6968 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2177 ms
2018-12-20 12:46:26,533 7136 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 12:46:26,535 7138 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 12:46:26,540 7143 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 12:46:26,541 7144 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 12:46:26,541 7144 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 12:46:26,541 7144 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 12:46:26,595 7198 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'freezeServiceController': Unsatisfied dependency expressed through field 'restResponse'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.equinoxfitness.commons.utils.RestResponse' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2018-12-20 12:46:26,596 7199 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 12:46:26,621 7224 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.a.l.AutoConfigurationReportLoggingInitializer - 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
2018-12-20 12:46:26,740 7343 [freeze] [A2ML10676] [8011] [main] [] [] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field restResponse in com.equinoxfitness.freezeservice.controller.FreezeServiceController required a bean of type 'com.equinoxfitness.commons.utils.RestResponse' that could not be found.


Action:

Consider defining a bean of type 'com.equinoxfitness.commons.utils.RestResponse' in your configuration.

2018-12-20 12:47:06,068 2008 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc: startup date [Thu Dec 20 12:47:06 IST 2018]; root of context hierarchy
2018-12-20 12:47:06,280 2220 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 12:47:06,464 2404 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:47:06,543 2483 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:47:08,512 4452 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 12:47:09,699 5639 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 12:47:09,700 5640 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - No active profile set, falling back to default profiles: default
2018-12-20 12:47:09,717 5657 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@6475472c: startup date [Thu Dec 20 12:47:09 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:47:10,611 6551 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 12:47:10,936 6876 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=ed73bd79-81a0-302f-9a50-384dc365f077
2018-12-20 12:47:10,970 6910 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:47:11,146 7086 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$937da83] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:47:11,214 7154 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 12:47:11,285 7225 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$64a01584] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:47:11,333 7273 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:47:11,735 7675 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 12:47:11,752 7692 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 12:47:11,753 7693 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 12:47:12,059 7999 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 12:47:12,060 8000 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2343 ms
2018-12-20 12:47:12,303 8243 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 12:47:12,305 8245 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 12:47:12,311 8251 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 12:47:12,312 8252 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 12:47:12,313 8253 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 12:47:12,313 8253 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 12:47:12,365 8305 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'facilityUtils': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'accountingCodeToFacilityId.map' in value "#{${accountingCodeToFacilityId.map}}"
2018-12-20 12:47:12,366 8306 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 12:47:12,402 8342 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.a.l.AutoConfigurationReportLoggingInitializer - 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
2018-12-20 12:47:12,424 8364 [freeze] [A2ML10676] [8011] [main] [] [] ERROR o.s.boot.SpringApplication - Application startup failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'facilityUtils': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'accountingCodeToFacilityId.map' in value "#{${accountingCodeToFacilityId.map}}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:761)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:867)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.refresh(EmbeddedWebApplicationContext.java:122)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:693)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:360)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1118)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1107)
	at com.equinoxfitness.freezeservice.FreezeServiceApplication.main(FreezeServiceApplication.java:12)
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'accountingCodeToFacilityId.map' in value "#{${accountingCodeToFacilityId.map}}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:174)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:126)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:236)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer$2.resolveStringValue(PropertySourcesPlaceholderConfigurer.java:172)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:831)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1086)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	... 17 common frames omitted
2018-12-20 12:47:59,641 1941 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc: startup date [Thu Dec 20 12:47:59 IST 2018]; root of context hierarchy
2018-12-20 12:47:59,854 2154 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 12:48:00,081 2381 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:48:00,162 2462 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:49:09,653 1705 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc: startup date [Thu Dec 20 12:49:09 IST 2018]; root of context hierarchy
2018-12-20 12:49:09,844 1896 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 12:49:10,049 2101 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:49:10,101 2153 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:54:32,735 1565 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc: startup date [Thu Dec 20 12:54:32 IST 2018]; root of context hierarchy
2018-12-20 12:54:32,886 1716 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 12:54:43,084 11914 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:54:43,140 11970 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b3e9c150] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:57:04,980 1500 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc: startup date [Thu Dec 20 12:57:04 IST 2018]; root of context hierarchy
2018-12-20 12:57:05,127 1647 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 12:57:05,287 1807 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:57:05,339 1859 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:26:19,823 1645 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 14:26:19 IST 2018]; root of context hierarchy
2018-12-20 14:26:20,017 1839 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 14:26:20,152 1974 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 14:26:20,212 2034 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:31:33,614 1573 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 14:31:33 IST 2018]; root of context hierarchy
2018-12-20 14:31:33,773 1732 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 14:31:33,922 1881 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 14:31:33,983 1942 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:34:40,838 1529 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 14:34:40 IST 2018]; root of context hierarchy
2018-12-20 14:34:40,995 1686 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 14:34:41,179 1870 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 14:34:41,232 1923 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 16:43:26,060 1669 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 16:43:26 IST 2018]; root of context hierarchy
2018-12-20 16:43:26,247 1856 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 16:43:26,379 1988 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 16:43:26,439 2048 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$88fb1f6e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:31:52,205 1680 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 17:31:52 IST 2018]; root of context hierarchy
2018-12-20 17:31:52,370 1845 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 17:31:52,511 1986 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:31:52,570 2045 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:34:15,878 1520 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 17:34:15 IST 2018]; root of context hierarchy
2018-12-20 17:34:16,060 1702 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 17:34:16,199 1841 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:34:16,257 1899 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:36:12,049 1527 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 17:36:12 IST 2018]; root of context hierarchy
2018-12-20 17:36:12,216 1694 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 17:36:12,377 1855 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:36:12,436 1914 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:45:01,853 1639 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 17:45:01 IST 2018]; root of context hierarchy
2018-12-20 17:45:02,053 1839 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 17:45:02,234 2020 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:45:02,325 2111 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:47:36,238 1504 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 17:47:36 IST 2018]; root of context hierarchy
2018-12-20 17:47:36,390 1656 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 17:47:36,551 1817 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:47:36,622 1888 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:56:37,545 1505 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 17:56:37 IST 2018]; root of context hierarchy
2018-12-20 17:56:37,687 1647 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 17:56:37,853 1813 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:56:37,901 1861 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:07:00,876 1563 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 18:07:00 IST 2018]; root of context hierarchy
2018-12-20 18:07:01,036 1723 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 18:07:01,194 1881 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 18:07:01,247 1934 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:10:10,500 1566 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 18:10:10 IST 2018]; root of context hierarchy
2018-12-20 18:10:10,653 1719 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 18:10:10,800 1866 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 18:10:10,855 1921 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:13:50,457 1602 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 18:13:50 IST 2018]; root of context hierarchy
2018-12-20 18:13:50,621 1766 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 18:13:50,794 1939 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 18:13:50,847 1992 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:26:11,002 1632 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Thu Dec 20 18:26:11 IST 2018]; root of context hierarchy
2018-12-20 18:26:11,178 1808 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-20 18:26:11,338 1968 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 18:26:11,392 2022 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
