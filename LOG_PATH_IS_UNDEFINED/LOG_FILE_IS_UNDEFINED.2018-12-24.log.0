2018-12-24 14:58:18,095 1656 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c: startup date [Mon Dec 24 14:58:18 IST 2018]; root of context hierarchy
2018-12-24 14:58:18,387 1948 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 14:58:18,587 2148 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 14:58:18,726 2287 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d66acf56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:00:16,200 1544 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:00:16 IST 2018]; root of context hierarchy
2018-12-24 15:00:16,363 1707 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:00:16,508 1852 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:00:16,566 1910 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:01:39,918 3518 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:01:39 IST 2018]; root of context hierarchy
2018-12-24 15:01:40,393 3993 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:01:40,702 4302 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:01:40,771 4371 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:04:08,471 1587 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:04:08 IST 2018]; root of context hierarchy
2018-12-24 15:04:08,659 1775 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:04:08,830 1946 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:04:08,897 2013 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:07:01,588 1562 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:07:01 IST 2018]; root of context hierarchy
2018-12-24 15:07:01,744 1718 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:07:01,914 1888 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:07:01,972 1946 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:12:27,538 1499 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:12:27 IST 2018]; root of context hierarchy
2018-12-24 15:12:27,689 1650 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:12:27,844 1805 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:12:27,894 1855 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:14:50,026 1534 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:14:50 IST 2018]; root of context hierarchy
2018-12-24 15:14:50,183 1691 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:14:50,331 1839 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:14:50,390 1898 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:18:56,382 1669 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:18:56 IST 2018]; root of context hierarchy
2018-12-24 15:18:56,537 1824 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:18:56,697 1984 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:18:56,752 2039 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:22:51,917 1595 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:22:51 IST 2018]; root of context hierarchy
2018-12-24 15:22:52,074 1752 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:22:52,229 1907 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:22:52,282 1960 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:28:07,179 1584 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:28:07 IST 2018]; root of context hierarchy
2018-12-24 15:28:07,331 1736 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:28:07,480 1885 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:28:07,552 1957 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:34:11,032 1558 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:34:11 IST 2018]; root of context hierarchy
2018-12-24 15:34:11,190 1716 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:34:11,374 1900 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:34:11,472 1998 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:39:48,487 11592 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:39:48 IST 2018]; root of context hierarchy
2018-12-24 15:39:48,649 11754 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:39:48,828 11933 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:39:48,877 11982 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:44:17,565 1582 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:44:17 IST 2018]; root of context hierarchy
2018-12-24 15:44:17,729 1746 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:44:17,883 1900 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:44:17,940 1957 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d66acf56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 15:53:51,815 1569 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 15:53:51 IST 2018]; root of context hierarchy
2018-12-24 15:53:51,979 1733 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 15:53:52,115 1869 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 15:53:52,175 1929 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d66acf56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 16:03:35,261 1546 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 16:03:35 IST 2018]; root of context hierarchy
2018-12-24 16:03:35,460 1745 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 16:03:35,608 1893 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 16:03:35,677 1962 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 16:03:48,589 1565 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 16:03:48 IST 2018]; root of context hierarchy
2018-12-24 16:03:48,747 1723 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 16:03:48,897 1873 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 16:03:48,956 1932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 16:06:30,322 1609 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 16:06:30 IST 2018]; root of context hierarchy
2018-12-24 16:06:30,478 1765 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 16:06:30,641 1928 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 16:06:30,699 1986 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 16:18:53,344 1718 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 16:18:53 IST 2018]; root of context hierarchy
2018-12-24 16:18:53,507 1881 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 16:18:53,660 2034 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 16:18:53,716 2090 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 16:22:44,312 1558 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 16:22:44 IST 2018]; root of context hierarchy
2018-12-24 16:22:44,462 1708 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 16:22:44,618 1864 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 16:22:44,675 1921 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 16:25:35,024 1571 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 16:25:35 IST 2018]; root of context hierarchy
2018-12-24 16:25:35,193 1740 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 16:25:35,378 1925 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 16:25:35,430 1977 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 16:28:38,645 1503 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 16:28:38 IST 2018]; root of context hierarchy
2018-12-24 16:28:38,800 1658 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 16:28:38,959 1817 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 16:28:39,012 1870 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 17:11:26,407 1666 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 17:11:26 IST 2018]; root of context hierarchy
2018-12-24 17:11:26,575 1834 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 17:11:26,711 1970 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 17:11:26,776 2035 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 17:15:35,834 1692 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 17:15:35 IST 2018]; root of context hierarchy
2018-12-24 17:15:36,037 1895 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 17:15:46,280 12138 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 17:15:46,344 12202 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 17:55:59,990 1625 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 17:55:59 IST 2018]; root of context hierarchy
2018-12-24 17:56:00,155 1790 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 17:56:00,287 1922 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 17:56:00,347 1982 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-24 17:59:12,246 1660 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Mon Dec 24 17:59:12 IST 2018]; root of context hierarchy
2018-12-24 17:59:12,407 1821 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-24 17:59:12,619 2033 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-24 17:59:12,696 2110 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
