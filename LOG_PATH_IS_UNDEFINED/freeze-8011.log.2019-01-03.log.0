2019-01-03 12:50:59,879 3408 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 12:51:01,052 4581 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 12:51:01,060 4589 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 12:51:01,092 4621 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@5c153b9e: startup date [Thu Jan 03 12:51:01 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 12:51:02,240 5769 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 12:51:02,476 6005 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 12:51:02,492 6021 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 12:51:02,570 6099 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$34bc7c02] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 12:51:02,602 6131 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 12:51:02,695 6224 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9024b703] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 12:51:02,961 6490 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ac3eba00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 12:51:03,388 6917 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 12:51:03,403 6932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 12:51:03,403 6932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 12:51:03,571 7100 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 12:51:03,571 7100 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2479 ms
2019-01-03 12:51:03,805 7334 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 12:51:03,805 7334 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 12:51:03,805 7334 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 12:51:03,805 7334 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 12:51:03,805 7334 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 12:51:03,805 7334 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 12:51:04,588 8117 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 12:51:04,638 8167 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 12:51:21,224 24753 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 12:51:21,244 24773 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 12:51:21,244 24773 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 12:51:52,378 55907 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 12:51:52,380 55909 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 12:51:52,380 55909 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 12:51:52,381 55910 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 12:51:52,383 55912 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 12:51:52,384 55913 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 12:51:52,385 55914 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 12:51:52,389 55918 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 12:51:52,390 55919 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 12:51:53,088 56617 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@5c153b9e: startup date [Thu Jan 03 12:51:01 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 12:51:53,176 56705 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 12:51:53,176 56705 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 12:51:53,195 56724 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 12:51:53,247 56776 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 12:51:55,308 58837 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@5c153b9e: startup date [Thu Jan 03 12:51:01 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 12:51:55,322 58851 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 12:51:55,393 58922 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 12:51:55,793 59322 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 12:51:55,793 59322 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 12:51:55,793 59322 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 12:51:55,793 59322 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 12:51:55,893 59422 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 12:51:55,909 59438 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 12:51:55,909 59438 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 12:51:55,909 59438 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 12:51:55,909 59438 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 12:51:55,924 59453 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 12:51:55,943 59472 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=5c153b9e,type=ConfigurationPropertiesRebinder]
2019-01-03 12:51:57,568 61097 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 12:51:57,594 61123 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 12:51:57,602 61131 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 12:51:57,749 61278 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 12:51:57,763 61292 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 12:51:57,766 61295 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 12:51:57,787 61316 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 12:51:57,793 61322 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 61.117 seconds (JVM running for 62.331)
2019-01-03 12:53:00,127 123656 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 12:53:00,128 123657 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 12:53:00,178 123707 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 50 ms
2019-01-03 12:53:41,966 165495 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 12:53:41,966 165495 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 611fccb4-18d4-4813-aef7-f2d30e312ca9 
2019-01-03 13:51:31,577 3635106 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 13:51:31,577 3635106 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@5c153b9e: startup date [Thu Jan 03 12:51:01 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 13:51:31,585 3635114 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 13:51:31,585 3635114 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 13:51:31,594 3635123 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 13:51:32,654 3636183 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 13:51:32,658 3636187 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 13:51:32,667 3636196 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 13:51:32,672 3636201 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 13:51:54,274 3350 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 13:51:55,458 4534 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 13:51:55,462 4538 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 13:51:55,484 4560 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 13:51:55 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 13:51:56,359 5435 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 13:51:56,562 5638 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 13:51:56,586 5662 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 13:51:56,649 5725 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 13:51:56,727 5803 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 13:51:56,774 5850 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 13:51:56,977 6053 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 13:51:57,420 6496 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 13:51:57,431 6507 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 13:51:57,432 6508 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 13:51:57,594 6670 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 13:51:57,594 6670 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2110 ms
2019-01-03 13:51:57,810 6886 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 13:51:57,826 6902 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 13:51:57,826 6902 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 13:51:57,826 6902 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 13:51:57,826 6902 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 13:51:57,826 6902 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 13:51:58,533 7609 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 13:51:58,626 7702 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 13:52:14,148 23224 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 13:52:14,160 23236 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 13:52:14,160 23236 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 13:52:43,367 52443 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 13:52:43,381 52457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 13:52:43,381 52457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 13:52:43,381 52457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 13:52:43,381 52457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 13:52:43,381 52457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 13:52:43,381 52457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 13:52:43,381 52457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 13:52:43,381 52457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 13:52:43,947 53023 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 13:51:55 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 13:52:44,026 53102 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 13:52:44,026 53102 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 13:52:44,057 53133 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 13:52:44,088 53164 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 13:52:46,102 55178 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 13:51:55 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 13:52:46,115 55191 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 13:52:46,163 55239 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 13:52:46,522 55598 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 13:52:46,522 55598 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 13:52:46,540 55616 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 13:52:46,540 55616 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 13:52:46,624 55700 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 13:52:46,624 55700 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 13:52:46,624 55700 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 13:52:46,624 55700 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 13:52:46,624 55700 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 13:52:46,640 55716 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 13:52:46,655 55731 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 13:52:48,252 57328 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 13:52:48,270 57346 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 13:52:48,270 57346 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 13:52:48,411 57487 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 13:52:48,426 57502 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 13:52:48,461 57537 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 13:52:48,478 57554 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 13:52:48,478 57554 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 57.35 seconds (JVM running for 58.691)
2019-01-03 13:53:26,247 95323 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 13:53:26,247 95323 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 13:53:26,276 95352 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 29 ms
2019-01-03 13:53:26,327 95403 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 13:53:26,327 95403 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated f0b0e8b9-908c-4163-ae5d-ac4ef8c7ce07 
2019-01-03 13:53:26,331 95407 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [f0b0e8b9-908c-4163-ae5d-ac4ef8c7ce07] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 13:53:26,331 95407 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [f0b0e8b9-908c-4163-ae5d-ac4ef8c7ce07] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 13:53:26,334 95410 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [f0b0e8b9-908c-4163-ae5d-ac4ef8c7ce07] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 13:53:27,291 96367 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [f0b0e8b9-908c-4163-ae5d-ac4ef8c7ce07] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 13:53:27,302 96378 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [f0b0e8b9-908c-4163-ae5d-ac4ef8c7ce07] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 970ms]
2019-01-03 13:56:53,743 302819 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 13:56:53,757 302833 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 13:51:55 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 13:56:53,758 302834 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 13:56:53,758 302834 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 13:56:53,774 302850 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 13:56:54,555 303631 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 13:56:54,555 303631 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 13:56:54,555 303631 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 13:56:54,571 303647 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 13:57:03,336 3272 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 13:57:04,477 4413 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 13:57:04,485 4421 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 13:57:04,516 4452 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@1d0d6318: startup date [Thu Jan 03 13:57:04 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 13:57:05,360 5296 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 13:57:05,563 5499 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 13:57:05,567 5503 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 13:57:05,645 5581 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$5ee89158] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 13:57:05,692 5628 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 13:57:05,754 5690 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ba50cc59] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 13:57:05,926 5862 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d66acf56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 13:57:06,254 6190 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 13:57:06,254 6190 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 13:57:06,254 6190 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 13:57:06,442 6378 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 13:57:06,442 6378 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1926 ms
2019-01-03 13:57:06,660 6596 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 13:57:06,673 6609 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 13:57:06,673 6609 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 13:57:06,673 6609 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 13:57:06,673 6609 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 13:57:06,673 6609 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 13:57:07,354 7290 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 13:57:07,401 7337 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 13:57:22,077 22013 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 13:57:22,093 22029 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 13:57:22,093 22029 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 13:57:50,979 50915 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 13:57:50,995 50931 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 13:57:50,996 50932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 13:57:50,996 50932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 13:57:50,996 50932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 13:57:50,996 50932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 13:57:50,996 50932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 13:57:50,996 50932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 13:57:50,996 50932 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 13:57:51,498 51434 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@1d0d6318: startup date [Thu Jan 03 13:57:04 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 13:57:51,576 51512 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 13:57:51,576 51512 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 13:57:51,607 51543 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 13:57:51,639 51575 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 13:57:53,623 53559 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@1d0d6318: startup date [Thu Jan 03 13:57:04 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 13:57:53,626 53562 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 13:57:53,673 53609 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 13:57:54,032 53968 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 13:57:54,032 53968 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 13:57:54,048 53984 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 13:57:54,048 53984 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 13:57:54,126 54062 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 13:57:54,142 54078 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 13:57:54,142 54078 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 13:57:54,142 54078 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 13:57:54,142 54078 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 13:57:54,157 54093 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 13:57:54,157 54093 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=1d0d6318,type=ConfigurationPropertiesRebinder]
2019-01-03 13:57:55,738 55674 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 13:57:55,745 55681 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 13:57:55,761 55697 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 13:57:55,886 55822 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 13:57:55,886 55822 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 13:57:55,901 55837 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 13:57:55,917 55853 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 13:57:55,917 55853 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 55.65 seconds (JVM running for 56.613)
2019-01-03 13:58:20,674 80610 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 13:58:20,675 80611 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 13:58:20,707 80643 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 32 ms
2019-01-03 13:58:20,759 80695 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 13:58:20,760 80696 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 4e727f8a-150c-48a6-8db1-c0e11dbffb70 
2019-01-03 13:58:20,765 80701 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [4e727f8a-150c-48a6-8db1-c0e11dbffb70] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 13:58:20,765 80701 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [4e727f8a-150c-48a6-8db1-c0e11dbffb70] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 13:58:20,771 80707 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [4e727f8a-150c-48a6-8db1-c0e11dbffb70] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 13:58:21,719 81655 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [4e727f8a-150c-48a6-8db1-c0e11dbffb70] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 13:58:21,725 81661 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [4e727f8a-150c-48a6-8db1-c0e11dbffb70] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 958ms]
2019-01-03 13:58:21,726 81662 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [4e727f8a-150c-48a6-8db1-c0e11dbffb70] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 0
2019-01-03 13:59:45,895 165831 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 13:59:45,895 165831 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@1d0d6318: startup date [Thu Jan 03 13:57:04 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 13:59:45,905 165841 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 13:59:45,905 165841 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 13:59:45,920 165856 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 13:59:47,355 167291 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 13:59:47,359 167295 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 13:59:47,375 167311 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 13:59:47,397 167333 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 14:00:10,500 4429 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 14:00:11,651 5580 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 14:00:11,659 5588 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 14:00:11,691 5620 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 14:00:11 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 14:00:12,550 6479 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 14:00:12,800 6729 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 14:00:12,825 6754 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 14:00:12,888 6817 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 14:00:12,935 6864 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 14:00:12,981 6910 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 14:00:13,153 7082 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 14:00:13,506 7435 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 14:00:13,522 7451 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 14:00:13,522 7451 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 14:00:13,662 7591 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 14:00:13,662 7591 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1971 ms
2019-01-03 14:00:13,959 7888 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 14:00:13,974 7903 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 14:00:13,974 7903 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 14:00:13,974 7903 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 14:00:13,974 7903 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 14:00:13,974 7903 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 14:00:14,943 8872 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 14:00:15,005 8934 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 14:00:30,104 24033 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 14:00:30,106 24035 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 14:00:30,106 24035 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 14:00:58,596 52525 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 14:00:58,611 52540 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 14:00:58,612 52541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 14:00:58,612 52541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 14:00:58,612 52541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 14:00:58,612 52541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 14:00:58,612 52541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 14:00:58,612 52541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 14:00:58,612 52541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 14:00:59,175 53104 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 14:00:11 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 14:00:59,253 53182 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 14:00:59,253 53182 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 14:00:59,269 53198 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 14:00:59,315 53244 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 14:01:01,237 55166 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 14:00:11 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 14:01:01,245 55174 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 14:01:01,293 55222 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 14:01:01,661 55590 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 14:01:01,661 55590 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 14:01:01,661 55590 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 14:01:01,661 55590 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 14:01:01,771 55700 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 14:01:01,786 55715 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 14:01:01,786 55715 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 14:01:01,786 55715 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 14:01:01,786 55715 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 14:01:01,802 55731 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 14:01:01,818 55747 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 14:01:03,430 57359 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 14:01:03,456 57385 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 14:01:03,456 57385 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 14:01:03,581 57510 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 14:01:03,581 57510 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 14:01:03,597 57526 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 14:01:03,628 57557 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 14:01:03,628 57557 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 57.091 seconds (JVM running for 59.384)
2019-01-03 14:01:11,163 65092 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 14:01:11,164 65093 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 14:01:11,193 65122 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 29 ms
2019-01-03 14:01:11,243 65172 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 14:01:11,245 65174 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated c4a8e4ff-e2da-46f0-9a59-8aa48f849fe6 
2019-01-03 14:01:11,248 65177 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [c4a8e4ff-e2da-46f0-9a59-8aa48f849fe6] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 14:01:11,248 65177 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [c4a8e4ff-e2da-46f0-9a59-8aa48f849fe6] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 14:01:11,252 65181 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [c4a8e4ff-e2da-46f0-9a59-8aa48f849fe6] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 14:01:12,143 66072 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [c4a8e4ff-e2da-46f0-9a59-8aa48f849fe6] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 14:01:12,150 66079 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [c4a8e4ff-e2da-46f0-9a59-8aa48f849fe6] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 901ms]
2019-01-03 14:01:12,151 66080 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [c4a8e4ff-e2da-46f0-9a59-8aa48f849fe6] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId null
2019-01-03 14:02:08,507 122436 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 14:02:08,509 122438 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 14:00:11 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 14:02:08,510 122439 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 14:02:08,510 122439 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 14:02:08,510 122439 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 14:02:09,292 123221 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 14:02:09,307 123236 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 14:02:09,307 123236 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 14:02:09,307 123236 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 14:02:16,907 3180 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 14:02:18,052 4325 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 14:02:18,061 4334 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 14:02:18,076 4349 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 14:02:18 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 14:02:18,905 5178 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 14:02:19,108 5381 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 14:02:19,138 5411 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 14:02:19,200 5473 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 14:02:19,247 5520 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 14:02:19,294 5567 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 14:02:19,485 5758 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 14:02:19,829 6102 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 14:02:19,845 6118 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 14:02:19,845 6118 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 14:02:19,985 6258 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 14:02:19,985 6258 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1909 ms
2019-01-03 14:02:20,204 6477 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 14:02:20,205 6478 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 14:02:20,206 6479 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 14:02:20,206 6479 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 14:02:20,206 6479 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 14:02:20,206 6479 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 14:02:20,894 7167 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 14:02:20,941 7214 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 14:02:37,359 23632 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 14:02:37,386 23659 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 14:02:37,386 23659 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 14:03:06,319 52592 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 14:03:06,327 52600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 14:03:06,327 52600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 14:03:06,327 52600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 14:03:06,327 52600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 14:03:06,327 52600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 14:03:06,327 52600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 14:03:06,327 52600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 14:03:06,327 52600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 14:03:06,838 53111 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 14:02:18 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 14:03:06,916 53189 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 14:03:06,916 53189 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 14:03:06,931 53204 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 14:03:06,963 53236 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 14:03:08,805 55078 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 14:02:18 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 14:03:08,821 55094 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 14:03:08,853 55126 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 14:03:09,212 55485 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 14:03:09,212 55485 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 14:03:09,212 55485 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 14:03:09,212 55485 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 14:03:09,290 55563 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 14:03:09,306 55579 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 14:03:09,306 55579 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 14:03:09,306 55579 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 14:03:09,306 55579 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 14:03:09,321 55594 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 14:03:09,340 55613 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 14:03:10,841 57114 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 14:03:10,851 57124 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 14:03:10,867 57140 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 14:03:10,976 57249 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 14:03:10,992 57265 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 14:03:10,992 57265 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 14:03:11,007 57280 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 14:03:11,023 57296 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 57.14 seconds (JVM running for 58.06)
2019-01-03 14:11:10,817 537090 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 14:11:10,818 537091 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 14:11:10,850 537123 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 32 ms
2019-01-03 14:11:10,903 537176 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 14:11:10,904 537177 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated b8de0dc0-d932-4a37-8714-22e107750ed3 
2019-01-03 14:11:10,907 537180 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [b8de0dc0-d932-4a37-8714-22e107750ed3] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 14:11:10,908 537181 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [b8de0dc0-d932-4a37-8714-22e107750ed3] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 14:11:10,911 537184 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [b8de0dc0-d932-4a37-8714-22e107750ed3] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 14:11:11,857 538130 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [b8de0dc0-d932-4a37-8714-22e107750ed3] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 14:11:11,865 538138 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [b8de0dc0-d932-4a37-8714-22e107750ed3] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 957ms]
2019-01-03 14:11:11,866 538139 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [b8de0dc0-d932-4a37-8714-22e107750ed3] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 15:30:53,109 5319382 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 15:30:53,113 5319386 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 14:02:18 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 15:30:53,255 5319528 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 15:30:53,258 5319531 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 15:30:53,311 5319584 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 15:30:54,571 5320844 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 15:30:54,601 5320874 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 15:30:54,653 5320926 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 15:30:54,675 5320948 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 15:32:30,624 3600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 15:32:31,824 4800 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 15:32:31,826 4802 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 15:32:31,840 4816 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 15:32:31 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 15:32:32,852 5828 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 15:32:33,081 6057 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 15:32:33,100 6076 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 15:32:33,181 6157 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 15:32:33,260 6236 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 15:32:33,322 6298 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 15:32:33,523 6499 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 15:32:34,447 7423 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 15:32:34,458 7434 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 15:32:34,459 7435 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 15:32:34,635 7611 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 15:32:34,636 7612 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2797 ms
2019-01-03 15:32:34,878 7854 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 15:32:34,880 7856 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 15:32:34,884 7860 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 15:32:34,885 7861 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 15:32:34,886 7862 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 15:32:34,886 7862 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 15:32:35,740 8716 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 15:32:35,823 8799 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 15:32:54,083 27059 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 15:32:54,107 27083 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 15:32:54,107 27083 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 15:33:24,234 57210 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 15:33:24,236 57212 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 15:33:24,236 57212 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 15:33:24,237 57213 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 15:33:24,239 57215 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 15:33:24,240 57216 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 15:33:24,241 57217 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 15:33:24,245 57221 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 15:33:24,245 57221 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 15:33:24,833 57809 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 15:32:31 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 15:33:24,915 57891 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 15:33:24,915 57891 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 15:33:24,943 57919 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 15:33:24,975 57951 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 15:33:27,401 60377 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 15:32:31 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 15:33:27,403 60379 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 15:33:27,457 60433 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 15:33:28,001 60977 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 15:33:28,001 60977 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 15:33:28,005 60981 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 15:33:28,005 60981 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 15:33:28,089 61065 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 15:33:28,099 61075 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 15:33:28,100 61076 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 15:33:28,102 61078 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 15:33:28,105 61081 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 15:33:28,120 61096 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 15:33:28,131 61107 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 15:33:29,793 62769 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 15:33:29,814 62790 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 15:33:29,822 62798 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 15:33:29,964 62940 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 15:33:29,976 62952 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 15:33:30,004 62980 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 15:33:30,024 63000 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 15:33:30,030 63006 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 62.767 seconds (JVM running for 64.19)
2019-01-03 15:33:40,110 73086 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 15:33:40,110 73086 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 15:33:40,140 73116 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 29 ms
2019-01-03 15:33:40,193 73169 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 15:33:40,193 73169 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 158c95c6-494d-4029-bb8b-1d7458621831 
2019-01-03 15:33:40,197 73173 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 15:33:40,197 73173 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 15:33:40,201 73177 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 15:33:41,204 74180 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 15:33:41,206 74182 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 1008ms]
2019-01-03 15:33:41,206 74182 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 15:33:41,206 74182 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 15:33:42,973 75949 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 1767 ms
2019-01-03 15:33:42,979 75955 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 15:33:42,986 75962 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 15:33:42,987 75963 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 15:33:44,585 77561 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :1c6e88de-1e12-49f6-a286-97c69a8a04b2
2019-01-03 15:33:44,585 77561 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C403E321B87D252601130289E8F360AABA374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200
2019-01-03 15:33:44,592 77568 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 15:33:45,142 78118 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error 500 Internal Server Error
2019-01-03 15:33:45,155 78131 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [158c95c6-494d-4029-bb8b-1d7458621831] ERROR c.e.c.e.a.ExceptionControllerAdvice - Exception occurred due to: 
java.lang.NullPointerException: null
	at com.equinoxfitness.freezeservice.service.impl.FreezeMosoServiceImpl.freezeExtension(FreezeMosoServiceImpl.java:172)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.freezeExtension(FreezeServiceImpl.java:246)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(FreezeServiceController.java:121)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$FastClassBySpringCGLIB$$60e58a08.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.CorrelationAspect.controllerMethods(CorrelationAspect.java:68)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$EnhancerBySpringCGLIB$$2b313eb2.freezeExtension(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2019-01-03 18:14:18,654 9711630 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 18:14:18,667 9711643 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 15:32:31 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:14:18,824 9711800 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 18:14:18,828 9711804 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 18:14:18,902 9711878 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:14:20,219 9713195 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 18:14:20,235 9713211 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:14:20,277 9713253 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 18:14:20,299 9713275 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 18:14:38,432 4076 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 18:14:39,627 5271 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 18:14:39,629 5273 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 18:14:39,643 5287 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:14:39 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:14:40,880 6524 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 18:14:41,235 6879 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 18:14:41,262 6906 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:14:41,373 7017 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:14:41,447 7091 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 18:14:41,532 7176 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:14:41,805 7449 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:14:42,328 7972 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 18:14:42,342 7986 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 18:14:42,343 7987 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 18:14:42,550 8194 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 18:14:42,551 8195 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2908 ms
2019-01-03 18:14:42,891 8535 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 18:14:42,896 8540 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 18:14:42,902 8546 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 18:14:42,903 8547 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 18:14:42,904 8548 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 18:14:42,904 8548 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 18:14:43,979 9623 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 18:14:44,076 9720 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 18:14:58,917 24561 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 18:14:58,938 24582 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 18:14:58,938 24582 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 18:15:27,519 53163 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 18:15:27,520 53164 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 18:15:27,521 53165 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 18:15:27,522 53166 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 18:15:27,525 53169 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 18:15:27,526 53170 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 18:15:27,527 53171 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 18:15:27,532 53176 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 18:15:27,533 53177 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 18:15:28,314 53958 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:14:39 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:15:28,451 54095 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:15:28,452 54096 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:15:28,514 54158 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 18:15:28,569 54213 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:15:31,161 56805 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:14:39 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 18:15:31,163 56807 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 18:15:31,229 56873 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 18:15:31,936 57580 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:15:31,937 57581 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:15:31,953 57597 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:15:31,953 57597 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:15:32,106 57750 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 18:15:32,128 57772 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 18:15:32,128 57772 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 18:15:32,131 57775 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 18:15:32,134 57778 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 18:15:32,153 57797 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 18:15:32,168 57812 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 18:15:34,751 60395 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 18:15:34,780 60424 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 18:15:34,793 60437 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 18:15:34,961 60605 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:15:34,984 60628 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 18:15:35,006 60650 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 18:15:35,031 60675 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 18:15:35,037 60681 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 60.435 seconds (JVM running for 61.933)
2019-01-03 18:15:44,548 70192 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:15:44,549 70193 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 18:15:44,591 70235 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 42 ms
2019-01-03 18:15:44,664 70308 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 18:15:44,665 70309 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated b55a1dc8-30a7-4d3f-924a-0abfd2f359e4 
2019-01-03 18:15:44,669 70313 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 18:15:44,669 70313 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 18:15:44,672 70316 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 18:15:45,542 71186 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 18:15:45,545 71189 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 875ms]
2019-01-03 18:15:45,545 71189 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 18:15:45,545 71189 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 18:15:48,213 73857 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 2668 ms
2019-01-03 18:15:48,216 73860 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 18:15:48,221 73865 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 18:15:48,222 73866 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 18:16:09,492 95136 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] ERROR c.e.f.u.MosoSessionMediatorForFreeze - GetSession failed !!!
2019-01-03 18:16:09,492 95136 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] ERROR c.e.f.u.MosoSessionMediatorForFreeze - I/O error on POST request for "https://equinoxstaging.mosocloud.com:443/api/2/session/start": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
2019-01-03 18:16:09,492 95136 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :null
2019-01-03 18:16:09,493 95137 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :null
2019-01-03 18:16:09,500 95144 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 18:16:30,515 116159 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [b55a1dc8-30a7-4d3f-924a-0abfd2f359e4] ERROR c.e.c.e.a.ExceptionControllerAdvice - Exception occurred due to: 
org.springframework.web.client.ResourceAccessException: I/O error on PUT request for "https://equinoxstaging.mosocloud.com:443/api/2/agreements/freezeagreement": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:666)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:621)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:540)
	at com.equinoxfitness.freezeservice.service.impl.FreezeMosoServiceImpl.freezeExtension(FreezeMosoServiceImpl.java:176)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.freezeExtension(FreezeServiceImpl.java:256)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(FreezeServiceController.java:121)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$FastClassBySpringCGLIB$$60e58a08.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.CorrelationAspect.controllerMethods(CorrelationAspect.java:68)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$EnhancerBySpringCGLIB$$2b313eb2.freezeExtension(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.security.ssl.SSLSocketImpl.connect(SSLSocketImpl.java:668)
	at sun.security.ssl.BaseSSLSocketImpl.connect(BaseSSLSocketImpl.java:173)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:432)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:527)
	at sun.net.www.protocol.https.HttpsClient.<init>(HttpsClient.java:264)
	at sun.net.www.protocol.https.HttpsClient.New(HttpsClient.java:367)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.getNewHttpClient(AbstractDelegateHttpsURLConnection.java:191)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1105)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:999)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:177)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:153)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:78)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:652)
	... 77 common frames omitted
2019-01-03 18:17:46,053 191697 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 18:17:46,054 191698 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated adcb3c8e-abaf-4d1c-9a38-d3402123539c 
2019-01-03 18:17:46,055 191699 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 18:17:46,055 191699 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 18:17:46,055 191699 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 18:17:46,942 192586 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 18:17:46,942 192586 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 887ms]
2019-01-03 18:17:46,943 192587 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 18:17:46,943 192587 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 18:17:48,276 193920 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 1333 ms
2019-01-03 18:17:48,276 193920 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 18:17:48,276 193920 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 18:17:48,276 193920 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 18:17:50,062 195706 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :c6cd18c0-a910-41e6-aca8-7947f694d81f
2019-01-03 18:17:50,062 195706 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200
2019-01-03 18:17:50,064 195708 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 18:17:50,862 196506 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [adcb3c8e-abaf-4d1c-9a38-d3402123539c] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error 500 Internal Server Error
2019-01-03 18:19:52,015 317659 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 18:19:52,016 317660 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:14:39 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:19:52,024 317668 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 18:19:52,024 317668 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 18:19:52,037 317681 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:19:53,415 319059 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 18:19:53,424 319068 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:19:53,436 319080 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 18:19:53,442 319086 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 18:20:04,212 4764 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 18:20:05,485 6037 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 18:20:05,487 6039 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 18:20:05,505 6057 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:20:05 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:20:07,009 7561 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 18:20:07,394 7946 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 18:20:07,426 7978 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:20:07,577 8129 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:20:07,663 8215 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 18:20:07,761 8313 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:20:08,091 8643 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:20:08,724 9276 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 18:20:08,741 9293 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 18:20:08,742 9294 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 18:20:08,994 9546 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 18:20:08,995 9547 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 3490 ms
2019-01-03 18:20:09,396 9948 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 18:20:09,398 9950 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 18:20:09,404 9956 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 18:20:09,405 9957 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 18:20:09,405 9957 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 18:20:09,405 9957 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 18:20:10,843 11395 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 18:20:10,946 11498 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 18:20:27,397 27949 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 18:20:27,424 27976 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 18:20:27,424 27976 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 18:21:02,005 62557 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 18:21:02,007 62559 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 18:21:02,008 62560 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 18:21:02,009 62561 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 18:21:02,013 62565 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 18:21:02,016 62568 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 18:21:02,019 62571 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 18:21:02,025 62577 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 18:21:02,026 62578 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 18:21:03,292 63844 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:20:05 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:21:03,489 64041 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:21:03,490 64042 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:21:03,558 64110 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 18:21:03,627 64179 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:21:09,355 69907 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:20:05 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 18:21:09,357 69909 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 18:21:09,507 70059 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 18:21:10,381 70933 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:21:10,382 70934 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:21:10,391 70943 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:21:10,391 70943 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:21:10,616 71168 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 18:21:10,634 71186 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 18:21:10,635 71187 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 18:21:10,639 71191 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 18:21:10,644 71196 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 18:21:10,665 71217 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 18:21:10,686 71238 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 18:21:15,546 76098 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 18:21:15,594 76146 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 18:21:15,615 76167 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 18:21:15,923 76475 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:21:15,940 76492 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 18:21:15,975 76527 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 18:21:16,014 76566 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 18:21:16,022 76574 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 76.3 seconds (JVM running for 78.127)
2019-01-03 18:23:12,835 193387 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:23:12,835 193387 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 18:23:12,880 193432 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 44 ms
2019-01-03 18:23:12,962 193514 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 18:23:12,963 193515 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 0e550f11-8a19-40c6-9fda-6746cf041e77 
2019-01-03 18:23:12,974 193526 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 18:23:12,975 193527 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 18:23:12,980 193532 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 18:23:14,383 194935 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 18:23:14,385 194937 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 1410ms]
2019-01-03 18:23:14,385 194937 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 18:23:14,386 194938 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 18:23:17,531 198083 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 3145 ms
2019-01-03 18:23:17,534 198086 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 18:23:17,540 198092 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 18:23:17,540 198092 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 18:23:19,322 199874 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :389b6a17-24cb-45ff-badb-4a9d44326b63
2019-01-03 18:23:19,322 199874 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200
2019-01-03 18:23:19,330 199882 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 18:23:19,759 200311 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [0e550f11-8a19-40c6-9fda-6746cf041e77] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Cannot update the start date when Suspension is active (10104)","Data":null}
2019-01-03 18:25:16,435 316987 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 18:25:16,435 316987 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:20:05 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:25:16,442 316994 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 18:25:16,443 316995 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 18:25:16,456 317008 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:25:17,481 318033 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 18:25:17,489 318041 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:25:17,499 318051 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 18:25:17,504 318056 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 18:25:26,618 4364 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 18:25:27,821 5567 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 18:25:27,823 5569 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 18:25:27,841 5587 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:25:27 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:25:29,074 6820 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 18:25:29,408 7154 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 18:25:29,435 7181 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:25:29,548 7294 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:25:29,621 7367 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 18:25:29,703 7449 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:25:29,942 7688 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:25:30,483 8229 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 18:25:30,494 8240 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 18:25:30,496 8242 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 18:25:30,693 8439 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 18:25:30,694 8440 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2853 ms
2019-01-03 18:25:31,075 8821 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 18:25:31,077 8823 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 18:25:31,083 8829 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 18:25:31,084 8830 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 18:25:31,084 8830 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 18:25:31,084 8830 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 18:25:32,688 10434 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 18:25:32,795 10541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 18:25:50,731 28477 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 18:25:50,753 28499 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 18:25:50,753 28499 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 18:26:25,121 62867 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 18:26:25,122 62868 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 18:26:25,123 62869 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 18:26:25,124 62870 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 18:26:25,127 62873 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 18:26:25,128 62874 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 18:26:25,129 62875 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 18:26:25,133 62879 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 18:26:25,134 62880 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 18:26:25,887 63633 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:25:27 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:26:26,004 63750 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:26:26,005 63751 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:26:26,043 63789 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 18:26:26,089 63835 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:26:29,080 66826 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:25:27 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 18:26:29,082 66828 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 18:26:29,163 66909 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 18:26:29,676 67422 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:26:29,677 67423 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:26:29,685 67431 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:26:29,685 67431 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:26:29,824 67570 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 18:26:29,851 67597 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 18:26:29,851 67597 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 18:26:29,854 67600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 18:26:29,856 67602 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 18:26:29,873 67619 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 18:26:29,890 67636 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 18:26:32,163 69909 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 18:26:32,185 69931 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 18:26:32,193 69939 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 18:26:32,348 70094 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:26:32,369 70115 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 18:26:32,375 70121 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 18:26:32,400 70146 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 18:26:32,406 70152 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 69.883 seconds (JVM running for 71.475)
2019-01-03 18:30:15,613 293359 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:30:15,613 293359 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 18:30:15,731 293477 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 118 ms
2019-01-03 18:30:17,353 295099 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 18:30:17,354 295100 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 49193c15-74f5-40fd-b3fb-88f313a8fdec 
2019-01-03 18:30:17,360 295106 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 18:30:17,360 295106 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 18:30:17,423 295169 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 18:30:19,069 296815 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 18:30:19,072 296818 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 1711ms]
2019-01-03 18:30:19,073 296819 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 18:30:19,073 296819 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 18:30:21,005 298751 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 1932 ms
2019-01-03 18:30:21,009 298755 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 18:30:21,017 298763 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 18:30:21,017 298763 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 18:30:22,872 300618 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :89942901-7381-44d8-8ae3-44d671df65af
2019-01-03 18:30:22,873 300619 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200
2019-01-03 18:30:22,883 300629 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 18:30:23,444 301190 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [49193c15-74f5-40fd-b3fb-88f313a8fdec] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Cannot update the start date when Suspension is active (10104)","Data":null}
2019-01-03 18:33:16,769 474515 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 18:33:16,769 474515 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:25:27 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:33:16,777 474523 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 18:33:16,777 474523 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 18:33:16,789 474535 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:33:18,046 475792 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 18:33:18,049 475795 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:33:18,057 475803 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 18:33:18,063 475809 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 18:33:26,819 4060 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 18:33:28,005 5246 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 18:33:28,007 5248 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 18:33:28,021 5262 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:33:28 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:33:29,218 6459 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 18:33:29,561 6802 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 18:33:29,588 6829 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:33:29,730 6971 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:33:29,809 7050 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 18:33:29,891 7132 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:33:30,136 7377 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:33:30,641 7882 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 18:33:30,653 7894 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 18:33:30,654 7895 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 18:33:30,857 8098 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 18:33:30,858 8099 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2836 ms
2019-01-03 18:33:31,157 8398 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 18:33:31,158 8399 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 18:33:31,162 8403 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 18:33:31,163 8404 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 18:33:31,163 8404 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 18:33:31,163 8404 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 18:33:32,201 9442 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 18:33:32,277 9518 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 18:33:50,232 27473 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 18:33:50,253 27494 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 18:33:50,254 27495 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 18:34:24,342 61583 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 18:34:24,344 61585 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 18:34:24,344 61585 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 18:34:24,346 61587 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 18:34:24,351 61592 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 18:34:24,352 61593 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 18:34:24,354 61595 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 18:34:24,359 61600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 18:34:24,360 61601 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 18:34:25,103 62344 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:33:28 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:34:25,227 62468 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:34:25,227 62468 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:34:25,264 62505 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 18:34:25,306 62547 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:34:28,122 65363 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:33:28 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 18:34:28,124 65365 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 18:34:28,193 65434 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 18:34:28,699 65940 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:34:28,699 65940 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:34:28,705 65946 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:34:28,705 65946 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:34:28,834 66075 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 18:34:28,848 66089 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 18:34:28,850 66091 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 18:34:28,852 66093 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 18:34:28,856 66097 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 18:34:28,873 66114 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 18:34:28,887 66128 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 18:34:31,099 68340 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 18:34:31,120 68361 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 18:34:31,129 68370 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 18:34:31,281 68522 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:34:31,298 68539 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 18:34:31,302 68543 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 18:34:31,325 68566 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 18:34:31,331 68572 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 68.331 seconds (JVM running for 69.693)
2019-01-03 18:34:38,849 76090 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:34:38,849 76090 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 18:34:38,887 76128 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 38 ms
2019-01-03 18:34:38,950 76191 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 18:34:38,956 76197 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 1f6be624-aff4-430f-a21a-ddca978ebbe9 
2019-01-03 18:34:38,963 76204 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 18:34:38,964 76205 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 18:34:38,968 76209 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 18:34:40,414 77655 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 18:34:40,416 77657 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 1451ms]
2019-01-03 18:34:40,417 77658 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 18:34:40,417 77658 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 18:34:42,336 79577 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 1919 ms
2019-01-03 18:34:42,338 79579 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 18:34:42,344 79585 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 18:34:42,345 79586 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 18:34:43,978 81219 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :17350582-806a-4094-9839-bf4f225daa20
2019-01-03 18:34:43,979 81220 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C403E321B87D252601130289E8F360AABA374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200
2019-01-03 18:34:43,987 81228 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 18:34:44,417 81658 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Cannot update the start date when Suspension is active (10104)","Data":null}
2019-01-03 18:34:44,428 81669 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeServiceImpl - Success 
2019-01-03 18:34:44,428 81669 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeServiceImpl - null
2019-01-03 18:34:44,793 82034 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - ID 850
2019-01-03 18:34:44,793 82034 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] DEBUG c.e.f.s.impl.FreezeServiceImpl - Item price 30.0
2019-01-03 18:34:44,800 82041 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [1f6be624-aff4-430f-a21a-ddca978ebbe9] ERROR c.e.c.e.a.ExceptionControllerAdvice - Exception occurred due to: 
java.lang.IllegalArgumentException: Not enough variable values available to expand 'authToken'
	at org.springframework.web.util.UriComponents$VarArgsTemplateVariables.getValue(UriComponents.java:329)
	at org.springframework.web.util.HierarchicalUriComponents$QueryUriTemplateVariables.getValue(HierarchicalUriComponents.java:899)
	at org.springframework.web.util.UriComponents.expandUriComponent(UriComponents.java:232)
	at org.springframework.web.util.HierarchicalUriComponents.expandQueryParams(HierarchicalUriComponents.java:347)
	at org.springframework.web.util.HierarchicalUriComponents.expandInternal(HierarchicalUriComponents.java:332)
	at org.springframework.web.util.HierarchicalUriComponents.expandInternal(HierarchicalUriComponents.java:48)
	at org.springframework.web.util.UriComponents.expand(UriComponents.java:165)
	at org.springframework.web.util.UriComponentsBuilder.buildAndExpand(UriComponentsBuilder.java:360)
	at org.springframework.web.util.DefaultUriTemplateHandler.expandAndEncode(DefaultUriTemplateHandler.java:140)
	at org.springframework.web.util.DefaultUriTemplateHandler.expandInternal(DefaultUriTemplateHandler.java:104)
	at org.springframework.web.util.AbstractUriTemplateHandler.expand(AbstractUriTemplateHandler.java:106)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:612)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:531)
	at com.equinoxfitness.freezeservice.service.impl.FreezeMosoServiceImpl.createAndfinalizeInvoice(FreezeMosoServiceImpl.java:204)
	at com.equinoxfitness.freezeservice.service.impl.FreezeServiceImpl.freezeExtension(FreezeServiceImpl.java:281)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(FreezeServiceController.java:122)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$FastClassBySpringCGLIB$$60e58a08.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:85)
	at com.equinoxfitness.commons.aspect.CorrelationAspect.controllerMethods(CorrelationAspect.java:68)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:629)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:618)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController$$EnhancerBySpringCGLIB$$2b313eb2.freezeExtension(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2019-01-03 18:41:33,632 490873 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 18:41:33,633 490874 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:33:28 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:41:33,640 490881 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 18:41:33,640 490881 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 18:41:33,651 490892 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:41:34,651 491892 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 18:41:34,658 491899 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:41:34,667 491908 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 18:41:34,673 491914 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 18:41:57,624 4352 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 18:41:58,831 5559 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 18:41:58,833 5561 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 18:41:58,848 5576 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:41:58 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:42:00,028 6756 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 18:42:00,457 7185 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 18:42:00,484 7212 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:42:00,592 7320 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:42:00,647 7375 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 18:42:00,739 7467 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:42:01,023 7751 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:42:01,580 8308 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 18:42:01,593 8321 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 18:42:01,594 8322 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 18:42:01,866 8594 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 18:42:01,867 8595 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 3019 ms
2019-01-03 18:42:02,236 8964 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 18:42:02,238 8966 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 18:42:02,242 8970 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 18:42:02,242 8970 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 18:42:02,242 8970 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 18:42:02,242 8970 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 18:42:03,359 10087 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 18:42:03,437 10165 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 18:42:21,587 28315 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 18:42:21,606 28334 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 18:42:21,606 28334 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 18:43:19,268 85996 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 18:43:19,269 85997 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 18:43:19,270 85998 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 18:43:19,271 85999 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 18:43:19,273 86001 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 18:43:19,274 86002 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 18:43:19,275 86003 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 18:43:19,279 86007 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 18:43:19,280 86008 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 18:43:20,024 86752 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:41:58 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:43:20,119 86847 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:43:20,120 86848 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:43:20,157 86885 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 18:43:20,209 86937 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:43:22,718 89446 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:41:58 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 18:43:22,720 89448 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 18:43:22,799 89527 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 18:43:23,270 89998 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:43:23,270 89998 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:43:23,276 90004 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:43:23,276 90004 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:43:23,410 90138 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 18:43:23,426 90154 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 18:43:23,427 90155 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 18:43:23,432 90160 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 18:43:23,436 90164 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 18:43:23,455 90183 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 18:43:23,478 90206 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 18:43:25,729 92457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 18:43:25,754 92482 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 18:43:25,763 92491 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 18:43:25,919 92647 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:43:25,934 92662 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 18:43:25,938 92666 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 18:43:25,964 92692 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 18:43:25,971 92699 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 92.47 seconds (JVM running for 93.804)
2019-01-03 18:43:33,871 100599 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:43:33,871 100599 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 18:43:33,910 100638 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 39 ms
2019-01-03 18:43:33,985 100713 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 18:43:33,988 100716 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 2b17a3fa-2e04-40b7-965c-393c8504ef15 
2019-01-03 18:43:33,993 100721 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 18:43:33,993 100721 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 18:43:33,996 100724 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 18:43:34,992 101720 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 18:43:34,994 101722 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 1000ms]
2019-01-03 18:43:34,995 101723 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 18:43:34,995 101723 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 18:43:37,273 104001 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 2278 ms
2019-01-03 18:43:37,277 104005 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 18:43:37,284 104012 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 18:43:37,285 104013 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 18:43:38,846 105574 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :60cf9284-06f2-403f-84ee-40e35e8920da
2019-01-03 18:43:38,846 105574 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200
2019-01-03 18:43:38,854 105582 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 18:43:39,266 105994 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Cannot update the start date when Suspension is active (10104)","Data":null}
2019-01-03 18:43:39,275 106003 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeServiceImpl - Success 
2019-01-03 18:43:39,276 106004 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeServiceImpl - null
2019-01-03 18:43:39,593 106321 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - ID 850
2019-01-03 18:43:39,593 106321 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] DEBUG c.e.f.s.impl.FreezeServiceImpl - Item price 30.0
2019-01-03 18:43:40,446 107174 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [2b17a3fa-2e04-40b7-965c-393c8504ef15] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Item 1343 is not marked as available for POS. It can only be used in a recurring setup, not directly on a transaction. (10039)","Data":null}
2019-01-03 18:47:01,071 307799 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 18:47:01,072 307800 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:41:58 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:47:01,078 307806 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 18:47:01,078 307806 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 18:47:01,090 307818 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:47:02,054 308782 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 18:47:02,057 308785 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:47:02,065 308793 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 18:47:02,071 308799 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2019-01-03 18:47:12,239 3999 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2019-01-03 18:47:13,422 5182 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2019-01-03 18:47:13,424 5184 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag,stag
2019-01-03 18:47:13,438 5198 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:47:13 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:47:14,690 6450 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2019-01-03 18:47:14,995 6755 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=f0d56419-b8cd-36e6-ad69-4c9da087cbdd
2019-01-03 18:47:15,019 6779 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:47:15,128 6888 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$99f16c5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:47:15,177 6937 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2019-01-03 18:47:15,261 7021 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f559a75e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:47:15,513 7273 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:47:15,998 7758 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2019-01-03 18:47:16,011 7771 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2019-01-03 18:47:16,012 7772 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2019-01-03 18:47:16,202 7962 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2019-01-03 18:47:16,202 7962 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2764 ms
2019-01-03 18:47:16,536 8296 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2019-01-03 18:47:16,538 8298 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2019-01-03 18:47:16,542 8302 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2019-01-03 18:47:16,543 8303 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2019-01-03 18:47:16,543 8303 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2019-01-03 18:47:16,544 8304 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2019-01-03 18:47:17,559 9319 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2019-01-03 18:47:17,637 9397 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2019-01-03 18:47:38,976 30736 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.saaj.SaajSoapMessageFactory - Creating SAAJ 1.3 MessageFactory with SOAP 1.1 Protocol
2019-01-03 18:47:38,996 30756 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Loaded MoSoSessionMediator...
2019-01-03 18:47:38,996 30756 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.u.MosoSessionMediatorForFreeze - Default URI: null
2019-01-03 18:48:37,987 89747 [freeze] [A2ML10676] [8011] [main] [] [] ERROR o.a.tomcat.jdbc.pool.ConnectionPool - Unable to create initial connections of pool.
com.microsoft.sqlserver.jdbc.SQLServerException: The TCP/IP connection to the host stag-websql.equinoxfitness.com, port 1433 has failed. Error: "Connection timed out: no further information. Verify the connection properties. Make sure that an instance of SQL Server is running on the host and accepting TCP/IP connections at the port. Make sure that TCP connections to the port are not blocked by a firewall.".
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:206)
	at com.microsoft.sqlserver.jdbc.SQLServerException.ConvertConnectExceptionToSQLServerException(SQLServerException.java:257)
	at com.microsoft.sqlserver.jdbc.SocketFinder.findSocket(IOBuffer.java:2385)
	at com.microsoft.sqlserver.jdbc.TDSChannel.open(IOBuffer.java:567)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1955)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:1616)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:1447)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:788)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:1187)
	at org.apache.tomcat.jdbc.pool.PooledConnection.connectUsingDriver(PooledConnection.java:310)
	at org.apache.tomcat.jdbc.pool.PooledConnection.connect(PooledConnection.java:203)
	at org.apache.tomcat.jdbc.pool.ConnectionPool.createConnection(ConnectionPool.java:735)
	at org.apache.tomcat.jdbc.pool.ConnectionPool.borrowConnection(ConnectionPool.java:667)
	at org.apache.tomcat.jdbc.pool.ConnectionPool.init(ConnectionPool.java:482)
	at org.apache.tomcat.jdbc.pool.ConnectionPool.<init>(ConnectionPool.java:154)
	at org.apache.tomcat.jdbc.pool.DataSourceProxy.pCreatePool(DataSourceProxy.java:118)
	at org.apache.tomcat.jdbc.pool.DataSourceProxy.createPool(DataSourceProxy.java:107)
	at org.apache.tomcat.jdbc.pool.DataSourceProxy.getConnection(DataSourceProxy.java:131)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:77)
	at org.springframework.jdbc.support.JdbcUtils.extractDatabaseMetaData(JdbcUtils.java:326)
	at org.springframework.jdbc.support.JdbcUtils.extractDatabaseMetaData(JdbcUtils.java:366)
	at org.springframework.jdbc.support.SQLErrorCodesFactory.getErrorCodes(SQLErrorCodesFactory.java:212)
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.setDataSource(SQLErrorCodeSQLExceptionTranslator.java:134)
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.<init>(SQLErrorCodeSQLExceptionTranslator.java:97)
	at org.springframework.jdbc.support.JdbcAccessor.getExceptionTranslator(JdbcAccessor.java:99)
	at org.springframework.jdbc.support.JdbcAccessor.afterPropertiesSet(JdbcAccessor.java:138)
	at org.springframework.jdbc.core.JdbcTemplate.<init>(JdbcTemplate.java:181)
	at com.equinoxfitness.events.config.DatabaseConfiguration.paperTrailJdbcTemplate(DatabaseConfiguration.java:43)
	at com.equinoxfitness.events.config.DatabaseConfiguration$$EnhancerBySpringCGLIB$$9c64c4f7.CGLIB$paperTrailJdbcTemplate$2(<generated>)
	at com.equinoxfitness.events.config.DatabaseConfiguration$$EnhancerBySpringCGLIB$$9c64c4f7$$FastClassBySpringCGLIB$$978a0b0f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:228)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:358)
	at com.equinoxfitness.events.config.DatabaseConfiguration$$EnhancerBySpringCGLIB$$9c64c4f7.paperTrailJdbcTemplate(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:162)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:588)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1173)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1067)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:761)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:867)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.refresh(EmbeddedWebApplicationContext.java:122)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:693)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:360)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1118)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1107)
	at com.equinoxfitness.freezeservice.FreezeServiceApplication.main(FreezeServiceApplication.java:13)
2019-01-03 18:48:37,996 89756 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.j.support.SQLErrorCodesFactory - Error while extracting database name - falling back to empty error codes
org.springframework.jdbc.support.MetaDataAccessException: Could not get Connection for extracting meta data; nested exception is org.springframework.jdbc.CannotGetJdbcConnectionException: Could not get JDBC Connection; nested exception is com.microsoft.sqlserver.jdbc.SQLServerException: The TCP/IP connection to the host stag-websql.equinoxfitness.com, port 1433 has failed. Error: "Connection timed out: no further information. Verify the connection properties. Make sure that an instance of SQL Server is running on the host and accepting TCP/IP connections at the port. Make sure that TCP connections to the port are not blocked by a firewall.".
	at org.springframework.jdbc.support.JdbcUtils.extractDatabaseMetaData(JdbcUtils.java:339)
	at org.springframework.jdbc.support.JdbcUtils.extractDatabaseMetaData(JdbcUtils.java:366)
	at org.springframework.jdbc.support.SQLErrorCodesFactory.getErrorCodes(SQLErrorCodesFactory.java:212)
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.setDataSource(SQLErrorCodeSQLExceptionTranslator.java:134)
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.<init>(SQLErrorCodeSQLExceptionTranslator.java:97)
	at org.springframework.jdbc.support.JdbcAccessor.getExceptionTranslator(JdbcAccessor.java:99)
	at org.springframework.jdbc.support.JdbcAccessor.afterPropertiesSet(JdbcAccessor.java:138)
	at org.springframework.jdbc.core.JdbcTemplate.<init>(JdbcTemplate.java:181)
	at com.equinoxfitness.events.config.DatabaseConfiguration.paperTrailJdbcTemplate(DatabaseConfiguration.java:43)
	at com.equinoxfitness.events.config.DatabaseConfiguration$$EnhancerBySpringCGLIB$$9c64c4f7.CGLIB$paperTrailJdbcTemplate$2(<generated>)
	at com.equinoxfitness.events.config.DatabaseConfiguration$$EnhancerBySpringCGLIB$$9c64c4f7$$FastClassBySpringCGLIB$$978a0b0f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:228)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:358)
	at com.equinoxfitness.events.config.DatabaseConfiguration$$EnhancerBySpringCGLIB$$9c64c4f7.paperTrailJdbcTemplate(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:162)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:588)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1173)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1067)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:761)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:867)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.refresh(EmbeddedWebApplicationContext.java:122)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:693)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:360)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1118)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1107)
	at com.equinoxfitness.freezeservice.FreezeServiceApplication.main(FreezeServiceApplication.java:13)
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Could not get JDBC Connection; nested exception is com.microsoft.sqlserver.jdbc.SQLServerException: The TCP/IP connection to the host stag-websql.equinoxfitness.com, port 1433 has failed. Error: "Connection timed out: no further information. Verify the connection properties. Make sure that an instance of SQL Server is running on the host and accepting TCP/IP connections at the port. Make sure that TCP connections to the port are not blocked by a firewall.".
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.support.JdbcUtils.extractDatabaseMetaData(JdbcUtils.java:326)
	... 89 common frames omitted
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: The TCP/IP connection to the host stag-websql.equinoxfitness.com, port 1433 has failed. Error: "Connection timed out: no further information. Verify the connection properties. Make sure that an instance of SQL Server is running on the host and accepting TCP/IP connections at the port. Make sure that TCP connections to the port are not blocked by a firewall.".
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:206)
	at com.microsoft.sqlserver.jdbc.SQLServerException.ConvertConnectExceptionToSQLServerException(SQLServerException.java:257)
	at com.microsoft.sqlserver.jdbc.SocketFinder.findSocket(IOBuffer.java:2385)
	at com.microsoft.sqlserver.jdbc.TDSChannel.open(IOBuffer.java:567)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1955)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:1616)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:1447)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:788)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:1187)
	at org.apache.tomcat.jdbc.pool.PooledConnection.connectUsingDriver(PooledConnection.java:310)
	at org.apache.tomcat.jdbc.pool.PooledConnection.connect(PooledConnection.java:203)
	at org.apache.tomcat.jdbc.pool.ConnectionPool.createConnection(ConnectionPool.java:735)
	at org.apache.tomcat.jdbc.pool.ConnectionPool.borrowConnection(ConnectionPool.java:667)
	at org.apache.tomcat.jdbc.pool.ConnectionPool.init(ConnectionPool.java:482)
	at org.apache.tomcat.jdbc.pool.ConnectionPool.<init>(ConnectionPool.java:154)
	at org.apache.tomcat.jdbc.pool.DataSourceProxy.pCreatePool(DataSourceProxy.java:118)
	at org.apache.tomcat.jdbc.pool.DataSourceProxy.createPool(DataSourceProxy.java:107)
	at org.apache.tomcat.jdbc.pool.DataSourceProxy.getConnection(DataSourceProxy.java:131)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:77)
	... 90 common frames omitted
2019-01-03 18:48:39,071 90831 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/{mosoMemberId}/{freezeReason}/{duration}/{countryCode}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.RetrieveFreezeReasonOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.retrieveFreezeReason(java.lang.String,java.lang.String,int,int) throws java.lang.Exception
2019-01-03 18:48:39,072 90832 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2019-01-03 18:48:39,073 90833 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-reason/],methods=[POST]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.FreezeExtensionOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.freezeExtension(com.equinoxfitness.freezeservice.contract.FreezeExtensionInput) throws java.lang.Exception
2019-01-03 18:48:39,074 90834 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2019-01-03 18:48:39,077 90837 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2019-01-03 18:48:39,077 90837 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2019-01-03 18:48:39,079 90839 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2019-01-03 18:48:39,083 90843 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2019-01-03 18:48:39,084 90844 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2019-01-03 18:48:39,837 91597 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:47:13 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 18:48:39,935 91695 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:48:39,935 91695 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:48:39,976 91736 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in exceptionControllerAdvice
2019-01-03 18:48:40,014 91774 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2019-01-03 18:48:42,501 94261 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:47:13 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10] and template loader path [classpath:/templates/]
2019-01-03 18:48:42,503 94263 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2019-01-03 18:48:42,566 94326 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2019-01-03 18:48:43,048 94808 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:48:43,048 94808 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:48:43,055 94815 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2019-01-03 18:48:43,055 94815 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2019-01-03 18:48:43,173 94933 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2019-01-03 18:48:43,187 94947 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2019-01-03 18:48:43,188 94948 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2019-01-03 18:48:43,190 94950 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2019-01-03 18:48:43,194 94954 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2019-01-03 18:48:43,212 94972 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2019-01-03 18:48:43,232 94992 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=13f17eb4,type=ConfigurationPropertiesRebinder]
2019-01-03 18:48:45,569 97329 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2019-01-03 18:48:45,591 97351 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2019-01-03 18:48:45,600 97360 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2019-01-03 18:48:45,778 97538 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2019-01-03 18:48:45,795 97555 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2019-01-03 18:48:45,798 97558 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2019-01-03 18:48:45,822 97582 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2019-01-03 18:48:45,828 97588 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 97.349 seconds (JVM running for 98.737)
2019-01-03 18:48:56,952 108712 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 18:48:56,952 108712 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2019-01-03 18:48:56,993 108753 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 40 ms
2019-01-03 18:48:57,056 108816 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 18:48:57,057 108817 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated d6d144a9-a3ba-4c46-8609-7c00addaec2e 
2019-01-03 18:48:57,063 108823 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 18:48:57,064 108824 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 18:48:57,069 108829 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 18:49:14,657 126417 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 18:49:14,660 126420 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 17596ms]
2019-01-03 18:49:14,661 126421 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 18:49:14,661 126421 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 18:49:16,582 128342 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 1921 ms
2019-01-03 18:49:16,585 128345 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 18:49:16,592 128352 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 18:49:16,593 128353 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 18:49:18,343 130103 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :545cbf88-9fdb-48de-84c2-e5ecfebcedf4
2019-01-03 18:49:18,344 130104 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C403E321B87D252601130289E8F360AABA374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200
2019-01-03 18:49:18,353 130113 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 18:49:18,768 130528 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Cannot update the start date when Suspension is active (10104)","Data":null}
2019-01-03 18:49:18,781 130541 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - Success 
2019-01-03 18:49:18,782 130542 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - null
2019-01-03 18:49:19,358 131118 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - ID 850
2019-01-03 18:49:19,358 131118 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - Item price 30.0
2019-01-03 18:49:19,376 131136 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] DEBUG c.e.f.s.impl.FreezeServiceImpl - API Request {
  "Payments" : [ {
    "TenderTypeId" : 108,
    "Amount" : 0.0,
    "AdditionalInfo" : null,
    "CreditCardToken" : null,
    "ClientAccountPaySourceId" : null,
    "ClientAccountId" : 783401,
    "GiftCardNumber" : null,
    "CreditCardAuthResult" : null
  } ],
  "BusinessUnitCode" : "252",
  "TargetDate" : "2019-01-03",
  "MemberId" : "**********",
  "AccountId" : 783401,
  "Items" : [ {
    "IsItem" : true,
    "ItemCode" : "1343",
    "Quantity" : 0,
    "DiscountCodes" : null,
    "Price" : 30.0,
    "Comments" : null,
    "SalesPersonId" : null,
    "GiftCard" : null,
    "LockerDetails" : null
  } ],
  "CurrencyCode" : null,
  "Comments" : null,
  "TaxExemptId" : null,
  "EmployeeAuthorizationPin" : null
}
2019-01-03 18:49:19,********** [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [d6d144a9-a3ba-4c46-8609-7c00addaec2e] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Item 1343 is not marked as available for POS. It can only be used in a recurring setup, not directly on a transaction. (10039)","Data":null}
2019-01-03 18:49:55,********** [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] INFO  c.e.c.aspect.CorrelationAspect - Found Amazon tarce id Header null 
2019-01-03 18:49:55,********** [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] INFO  c.e.c.aspect.CorrelationAspect - No correlationId found in Header. Generated 1b857ffe-c62e-400e-8e44-5e1734c636af 
2019-01-03 18:49:55,********** [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - Inside freezeExtension Impl
2019-01-03 18:49:55,********** [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - Calling DAO 
2019-01-03 18:49:55,********** [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2019-01-03 18:49:56,820 168580 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2019-01-03 18:49:56,820 168580 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] INFO  c.e.h.p.dao.FreezeServiceDAOImpl - [MemberAgreementDetail com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(String) - 1638ms]
2019-01-03 18:49:56,820 168580 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - FreezeId 194183
2019-01-03 18:49:56,820 168580 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById V2 for 252
2019-01-03 18:49:58,796 170556 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.c.s.impl.FacilityServiceImpl - FacilityServiceImpl.getFacilityById took 1976 ms
2019-01-03 18:49:58,797 170557 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Invoking getSession
2019-01-03 18:49:58,797 170557 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - getting sesssion for 252-Web_Endpoint
2019-01-03 18:49:58,797 170557 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.u.MosoSessionMediatorForFreeze - Correlation-ID : 
2019-01-03 18:50:00,437 172197 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - AuthToken :84a40583-5674-48aa-8f20-71f4b3bf7502
2019-01-03 18:50:00,437 172197 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - Cookie :AWSELB=2BD7D52B1CF03A05F450BEC4CF9A99AB5088F64C409196A5B99EE48F2E625073EC7DE12C3374CB773A0643F8B3E02EFE1AB37F7B8B30925DDBBDDC53747E4FC917E232B653;PATH=/;MAX-AGE=43200
2019-01-03 18:50:00,438 172198 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - API Request {
  "FreezeId" : "194183",
  "FreezeStartDate" : "2019-06-21",
  "FreezeEndDate" : "2019-08-03",
  "Comments" : "Test",
  "ExceptionReasonId" : 0,
  "ExceptionValue" : null,
  "ExceptionComment" : null,
  "FreezeEndDateSet" : false
}
2019-01-03 18:50:01,242 173002 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Cannot update the start date when Suspension is active (10104)","Data":null}
2019-01-03 18:50:01,242 173002 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - Success 
2019-01-03 18:50:01,242 173002 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - null
2019-01-03 18:50:01,847 173607 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeMosoServiceImpl - ID 850
2019-01-03 18:50:01,847 173607 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - Item price 30.0
2019-01-03 18:50:01,852 173612 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] DEBUG c.e.f.s.impl.FreezeServiceImpl - API Request {
  "Payments" : [ {
    "TenderTypeId" : 108,
    "Amount" : 30.0,
    "AdditionalInfo" : null,
    "CreditCardToken" : null,
    "ClientAccountPaySourceId" : null,
    "ClientAccountId" : 783401,
    "GiftCardNumber" : null,
    "CreditCardAuthResult" : null
  } ],
  "BusinessUnitCode" : "252",
  "TargetDate" : "2019-01-03",
  "MemberId" : "**********",
  "AccountId" : 783401,
  "Items" : [ {
    "IsItem" : true,
    "ItemCode" : "1343",
    "Quantity" : 1,
    "DiscountCodes" : null,
    "Price" : 30.0,
    "Comments" : null,
    "SalesPersonId" : null,
    "GiftCard" : null,
    "LockerDetails" : null
  } ],
  "CurrencyCode" : null,
  "Comments" : null,
  "TaxExemptId" : null,
  "EmployeeAuthorizationPin" : null
}
2019-01-03 18:50:02,********** [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [1b857ffe-c62e-400e-8e44-5e1734c636af] ERROR c.e.f.s.impl.FreezeMosoServiceImpl - Error {"Message":"Item 1343 is not marked as available for POS. It can only be used in a recurring setup, not directly on a transaction. (10039)","Data":null}
2019-01-03 21:47:05,199 ******** [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2019-01-03 21:47:05,201 ******** [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@13f17eb4: startup date [Thu Jan 03 18:47:13 IST 2019]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10
2019-01-03 21:47:05,223 10796983 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2019-01-03 21:47:05,223 10796983 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2019-01-03 21:47:05,235 10796995 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2019-01-03 21:47:06,161 10797921 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2019-01-03 21:47:06,168 10797928 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2019-01-03 21:47:06,180 10797940 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2019-01-03 21:47:06,189 10797949 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-12*******] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
