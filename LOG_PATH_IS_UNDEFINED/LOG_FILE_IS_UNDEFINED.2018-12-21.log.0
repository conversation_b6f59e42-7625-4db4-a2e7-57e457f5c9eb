2018-12-21 12:03:35,500 1850 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@3dddefd8: startup date [Fri Dec 21 12:03:35 IST 2018]; root of context hierarchy
2018-12-21 12:03:35,991 2341 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:03:36,289 2639 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:03:36,516 2866 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$78ce2c1a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 12:05:18,060 2936 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@3dddefd8: startup date [Fri Dec 21 12:05:18 IST 2018]; root of context hierarchy
2018-12-21 12:05:18,500 3376 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:05:18,858 3734 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:05:18,963 3839 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c5d05a52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 12:12:36,622 2940 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@3dddefd8: startup date [Fri Dec 21 12:12:36 IST 2018]; root of context hierarchy
2018-12-21 12:12:37,057 3375 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:12:37,382 3700 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:12:37,465 3783 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c5d05a52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 12:15:51,181 2931 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@3dddefd8: startup date [Fri Dec 21 12:15:51 IST 2018]; root of context hierarchy
2018-12-21 12:15:51,607 3357 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:15:51,883 3633 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:15:51,989 3739 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c5d05a52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 12:17:52,817 2889 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@3dddefd8: startup date [Fri Dec 21 12:17:52 IST 2018]; root of context hierarchy
2018-12-21 12:17:53,301 3373 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:17:53,618 3690 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:17:53,737 3809 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c5d05a52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 12:23:51,931 1824 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@2b95e48b: startup date [Fri Dec 21 12:23:51 IST 2018]; root of context hierarchy
2018-12-21 12:23:52,372 2265 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:23:52,631 2524 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:23:52,770 2663 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ee51b18d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 12:51:21,794 2153 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@2b95e48b: startup date [Fri Dec 21 12:51:21 IST 2018]; root of context hierarchy
2018-12-21 12:51:22,370 2729 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:51:22,660 3019 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:51:22,764 3123 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ee51b18d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 12:56:03,964 2279 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@2b95e48b: startup date [Fri Dec 21 12:56:03 IST 2018]; root of context hierarchy
2018-12-21 12:56:04,452 2767 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:56:04,722 3037 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:56:04,814 3129 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ee51b18d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 12:57:59,123 2103 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@2b95e48b: startup date [Fri Dec 21 12:57:59 IST 2018]; root of context hierarchy
2018-12-21 12:57:59,591 2571 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 12:57:59,872 2852 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 12:57:59,977 2957 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ee51b18d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 13:01:19,741 1954 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@2b95e48b: startup date [Fri Dec 21 13:01:19 IST 2018]; root of context hierarchy
2018-12-21 13:01:20,172 2385 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 13:01:20,455 2668 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 13:01:20,537 2750 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ee51b18d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 13:03:27,053 2153 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@2b95e48b: startup date [Fri Dec 21 13:03:27 IST 2018]; root of context hierarchy
2018-12-21 13:03:27,621 2721 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 13:03:27,924 3024 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 13:03:28,025 3125 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ee51b18d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-21 13:06:19,735 1905 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@2b95e48b: startup date [Fri Dec 21 13:06:19 IST 2018]; root of context hierarchy
2018-12-21 13:06:20,206 2376 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2018-12-21 13:06:20,493 2663 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-21 13:06:20,588 2758 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ee51b18d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
