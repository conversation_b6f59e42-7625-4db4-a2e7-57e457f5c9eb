2018-12-20 12:48:02,251 4551 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 12:48:03,461 5761 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 12:48:03,463 5763 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 12:48:03,481 5781 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:48:03 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:48:04,281 6581 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 12:48:04,604 6904 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=ed73bd79-81a0-302f-9a50-384dc365f077
2018-12-20 12:48:04,628 6928 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:48:04,738 7038 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$937da83] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:48:04,790 7090 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 12:48:04,852 7152 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$64a01584] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:48:04,903 7203 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:48:05,258 7558 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 12:48:05,272 7572 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 12:48:05,273 7573 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 12:48:05,520 7820 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 12:48:05,520 7820 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2039 ms
2018-12-20 12:48:05,756 8056 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 12:48:05,758 8058 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 12:48:05,762 8062 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 12:48:05,762 8062 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 12:48:05,763 8063 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 12:48:05,763 8063 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 12:48:05,830 8130 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'restResponse': Unsatisfied dependency expressed through field 'eventsService'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.equinoxfitness.events.service.EventsService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2018-12-20 12:48:05,831 8131 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 12:48:05,864 8164 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.a.l.AutoConfigurationReportLoggingInitializer - 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
2018-12-20 12:48:06,029 8329 [freeze] [A2ML10676] [8011] [main] [] [] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field eventsService in com.equinoxfitness.commons.utils.RestResponse required a bean of type 'com.equinoxfitness.events.service.EventsService' that could not be found.


Action:

Consider defining a bean of type 'com.equinoxfitness.events.service.EventsService' in your configuration.

2018-12-20 12:49:11,861 3913 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 12:49:13,050 5102 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 12:49:13,053 5105 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 12:49:13,068 5120 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:49:13 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:49:14,357 6409 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 12:49:14,643 6695 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=db7bec69-51eb-39c2-856c-b86270fd7a41
2018-12-20 12:49:14,665 6717 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:49:14,768 6820 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$937da83] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:49:14,833 6885 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 12:49:14,882 6934 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$64a01584] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:49:14,932 6984 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:49:15,271 7323 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 12:49:15,284 7336 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 12:49:15,285 7337 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 12:49:15,523 7575 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 12:49:15,524 7576 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2455 ms
2018-12-20 12:49:15,746 7798 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 12:49:15,748 7800 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 12:49:15,752 7804 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 12:49:15,752 7804 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 12:49:15,753 7805 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 12:49:15,753 7805 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 12:49:16,306 8358 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 12:49:16,388 8440 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 12:49:45,435 37487 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 12:49:45,437 37489 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 12:49:45,440 37492 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 12:49:45,440 37492 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 12:49:45,441 37493 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 12:49:45,445 37497 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 12:49:45,445 37497 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 12:49:45,859 37911 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:49:13 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:49:45,934 37986 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:49:45,934 37986 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:49:45,994 38046 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:49:48,255 40307 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:49:13 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc] and template loader path [classpath:/templates/]
2018-12-20 12:49:48,257 40309 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 12:49:48,287 40339 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 12:49:48,536 40588 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 12:49:48,536 40588 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 12:49:48,543 40595 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 12:49:48,543 40595 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 12:49:48,624 40676 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 12:49:48,635 40687 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 12:49:48,636 40688 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 12:49:48,639 40691 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 12:49:48,643 40695 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 12:49:48,662 40714 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 12:49:48,673 40725 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=7c098bb3,type=ConfigurationPropertiesRebinder]
2018-12-20 12:49:49,900 41952 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 12:49:49,920 41972 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 12:49:49,931 41983 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 12:49:50,075 42127 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 12:49:50,089 42141 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 12:49:50,117 42169 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 12:49:50,147 42199 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 12:49:50,153 42205 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 41.989 seconds (JVM running for 43.185)
2018-12-20 12:50:19,125 71177 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 12:50:19,126 71178 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 12:50:19,174 71226 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 47 ms
2018-12-20 12:50:32,418 84470 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 12:50:32,437 84489 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.springframework.context.NoSuchMessageException: No message found under code 'FREEZE_REASON_BLANK' for locale 'en_US'.] with root cause
org.springframework.context.NoSuchMessageException: No message found under code 'FREEZE_REASON_BLANK' for locale 'en_US'.
	at org.springframework.context.support.DelegatingMessageSource.getMessage(DelegatingMessageSource.java:69)
	at org.springframework.context.support.DelegatingMessageSource.getMessage(DelegatingMessageSource.java:66)
	at com.equinoxfitness.commons.utils.RestResponse.getMessage(RestResponse.java:178)
	at com.equinoxfitness.commons.utils.RestResponse.createExceptionMessage(RestResponse.java:146)
	at com.equinoxfitness.commons.utils.RestResponse.createErrorMessage(RestResponse.java:121)
	at com.equinoxfitness.commons.utils.RestResponse.populateFailureResponse(RestResponse.java:57)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(FreezeServiceController.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2018-12-20 12:54:21,557 313609 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 12:54:21,558 313610 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:49:13 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:54:21,566 313618 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 12:54:21,566 313618 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 12:54:21,573 313625 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 12:54:22,409 314461 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 12:54:22,415 314467 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 12:54:22,422 314474 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 12:54:22,431 314483 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 12:54:44,793 13623 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 12:54:45,944 14774 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 12:54:45,946 14776 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 12:54:45,961 14791 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@31e4bb20: startup date [Thu Dec 20 12:54:45 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:54:47,002 15832 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 12:54:47,241 16071 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=36e741da-2561-3f89-8f85-40b9722fe4dc
2018-12-20 12:54:47,262 16092 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:54:47,373 16203 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$3c678352] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:54:47,426 16256 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 12:54:47,469 16299 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$97cfbe53] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:54:47,517 16347 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b3e9c150] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:54:47,859 16689 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 12:54:47,871 16701 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 12:54:47,872 16702 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 12:54:48,104 16934 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 12:54:48,105 16935 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2145 ms
2018-12-20 12:54:48,298 17128 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 12:54:48,299 17129 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 12:54:48,303 17133 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 12:54:48,303 17133 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 12:54:48,303 17133 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 12:54:48,303 17133 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 12:54:48,784 17614 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 12:54:48,840 17670 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 12:55:19,286 48116 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 12:55:19,288 48118 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 12:55:19,291 48121 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 12:55:19,292 48122 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 12:55:19,293 48123 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 12:55:19,296 48126 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 12:55:19,297 48127 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 12:55:19,693 48523 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@31e4bb20: startup date [Thu Dec 20 12:54:45 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:55:19,756 48586 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:55:19,756 48586 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:55:19,811 48641 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:55:22,000 50830 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@31e4bb20: startup date [Thu Dec 20 12:54:45 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc] and template loader path [classpath:/templates/]
2018-12-20 12:55:22,002 50832 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 12:55:22,029 50859 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 12:55:22,289 51119 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 12:55:22,290 51120 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 12:55:22,295 51125 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 12:55:22,295 51125 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 12:55:22,378 51208 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 12:55:22,388 51218 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 12:55:22,390 51220 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 12:55:22,392 51222 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 12:55:22,395 51225 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 12:55:22,408 51238 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 12:55:22,422 51252 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=31e4bb20,type=ConfigurationPropertiesRebinder]
2018-12-20 12:55:23,697 52527 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 12:55:23,715 52545 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 12:55:23,723 52553 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 12:55:23,833 52663 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 12:55:23,844 52674 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 12:55:23,860 52690 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 12:55:23,883 52713 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 12:55:23,888 52718 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 52.523 seconds (JVM running for 53.659)
2018-12-20 12:55:31,410 60240 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 12:55:31,411 60241 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 12:55:31,436 60266 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 25 ms
2018-12-20 12:55:31,498 60328 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 12:56:57,354 146184 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 12:56:57,355 146185 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@31e4bb20: startup date [Thu Dec 20 12:54:45 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:56:57,361 146191 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 12:56:57,362 146192 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 12:56:57,369 146199 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 12:56:58,243 147073 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 12:56:58,246 147076 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 12:56:58,254 147084 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 12:56:58,262 147092 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 12:57:06,937 3457 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 12:57:08,077 4597 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 12:57:08,083 4603 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 12:57:08,114 4634 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:57:08 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:57:09,084 5604 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 12:57:09,299 5819 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=36e741da-2561-3f89-8f85-40b9722fe4dc
2018-12-20 12:57:09,320 5840 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 12:57:09,409 5929 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$937da83] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:57:09,450 5970 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 12:57:09,488 6008 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$64a01584] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:57:09,531 6051 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80ba1881] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 12:57:09,873 6393 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 12:57:09,884 6404 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 12:57:09,885 6405 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 12:57:10,095 6615 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 12:57:10,095 6615 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1981 ms
2018-12-20 12:57:10,282 6802 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 12:57:10,284 6804 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 12:57:10,288 6808 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 12:57:10,289 6809 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 12:57:10,289 6809 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 12:57:10,289 6809 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 12:57:10,824 7344 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 12:57:10,886 7406 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 12:57:39,596 36116 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 12:57:39,598 36118 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 12:57:39,601 36121 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 12:57:39,602 36122 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 12:57:39,603 36123 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 12:57:39,606 36126 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 12:57:39,607 36127 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 12:57:39,990 36510 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:57:08 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 12:57:40,053 36573 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:57:40,053 36573 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:57:40,101 36621 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 12:57:42,223 38743 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:57:08 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc] and template loader path [classpath:/templates/]
2018-12-20 12:57:42,225 38745 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 12:57:42,253 38773 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 12:57:42,515 39035 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 12:57:42,517 39037 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 12:57:42,524 39044 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 12:57:42,524 39044 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 12:57:42,611 39131 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 12:57:42,621 39141 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 12:57:42,623 39143 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 12:57:42,626 39146 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 12:57:42,629 39149 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 12:57:42,642 39162 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 12:57:42,654 39174 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=7c098bb3,type=ConfigurationPropertiesRebinder]
2018-12-20 12:57:43,833 40353 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 12:57:43,852 40372 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 12:57:43,860 40380 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 12:57:44,021 40541 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 12:57:44,041 40561 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 12:57:44,058 40578 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 12:57:44,084 40604 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 12:57:44,090 40610 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 40.438 seconds (JVM running for 41.61)
2018-12-20 12:57:47,910 44430 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 12:57:47,911 44431 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 12:57:47,935 44455 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 24 ms
2018-12-20 12:57:47,994 44514 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 14:26:09,578 5346098 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 14:26:09,581 5346101 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@7c098bb3: startup date [Thu Dec 20 12:57:08 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@24313fcc
2018-12-20 14:26:09,647 5346167 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 14:26:09,650 5346170 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 14:26:09,673 5346193 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 14:26:10,597 5347117 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 14:26:10,606 5347126 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 14:26:10,620 5347140 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 14:26:10,630 5347150 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 14:26:21,776 3598 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 14:26:22,949 4771 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 14:26:22,952 4774 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 14:26:22,971 4793 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:26:22 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 14:26:23,926 5748 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 14:26:24,157 5979 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=012eb245-7d05-3a9e-8345-47dec46a9786
2018-12-20 14:26:24,176 5998 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 14:26:24,261 6083 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:26:24,336 6158 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 14:26:24,373 6195 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:26:24,431 6253 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:26:24,820 6642 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 14:26:24,832 6654 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 14:26:24,833 6655 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 14:26:25,080 6902 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 14:26:25,081 6903 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2110 ms
2018-12-20 14:26:25,263 7085 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 14:26:25,265 7087 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 14:26:25,269 7091 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 14:26:25,270 7092 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 14:26:25,270 7092 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 14:26:25,270 7092 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 14:26:25,338 7160 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'freezeServiceController': Unsatisfied dependency expressed through field 'freezeService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'freezeServiceImpl': Unsatisfied dependency expressed through field 'freezeServiceDAO'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'freezeServiceDAOImpl': Unsatisfied dependency expressed through field 'tenantJDBCTemplate'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.jdbc.core.JdbcTemplate' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true), @org.springframework.beans.factory.annotation.Qualifier(value=tenantJDBCTemplate)}
2018-12-20 14:26:25,340 7162 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 14:26:25,368 7190 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.a.l.AutoConfigurationReportLoggingInitializer - 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
2018-12-20 14:26:25,497 7319 [freeze] [A2ML10676] [8011] [main] [] [] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field tenantJDBCTemplate in com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl required a bean of type 'org.springframework.jdbc.core.JdbcTemplate' that could not be found.
	- Bean method 'jdbcTemplate' in 'JdbcTemplateAutoConfiguration' not loaded because @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) found beans 'esbAppJdbcTemplate', 'paperTrailJdbcTemplate'


Action:

Consider revisiting the conditions above or defining a bean of type 'org.springframework.jdbc.core.JdbcTemplate' in your configuration.

2018-12-20 14:31:35,616 3575 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 14:31:36,793 4752 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 14:31:36,798 4757 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 14:31:36,834 4793 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:31:36 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 14:31:37,966 5925 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 14:31:38,220 6179 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=5fed5901-0e7c-32ad-a549-c006db2f205c
2018-12-20 14:31:38,240 6199 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 14:31:38,327 6286 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:31:38,371 6330 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 14:31:38,408 6367 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:31:38,453 6412 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:31:38,794 6753 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 14:31:38,806 6765 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 14:31:38,807 6766 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 14:31:39,019 6978 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 14:31:39,019 6978 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2185 ms
2018-12-20 14:31:39,224 7183 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 14:31:39,227 7186 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 14:31:39,234 7193 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 14:31:39,235 7194 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 14:31:39,235 7194 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 14:31:39,235 7194 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 14:31:39,850 7809 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 14:31:39,928 7887 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 14:32:21,564 49523 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 14:32:21,566 49525 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 14:32:21,568 49527 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 14:32:21,569 49528 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 14:32:21,570 49529 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 14:32:21,573 49532 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 14:32:21,574 49533 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 14:32:21,993 49952 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:31:36 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 14:32:22,071 50030 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 14:32:22,072 50031 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 14:32:22,130 50089 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 14:32:24,469 52428 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:31:36 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 14:32:24,471 52430 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 14:32:24,507 52466 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 14:32:24,843 52802 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 14:32:24,843 52802 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 14:32:24,848 52807 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 14:32:24,849 52808 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 14:32:24,947 52906 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 14:32:24,960 52919 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 14:32:24,961 52920 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 14:32:24,964 52923 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 14:32:24,968 52927 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 14:32:24,985 52944 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 14:32:24,998 52957 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 14:32:26,723 54682 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 14:32:26,748 54707 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 14:32:26,760 54719 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 14:32:26,917 54876 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 14:32:26,929 54888 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 14:32:26,954 54913 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 14:32:26,982 54941 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 14:32:26,988 54947 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 54.748 seconds (JVM running for 55.912)
2018-12-20 14:32:35,337 63296 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 14:32:35,338 63297 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 14:32:35,366 63325 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 28 ms
2018-12-20 14:32:35,424 63383 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 14:32:35,426 63385 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 14:32:35,431 63390 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 14:32:36,311 64270 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] ERROR c.e.f.dao.impl.FreezeServiceDAOImpl - Error occured while obtaining member agreement detail java.util.ArrayList cannot be cast to com.equinoxfitness.freezeservice.dvo.MemberAgreementDetail
2018-12-20 14:32:49,969 77928 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 14:32:49,970 77929 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:31:36 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 14:32:49,978 77937 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 14:32:49,979 77938 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 14:32:49,988 77947 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 14:32:50,849 78808 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 14:32:50,854 78813 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 14:32:50,861 78820 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 14:32:50,892 78851 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 14:34:42,813 3504 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 14:34:43,963 4654 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 14:34:43,966 4657 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 14:34:43,986 4677 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:34:43 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 14:34:45,021 5712 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 14:34:45,250 5941 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=5fed5901-0e7c-32ad-a549-c006db2f205c
2018-12-20 14:34:45,269 5960 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 14:34:45,352 6043 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:34:45,396 6087 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 14:34:45,435 6126 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:34:45,475 6166 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 14:34:45,802 6493 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 14:34:45,813 6504 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 14:34:45,814 6505 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 14:34:46,039 6730 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 14:34:46,039 6730 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2053 ms
2018-12-20 14:34:46,255 6946 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 14:34:46,256 6947 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 14:34:46,260 6951 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 14:34:46,260 6951 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 14:34:46,261 6952 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 14:34:46,261 6952 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 14:34:46,886 7577 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 14:34:46,942 7633 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 14:35:27,953 48644 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 14:35:27,955 48646 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 14:35:27,958 48649 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 14:35:27,959 48650 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 14:35:27,960 48651 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 14:35:27,963 48654 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 14:35:27,964 48655 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 14:35:28,534 49225 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:34:43 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 14:35:28,646 49337 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 14:35:28,646 49337 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 14:35:28,698 49389 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 14:35:30,828 51519 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:34:43 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 14:35:30,830 51521 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 14:35:30,857 51548 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 14:35:31,102 51793 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 14:35:31,103 51794 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 14:35:31,108 51799 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 14:35:31,108 51799 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 14:35:31,190 51881 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 14:35:31,201 51892 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 14:35:31,202 51893 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 14:35:31,204 51895 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 14:35:31,207 51898 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 14:35:31,223 51914 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 14:35:31,234 51925 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 14:35:32,650 53341 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 14:35:32,668 53359 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 14:35:32,676 53367 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 14:35:32,782 53473 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 14:35:32,793 53484 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 14:35:32,808 53499 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 14:35:32,829 53520 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 14:35:32,834 53525 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 53.35 seconds (JVM running for 54.469)
2018-12-20 14:35:37,101 57792 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 14:35:37,102 57793 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 14:35:37,131 57822 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 29 ms
2018-12-20 14:35:37,190 57881 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 14:35:37,193 57884 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 14:35:37,198 57889 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 14:35:38,052 58743 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] ERROR c.e.f.dao.impl.FreezeServiceDAOImpl - Error occured while obtaining member agreement detail Index: 0, Size: 0
2018-12-20 14:35:38,069 58760 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IndexOutOfBoundsException: Index: 0, Size: 0] with root cause
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653)
	at java.util.ArrayList.get(ArrayList.java:429)
	at com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(FreezeServiceDAOImpl.java:54)
	at com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl$$FastClassBySpringCGLIB$$64d44286.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:136)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl$$EnhancerBySpringCGLIB$$c503d33d.getMemberAgreementDetail(<generated>)
	at com.equinoxfitness.freezeservice.controller.service.impl.FreezeServiceImpl.checkFreezeEligibility(FreezeServiceImpl.java:41)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(FreezeServiceController.java:69)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2018-12-20 14:36:37,584 118275 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 14:36:37,584 118275 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 14:36:37,584 118275 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 14:36:38,347 119038 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 16:43:15,680 7716371 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 16:43:15,704 7716395 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 14:34:43 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 16:43:15,903 7716594 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 16:43:15,906 7716597 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 16:43:15,949 7716640 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 16:43:17,532 7718223 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 16:43:17,547 7718238 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 16:43:17,573 7718264 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 16:43:17,588 7718279 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 16:43:28,072 3681 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 16:43:29,246 4855 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 16:43:29,247 4856 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 16:43:29,259 4868 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@62f68dff: startup date [Thu Dec 20 16:43:29 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 16:43:30,290 5899 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 16:43:30,532 6141 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 16:43:30,559 6168 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 16:43:30,677 6286 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$1178e170] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 16:43:30,751 6360 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 16:43:30,802 6411 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6ce11c71] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 16:43:30,863 6472 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$88fb1f6e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 16:43:31,346 6955 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 16:43:31,360 6969 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 16:43:31,361 6970 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 16:43:31,626 7235 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 16:43:31,626 7235 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2367 ms
2018-12-20 16:43:31,899 7508 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 16:43:31,901 7510 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 16:43:31,906 7515 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 16:43:31,907 7516 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 16:43:31,907 7516 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 16:43:31,907 7516 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 16:43:32,713 8322 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 16:43:32,833 8442 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 16:44:15,665 51274 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 16:44:15,667 51276 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 16:44:15,670 51279 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 16:44:15,671 51280 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 16:44:15,672 51281 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 16:44:15,675 51284 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 16:44:15,676 51285 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 16:44:16,085 51694 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@62f68dff: startup date [Thu Dec 20 16:43:29 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 16:44:16,157 51766 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 16:44:16,157 51766 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 16:44:16,209 51818 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 16:44:18,492 54101 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@62f68dff: startup date [Thu Dec 20 16:43:29 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 16:44:18,494 54103 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 16:44:18,521 54130 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 16:44:18,773 54382 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 16:44:18,773 54382 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 16:44:18,780 54389 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 16:44:18,780 54389 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 16:44:18,857 54466 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 16:44:18,868 54477 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 16:44:18,868 54477 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 16:44:18,870 54479 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 16:44:18,873 54482 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 16:44:18,892 54501 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 16:44:18,904 54513 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=62f68dff,type=ConfigurationPropertiesRebinder]
2018-12-20 16:44:20,369 55978 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 16:44:20,393 56002 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 16:44:20,402 56011 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 16:44:20,533 56142 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 16:44:20,552 56161 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 16:44:20,556 56165 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 16:44:20,585 56194 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 16:44:20,592 56201 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 55.975 seconds (JVM running for 57.513)
2018-12-20 16:45:09,391 105000 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 16:45:09,392 105001 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 16:45:09,418 105027 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 26 ms
2018-12-20 16:45:09,495 105104 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 16:45:09,499 105108 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 16:45:09,507 105116 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 16:45:10,649 106258 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 16:45:10,651 106260 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 17:31:41,480 2897089 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 17:31:41,514 2897123 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@62f68dff: startup date [Thu Dec 20 16:43:29 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:31:41,857 2897466 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 17:31:41,864 2897473 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 17:31:41,988 2897597 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:31:43,944 2899553 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 17:31:44,615 2900224 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:31:44,646 2900255 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 17:31:44,658 2900267 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 17:31:54,160 3635 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 17:31:55,370 4845 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 17:31:55,378 4853 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 17:31:55,408 4883 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:31:55 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:31:56,520 5995 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 17:31:56,738 6213 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 17:31:56,757 6232 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:31:56,838 6313 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:31:56,915 6390 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 17:31:56,950 6425 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:31:56,987 6462 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:31:57,432 6907 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 17:31:57,443 6918 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 17:31:57,444 6919 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 17:31:57,674 7149 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 17:31:57,675 7150 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2266 ms
2018-12-20 17:31:57,874 7349 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 17:31:57,876 7351 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 17:31:57,880 7355 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 17:31:57,882 7357 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 17:31:57,882 7357 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 17:31:57,883 7358 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 17:31:58,561 8036 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 17:31:58,639 8114 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 17:32:39,305 48780 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 17:32:39,307 48782 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 17:32:39,311 48786 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 17:32:39,312 48787 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 17:32:39,314 48789 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 17:32:39,317 48792 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 17:32:39,318 48793 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 17:32:39,820 49295 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:31:55 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:32:39,899 49374 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:32:39,899 49374 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:32:39,965 49440 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:32:42,382 51857 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:31:55 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 17:32:42,385 51860 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 17:32:42,415 51890 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 17:32:42,656 52131 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:32:42,656 52131 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:32:42,662 52137 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:32:42,662 52137 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:32:42,740 52215 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 17:32:42,752 52227 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 17:32:42,753 52228 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 17:32:42,755 52230 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 17:32:42,758 52233 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 17:32:42,776 52251 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 17:32:42,790 52265 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 17:32:44,382 53857 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 17:32:44,406 53881 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 17:32:44,416 53891 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 17:32:44,550 54025 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:32:44,562 54037 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 17:32:44,592 54067 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 17:32:44,616 54091 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 17:32:44,621 54096 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 53.867 seconds (JVM running for 55.378)
2018-12-20 17:33:26,232 95707 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:33:26,233 95708 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 17:33:26,257 95732 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 24 ms
2018-12-20 17:33:26,316 95791 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 17:33:26,319 95794 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 17:33:26,324 95799 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 17:33:27,170 96645 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 17:33:27,202 96677 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException] with root cause
java.lang.NullPointerException: null
	at com.equinoxfitness.freezeservice.utils.FreezeEligibilityRule.eligibilityRuleCheck(FreezeEligibilityRule.java:38)
	at com.equinoxfitness.freezeservice.controller.service.impl.FreezeServiceImpl.checkFreezeEligibility(FreezeServiceImpl.java:62)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(FreezeServiceController.java:75)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2018-12-20 17:34:07,568 137043 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 17:34:07,569 137044 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:31:55 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:34:07,576 137051 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 17:34:07,577 137052 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 17:34:07,584 137059 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:34:08,504 137979 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 17:34:08,514 137989 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:34:08,531 138006 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 17:34:08,581 138056 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 17:34:17,844 3486 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 17:34:18,997 4639 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 17:34:19,000 4642 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 17:34:19,022 4664 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:34:19 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:34:20,022 5664 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 17:34:20,252 5894 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 17:34:20,272 5914 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:34:20,363 6005 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:34:20,409 6051 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 17:34:20,444 6086 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:34:20,487 6129 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:34:20,821 6463 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 17:34:20,831 6473 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 17:34:20,832 6474 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 17:34:21,040 6682 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 17:34:21,041 6683 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2020 ms
2018-12-20 17:34:21,233 6875 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 17:34:21,235 6877 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 17:34:21,240 6882 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 17:34:21,241 6883 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 17:34:21,241 6883 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 17:34:21,241 6883 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 17:34:21,859 7501 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 17:34:21,914 7556 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 17:35:02,300 47942 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 17:35:02,302 47944 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 17:35:02,305 47947 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 17:35:02,305 47947 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 17:35:02,306 47948 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 17:35:02,309 47951 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 17:35:02,310 47952 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 17:35:02,700 48342 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:34:19 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:35:02,761 48403 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:35:02,761 48403 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:35:02,805 48447 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:35:04,920 50562 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:34:19 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 17:35:04,922 50564 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 17:35:04,949 50591 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 17:35:05,205 50847 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:35:05,205 50847 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:35:05,211 50853 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:35:05,212 50854 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:35:05,289 50931 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 17:35:05,299 50941 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 17:35:05,300 50942 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 17:35:05,302 50944 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 17:35:05,304 50946 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 17:35:05,319 50961 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 17:35:05,330 50972 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 17:35:06,838 52480 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 17:35:06,862 52504 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 17:35:06,875 52517 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 17:35:07,024 52666 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:35:07,041 52683 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 17:35:07,064 52706 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 17:35:07,096 52738 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 17:35:07,103 52745 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 52.574 seconds (JVM running for 53.634)
2018-12-20 17:35:22,752 68394 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:35:22,753 68395 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 17:35:22,784 68426 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 31 ms
2018-12-20 17:35:22,839 68481 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 17:35:22,842 68484 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 17:35:22,847 68489 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 17:35:23,684 69326 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 17:35:23,715 69357 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException] with root cause
java.lang.NullPointerException: null
	at com.equinoxfitness.freezeservice.utils.FreezeEligibilityRule.eligibilityRuleCheck(FreezeEligibilityRule.java:38)
	at com.equinoxfitness.freezeservice.controller.service.impl.FreezeServiceImpl.checkFreezeEligibility(FreezeServiceImpl.java:62)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(FreezeServiceController.java:75)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2018-12-20 17:35:53,438 99080 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 17:35:53,439 99081 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:34:19 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:35:53,447 99089 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 17:35:53,448 99090 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 17:35:53,459 99101 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:35:54,422 100064 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 17:35:54,425 100067 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:35:54,433 100075 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 17:35:54,467 100109 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 17:36:14,121 3599 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 17:36:15,270 4748 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 17:36:15,274 4752 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 17:36:15,301 4779 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:36:15 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:36:16,349 5827 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 17:36:16,592 6070 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 17:36:16,612 6090 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:36:16,700 6178 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:36:16,742 6220 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 17:36:16,778 6256 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:36:16,826 6304 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:36:17,196 6674 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 17:36:17,212 6690 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 17:36:17,213 6691 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 17:36:17,440 6918 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 17:36:17,440 6918 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2139 ms
2018-12-20 17:36:17,634 7112 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 17:36:17,636 7114 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 17:36:17,641 7119 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 17:36:17,641 7119 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 17:36:17,642 7120 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 17:36:17,642 7120 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 17:36:18,312 7790 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 17:36:18,374 7852 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 17:36:58,655 48133 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 17:36:58,658 48136 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 17:36:58,661 48139 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 17:36:58,662 48140 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 17:36:58,663 48141 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 17:36:58,666 48144 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 17:36:58,667 48145 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 17:36:59,062 48540 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:36:15 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:36:59,135 48613 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:36:59,135 48613 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:36:59,188 48666 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:37:01,284 50762 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:36:15 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 17:37:01,286 50764 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 17:37:01,314 50792 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 17:37:01,566 51044 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:37:01,566 51044 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:37:01,572 51050 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:37:01,572 51050 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:37:01,653 51131 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 17:37:01,663 51141 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 17:37:01,663 51141 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 17:37:01,665 51143 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 17:37:01,668 51146 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 17:37:01,685 51163 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 17:37:01,696 51174 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 17:37:03,146 52624 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 17:37:03,164 52642 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 17:37:03,172 52650 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 17:37:03,278 52756 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:37:03,288 52766 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 17:37:03,300 52778 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 17:37:03,327 52805 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 17:37:03,334 52812 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 52.637 seconds (JVM running for 53.869)
2018-12-20 17:37:09,796 59274 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:37:09,797 59275 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 17:37:09,823 59301 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 26 ms
2018-12-20 17:37:09,967 59445 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 17:37:09,972 59450 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 17:37:09,982 59460 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 17:37:10,834 60312 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 17:37:10,836 60314 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 17:37:10,837 60315 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 17:44:54,183 523661 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 17:44:54,185 523663 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:36:15 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:44:54,191 523669 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 17:44:54,192 523670 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 17:44:54,199 523677 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:44:55,049 524527 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 17:44:55,054 524532 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:44:55,072 524550 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 17:44:55,086 524564 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 17:45:04,078 3864 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 17:45:05,229 5015 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 17:45:05,234 5020 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 17:45:05,266 5052 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:45:05 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:45:06,259 6045 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 17:45:06,483 6269 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 17:45:06,502 6288 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:45:06,592 6378 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:45:06,633 6419 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 17:45:06,669 6455 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:45:06,710 6496 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:45:07,056 6842 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 17:45:07,067 6853 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 17:45:07,068 6854 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 17:45:07,276 7062 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 17:45:07,276 7062 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2010 ms
2018-12-20 17:45:07,468 7254 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 17:45:07,469 7255 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 17:45:07,473 7259 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 17:45:07,474 7260 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 17:45:07,474 7260 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 17:45:07,474 7260 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 17:45:08,099 7885 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 17:45:08,154 7940 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 17:45:49,044 48830 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 17:45:49,047 48833 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 17:45:49,052 48838 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 17:45:49,053 48839 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 17:45:49,054 48840 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 17:45:49,058 48844 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 17:45:49,058 48844 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 17:45:49,446 49232 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:45:05 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:45:49,518 49304 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:45:49,518 49304 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:45:49,580 49366 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:45:51,903 51689 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:45:05 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 17:45:51,904 51690 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 17:45:51,931 51717 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 17:45:52,175 51961 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:45:52,175 51961 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:45:52,181 51967 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:45:52,181 51967 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:45:52,255 52041 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 17:45:52,266 52052 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 17:45:52,266 52052 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 17:45:52,268 52054 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 17:45:52,270 52056 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 17:45:52,285 52071 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 17:45:52,298 52084 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 17:45:53,782 53568 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 17:45:53,798 53584 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 17:45:53,806 53592 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 17:45:53,916 53702 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:45:53,927 53713 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 17:45:53,941 53727 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 17:45:53,964 53750 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 17:45:53,970 53756 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 53.584 seconds (JVM running for 54.653)
2018-12-20 17:46:01,482 61268 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:46:01,482 61268 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 17:46:01,512 61298 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 30 ms
2018-12-20 17:46:01,570 61356 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 17:46:01,573 61359 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 17:46:01,579 61365 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 17:46:02,436 62222 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 17:46:02,438 62224 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 17:46:02,438 62224 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 17:47:23,326 143112 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 17:47:23,327 143113 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:45:05 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:47:23,333 143119 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 17:47:23,333 143119 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 17:47:23,341 143127 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:47:24,250 144036 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 17:47:24,254 144040 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:47:24,263 144049 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 17:47:24,269 144055 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 17:47:38,366 3632 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 17:47:39,520 4786 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 17:47:39,525 4791 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 17:47:39,551 4817 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:47:39 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:47:40,563 5829 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 17:47:40,776 6042 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 17:47:40,795 6061 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:47:40,878 6144 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:47:40,922 6188 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 17:47:40,960 6226 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:47:41,013 6279 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:47:41,363 6629 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 17:47:41,374 6640 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 17:47:41,375 6641 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 17:47:41,582 6848 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 17:47:41,582 6848 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2031 ms
2018-12-20 17:47:41,762 7028 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 17:47:41,764 7030 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 17:47:41,769 7035 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 17:47:41,769 7035 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 17:47:41,770 7036 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 17:47:41,770 7036 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 17:47:42,526 7792 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 17:47:42,590 7856 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 17:48:22,407 47673 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 17:48:22,410 47676 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 17:48:22,412 47678 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 17:48:22,413 47679 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 17:48:22,414 47680 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 17:48:22,417 47683 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 17:48:22,418 47684 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 17:48:22,860 48126 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:47:39 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:48:22,920 48186 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:48:22,920 48186 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:48:22,966 48232 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:48:25,175 50441 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:47:39 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 17:48:25,178 50444 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 17:48:25,213 50479 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 17:48:25,493 50759 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:48:25,493 50759 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:48:25,500 50766 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:48:25,500 50766 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:48:25,578 50844 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 17:48:25,589 50855 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 17:48:25,590 50856 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 17:48:25,592 50858 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 17:48:25,594 50860 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 17:48:25,608 50874 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 17:48:25,620 50886 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 17:48:27,193 52459 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 17:48:27,210 52476 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 17:48:27,218 52484 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 17:48:27,327 52593 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:48:27,338 52604 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 17:48:27,353 52619 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 17:48:27,375 52641 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 17:48:27,381 52647 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 52.475 seconds (JVM running for 53.548)
2018-12-20 17:49:13,070 98336 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:49:13,071 98337 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 17:49:13,116 98382 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 45 ms
2018-12-20 17:49:13,228 98494 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 17:49:13,233 98499 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 17:49:13,243 98509 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 17:49:14,143 99409 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 17:49:14,144 99410 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 17:49:14,145 99411 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 17:49:14,148 99414 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Regular Case
2018-12-20 17:49:14,148 99414 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - PREGNANCY Case
2018-12-20 17:49:14,149 99415 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - MEDICAL Case
2018-12-20 17:49:14,149 99415 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - default Case
2018-12-20 17:56:30,234 535500 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 17:56:30,235 535501 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:47:39 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:56:30,241 535507 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 17:56:30,242 535508 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 17:56:30,249 535515 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:56:31,097 536363 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 17:56:31,101 536367 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:56:31,109 536375 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 17:56:31,135 536401 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 17:56:39,479 3439 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 17:56:40,637 4597 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 17:56:40,642 4602 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 17:56:40,674 4634 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:56:40 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:56:41,651 5611 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 17:56:41,869 5829 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 17:56:41,888 5848 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 17:56:41,969 5929 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:56:42,023 5983 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 17:56:42,058 6018 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:56:42,103 6063 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 17:56:42,444 6404 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 17:56:42,454 6414 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 17:56:42,455 6415 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 17:56:42,661 6621 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 17:56:42,661 6621 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1987 ms
2018-12-20 17:56:42,844 6804 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 17:56:42,845 6805 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 17:56:42,850 6810 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 17:56:42,851 6811 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 17:56:42,851 6811 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 17:56:42,852 6812 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 17:56:43,476 7436 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 17:56:43,534 7494 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 17:57:23,677 47637 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 17:57:23,679 47639 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 17:57:23,682 47642 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 17:57:23,683 47643 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 17:57:23,684 47644 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 17:57:23,687 47647 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 17:57:23,687 47647 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 17:57:24,052 48012 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:56:40 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 17:57:24,110 48070 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:57:24,111 48071 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:57:24,153 48113 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 17:57:26,272 50232 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:56:40 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 17:57:26,274 50234 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 17:57:26,301 50261 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 17:57:26,537 50497 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:57:26,537 50497 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:57:26,543 50503 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 17:57:26,543 50503 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 17:57:26,619 50579 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 17:57:26,629 50589 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 17:57:26,629 50589 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 17:57:26,631 50591 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 17:57:26,633 50593 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 17:57:26,649 50609 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 17:57:26,663 50623 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 17:57:28,162 52122 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 17:57:28,180 52140 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 17:57:28,188 52148 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 17:57:28,293 52253 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 17:57:28,303 52263 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 17:57:28,315 52275 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 17:57:28,337 52297 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 17:57:28,342 52302 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 52.124 seconds (JVM running for 53.208)
2018-12-20 17:57:38,839 62799 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 17:57:38,840 62800 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 17:57:38,865 62825 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 23 ms
2018-12-20 17:57:38,919 62879 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 17:57:38,923 62883 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 17:57:38,928 62888 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 17:57:39,801 63761 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 17:57:39,802 63762 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 17:57:39,803 63763 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 17:57:39,804 63764 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 17:57:39,807 63767 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Regular Case
2018-12-20 17:58:00,372 84332 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 17:58:00,373 84333 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 17:58:00,373 84333 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 17:58:01,101 85061 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 17:58:01,102 85062 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 17:58:01,102 85062 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 17:58:01,103 85063 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 17:58:01,103 85063 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Regular Case
2018-12-20 17:59:48,587 192547 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 17:59:48,588 192548 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 17:59:48,588 192548 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 17:59:49,329 193289 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Inactive
2018-12-20 17:59:49,330 193290 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 17:59:49,331 193291 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 17:59:49,331 193291 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:06:53,513 617473 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 18:06:53,514 617474 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 17:56:40 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:06:53,520 617480 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 18:06:53,521 617481 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 18:06:53,529 617489 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 18:06:54,441 618401 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 18:06:54,448 618408 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 18:06:54,463 618423 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 18:06:54,473 618433 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 18:07:02,839 3526 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 18:07:03,989 4676 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 18:07:03,995 4682 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 18:07:04,023 4710 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:07:04 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:07:05,282 5969 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 18:07:05,540 6227 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 18:07:05,564 6251 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 18:07:05,669 6356 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:07:05,733 6420 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 18:07:05,795 6482 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:07:05,861 6548 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:07:06,256 6943 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 18:07:06,266 6953 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 18:07:06,267 6954 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 18:07:06,499 7186 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 18:07:06,499 7186 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2476 ms
2018-12-20 18:07:06,685 7372 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 18:07:06,687 7374 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 18:07:06,691 7378 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 18:07:06,691 7378 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 18:07:06,692 7379 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 18:07:06,692 7379 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 18:07:07,327 8014 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 18:07:07,394 8081 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 18:07:47,670 48357 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 18:07:47,673 48360 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 18:07:47,675 48362 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 18:07:47,676 48363 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 18:07:47,677 48364 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 18:07:47,680 48367 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 18:07:47,681 48368 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 18:07:48,061 48748 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:07:04 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:07:48,126 48813 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:07:48,126 48813 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:07:48,174 48861 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:07:50,300 50987 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:07:04 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 18:07:50,302 50989 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 18:07:50,328 51015 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 18:07:50,624 51311 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 18:07:50,624 51311 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 18:07:50,630 51317 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 18:07:50,630 51317 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 18:07:50,709 51396 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 18:07:50,722 51409 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 18:07:50,723 51410 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 18:07:50,725 51412 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 18:07:50,727 51414 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 18:07:50,744 51431 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 18:07:50,756 51443 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 18:07:52,271 52958 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 18:07:52,290 52977 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 18:07:52,298 52985 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 18:07:52,408 53095 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 18:07:52,419 53106 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 18:07:52,436 53123 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 18:07:52,461 53148 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 18:07:52,466 53153 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 52.946 seconds (JVM running for 54.151)
2018-12-20 18:07:56,634 57321 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 18:07:56,635 57322 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 18:07:56,659 57346 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 24 ms
2018-12-20 18:07:56,715 57402 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:07:56,718 57405 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:07:56,723 57410 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:07:57,595 58282 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 18:07:57,597 58284 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 18:07:57,597 58284 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 18:07:57,598 58285 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:07:57,599 58286 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - StartTimeInMilis 1545290419920 EndTimeInMilis 1545330600000
2018-12-20 18:07:57,604 58291 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Regular Case
2018-12-20 18:10:04,136 184823 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 18:10:04,137 184824 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:07:04 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:10:04,143 184830 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 18:10:04,144 184831 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 18:10:04,151 184838 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 18:10:05,079 185766 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 18:10:05,083 185770 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 18:10:05,091 185778 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 18:10:05,099 185786 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 18:10:12,483 3549 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 18:10:13,647 4713 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 18:10:13,652 4718 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 18:10:13,691 4757 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:10:13 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:10:14,774 5840 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 18:10:14,999 6065 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 18:10:15,019 6085 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 18:10:15,105 6171 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:10:15,150 6216 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 18:10:15,189 6255 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:10:15,244 6310 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:10:15,608 6674 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 18:10:15,619 6685 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 18:10:15,620 6686 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 18:10:15,838 6904 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 18:10:15,838 6904 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2148 ms
2018-12-20 18:10:16,022 7088 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 18:10:16,024 7090 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 18:10:16,029 7095 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 18:10:16,030 7096 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 18:10:16,030 7096 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 18:10:16,030 7096 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 18:10:16,648 7714 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 18:10:16,708 7774 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 18:10:56,114 47180 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 18:10:56,116 47182 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 18:10:56,119 47185 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 18:10:56,120 47186 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 18:10:56,121 47187 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 18:10:56,124 47190 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 18:10:56,125 47191 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 18:10:56,491 47557 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:10:13 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:10:56,550 47616 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:10:56,550 47616 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:10:56,604 47670 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:10:58,698 49764 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:10:13 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 18:10:58,700 49766 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 18:10:58,726 49792 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 18:10:58,962 50028 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 18:10:58,962 50028 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 18:10:58,969 50035 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 18:10:58,969 50035 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 18:10:59,049 50115 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 18:10:59,060 50126 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 18:10:59,060 50126 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 18:10:59,063 50129 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 18:10:59,066 50132 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 18:10:59,080 50146 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 18:10:59,092 50158 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 18:11:00,534 51600 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 18:11:00,556 51622 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 18:11:00,566 51632 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 18:11:00,681 51747 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 18:11:00,693 51759 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 18:11:00,706 51772 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 18:11:00,733 51799 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 18:11:00,738 51804 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 51.61 seconds (JVM running for 52.768)
2018-12-20 18:11:07,392 58458 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 18:11:07,392 58458 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 18:11:07,418 58484 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 26 ms
2018-12-20 18:11:07,473 58539 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:11:07,476 58542 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:11:07,481 58547 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:11:08,335 59401 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 18:11:08,338 59404 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 18:11:08,339 59405 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 18:11:08,339 59405 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:11:08,339 59405 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - contractStatus Active
2018-12-20 18:12:01,023 112089 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:12:01,024 112090 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:12:01,025 112091 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:12:02,647 113713 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] ERROR c.e.f.dao.impl.FreezeServiceDAOImpl - Error occured while obtaining member agreement detail Index: 0, Size: 0
2018-12-20 18:12:02,692 113758 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-2] [] [] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IndexOutOfBoundsException: Index: 0, Size: 0] with root cause
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653)
	at java.util.ArrayList.get(ArrayList.java:429)
	at com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl.getMemberAgreementDetail(FreezeServiceDAOImpl.java:54)
	at com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl$$FastClassBySpringCGLIB$$64d44286.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:738)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:136)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:673)
	at com.equinoxfitness.freezeservice.dao.impl.FreezeServiceDAOImpl$$EnhancerBySpringCGLIB$$65bee122.getMemberAgreementDetail(<generated>)
	at com.equinoxfitness.freezeservice.controller.service.impl.FreezeServiceImpl.checkFreezeEligibility(FreezeServiceImpl.java:49)
	at com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(FreezeServiceController.java:75)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2018-12-20 18:13:23,882 194948 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 18:13:23,883 194949 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:10:13 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:13:23,889 194955 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 18:13:23,889 194955 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 18:13:23,896 194962 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 18:13:24,743 195809 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 18:13:24,747 195813 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 18:13:24,755 195821 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 18:13:24,762 195828 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 18:13:52,469 3614 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 18:13:53,622 4767 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 18:13:53,627 4772 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 18:13:53,664 4809 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:13:53 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:13:54,753 5898 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 18:13:54,980 6125 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 18:13:55,001 6146 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 18:13:55,090 6235 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:13:55,132 6277 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 18:13:55,168 6313 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:13:55,212 6357 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:13:55,549 6694 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 18:13:55,560 6705 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 18:13:55,561 6706 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 18:13:55,770 6915 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 18:13:55,771 6916 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2106 ms
2018-12-20 18:13:55,953 7098 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 18:13:55,955 7100 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 18:13:55,960 7105 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 18:13:55,961 7106 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 18:13:55,961 7106 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 18:13:55,961 7106 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 18:13:56,751 7896 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 18:13:56,827 7972 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 18:14:38,119 49264 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 18:14:38,121 49266 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 18:14:38,123 49268 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 18:14:38,124 49269 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 18:14:38,125 49270 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 18:14:38,129 49274 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 18:14:38,129 49274 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 18:14:38,497 49642 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:13:53 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:14:38,556 49701 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:14:38,556 49701 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:14:38,601 49746 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:14:40,632 51777 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:13:53 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 18:14:40,633 51778 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 18:14:40,659 51804 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 18:14:40,882 52027 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 18:14:40,882 52027 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 18:14:40,889 52034 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 18:14:40,889 52034 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 18:14:40,965 52110 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 18:14:40,975 52120 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 18:14:40,975 52120 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 18:14:40,977 52122 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 18:14:40,979 52124 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 18:14:40,993 52138 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 18:14:41,004 52149 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 18:14:42,516 53661 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 18:14:42,547 53692 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 18:14:42,560 53705 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 18:14:42,718 53863 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 18:14:42,735 53880 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 18:14:42,759 53904 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 18:14:42,790 53935 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 18:14:42,798 53943 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 53.746 seconds (JVM running for 54.918)
2018-12-20 18:14:54,380 65525 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 18:14:54,381 65526 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 18:14:54,409 65554 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 28 ms
2018-12-20 18:14:54,469 65614 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:14:54,474 65619 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:14:54,480 65625 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:14:55,378 66523 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] ERROR c.e.f.dao.impl.FreezeServiceDAOImpl - Error occured while obtaining member agreement detail Index: 0, Size: 0
2018-12-20 18:25:36,340 707485 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:25:36,341 707486 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:25:36,341 707486 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:25:37,081 708226 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 18:25:37,081 708226 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 18:25:37,082 708227 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 18:25:37,082 708227 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:25:37,082 708227 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - contractStatus Active
2018-12-20 18:25:37,082 708227 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - membershipClass Corp Bank of NY Mellon cc Select
2018-12-20 18:25:37,083 708228 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Regular Case
2018-12-20 18:26:04,377 735522 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 18:26:04,378 735523 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:13:53 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:26:04,385 735530 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 18:26:04,386 735531 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 18:26:04,395 735540 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 18:26:05,315 736460 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 18:26:05,323 736468 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 18:26:05,342 736487 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 18:26:05,355 736500 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
2018-12-20 18:26:13,030 3660 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at: http://localhost:8888
2018-12-20 18:26:14,177 4807 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.c.c.c.ConfigServicePropertySourceLocator - Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/freeze/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2018-12-20 18:26:14,181 4811 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - The following profiles are active: common-framework,moso,common-stag
2018-12-20 18:26:14,212 4842 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:26:14 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:26:15,252 5882 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2018-12-20 18:26:15,529 6159 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.context.scope.GenericScope - BeanFactory id=8b25282d-f7d1-346c-abe2-f9369ce4f5ba
2018-12-20 18:26:15,550 6180 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2018-12-20 18:26:15,645 6275 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$f944a933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:26:15,688 6318 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
2018-12-20 18:26:15,726 6356 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54ace434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:26:15,770 6400 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$70c6e731] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2018-12-20 18:26:16,129 6759 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 8011 (http)
2018-12-20 18:26:16,140 6770 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2018-12-20 18:26:16,141 6771 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.23
2018-12-20 18:26:16,357 6987 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2018-12-20 18:26:16,358 6988 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2145 ms
2018-12-20 18:26:16,545 7175 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
2018-12-20 18:26:16,546 7176 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'messageDispatcherServlet' to [/services/*]
2018-12-20 18:26:16,550 7180 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2018-12-20 18:26:16,550 7180 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2018-12-20 18:26:16,550 7180 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
2018-12-20 18:26:16,550 7180 [freeze] [A2ML10676] [8011] [localhost-startStop-1] [] [] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2018-12-20 18:26:17,203 7833 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.xml.XmlBeanDefinitionReader - Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2018-12-20 18:26:17,264 7894 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.support.SQLErrorCodesFactory - SQLErrorCodes loaded: [DB2, Derby, H2, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase, Hana]
2018-12-20 18:26:57,712 48342 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v1/freeze-eligible],methods=[POST],produces=[application/json]}" onto public org.springframework.http.ResponseEntity<com.equinoxfitness.freezeservice.contract.CheckFreezeEligiblityOutput> com.equinoxfitness.freezeservice.controller.FreezeServiceController.checkFreezeEligibility(com.equinoxfitness.freezeservice.contract.CheckFreezeEligibilityInput) throws java.lang.Exception
2018-12-20 18:26:57,714 48344 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
2018-12-20 18:26:57,717 48347 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
2018-12-20 18:26:57,717 48347 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
2018-12-20 18:26:57,718 48348 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
2018-12-20 18:26:57,722 48352 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2018-12-20 18:26:57,722 48352 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2018-12-20 18:26:58,106 48736 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:26:14 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 18:26:58,167 48797 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:26:58,167 48797 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:26:58,211 48841 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.h.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2018-12-20 18:27:00,318 50948 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.u.f.SpringTemplateLoader - SpringTemplateLoader for FreeMarker: using resource loader [org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:26:14 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c] and template loader path [classpath:/templates/]
2018-12-20 18:27:00,321 50951 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.w.s.v.f.FreeMarkerConfigurer - ClassTemplateLoader for Spring macros added to FreeMarker configuration
2018-12-20 18:27:00,358 50988 [freeze] [A2ML10676] [8011] [main] [] [] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2018-12-20 18:27:00,584 51214 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 18:27:00,585 51215 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 18:27:00,591 51221 [freeze] [A2ML10676] [8011] [main] [] [] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2018-12-20 18:27:00,591 51221 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2018-12-20 18:27:00,665 51295 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Registering beans for JMX exposure on startup
2018-12-20 18:27:00,677 51307 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'refreshScope' has been autodetected for JMX exposure
2018-12-20 18:27:00,678 51308 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'environmentManager' has been autodetected for JMX exposure
2018-12-20 18:27:00,680 51310 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2018-12-20 18:27:00,683 51313 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2018-12-20 18:27:00,701 51331 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2018-12-20 18:27:00,714 51344 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=765f05af,type=ConfigurationPropertiesRebinder]
2018-12-20 18:27:02,380 53010 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2018-12-20 18:27:02,398 53028 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2018-12-20 18:27:02,405 53035 [freeze] [A2ML10676] [8011] [main] [] [] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2018-12-20 18:27:02,513 53143 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8011"]
2018-12-20 18:27:02,523 53153 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8011"]
2018-12-20 18:27:02,539 53169 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2018-12-20 18:27:02,562 53192 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat started on port(s): 8011 (http)
2018-12-20 18:27:02,568 53198 [freeze] [A2ML10676] [8011] [main] [] [] INFO  c.e.f.FreezeServiceApplication - Started FreezeServiceApplication in 53.001 seconds (JVM running for 54.174)
2018-12-20 18:27:13,833 64463 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 18:27:13,833 64463 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization started
2018-12-20 18:27:13,856 64486 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] INFO  o.s.web.servlet.DispatcherServlet - FrameworkServlet 'dispatcherServlet': initialization completed in 23 ms
2018-12-20 18:27:13,911 64541 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:27:13,914 64544 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:27:13,921 64551 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:27:14,836 65466 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 18:27:14,838 65468 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 18:27:14,839 65469 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 18:27:14,839 65469 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-1] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:28:27,932 138562 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:28:27,933 138563 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:28:27,933 138563 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:28:28,665 139295 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Inactive
2018-12-20 18:28:28,666 139296 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Thu Dec 20 12:50:19 IST 2018
2018-12-20 18:28:28,667 139297 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 18:28:28,667 139297 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:28:28,668 139298 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - contractStatus NonFinalized
2018-12-20 18:28:28,668 139298 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-3] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - membershipClass Employee Benefit - Select
2018-12-20 18:34:29,077 499707 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:34:29,079 499709 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:34:29,079 499709 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:34:29,806 500436 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Inactive
2018-12-20 18:34:29,808 500438 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Mon Dec 31 12:50:19 IST 2018
2018-12-20 18:34:29,808 500438 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 18:34:29,808 500438 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:34:29,809 500439 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - contractStatus NonFinalized
2018-12-20 18:34:29,809 500439 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-5] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - membershipClass Employee Benefit - Select
2018-12-20 18:34:52,994 523624 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:34:52,995 523625 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:34:52,995 523625 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:34:53,740 524370 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 18:34:53,740 524370 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Mon Dec 31 12:50:19 IST 2018
2018-12-20 18:34:53,741 524371 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 18:34:53,741 524371 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:34:53,741 524371 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - contractStatus Active
2018-12-20 18:34:53,742 524372 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - membershipClass Select Access
2018-12-20 18:34:53,745 524375 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-6] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Regular Case
2018-12-20 18:36:05,022 595652 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.c.FreezeServiceController - Calling Service
2018-12-20 18:36:05,023 595653 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.c.s.impl.FreezeServiceImpl - Calling DAO 
2018-12-20 18:36:05,023 595653 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Getting member agreementDetail from tenant
2018-12-20 18:36:05,760 596390 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.dao.impl.FreezeServiceDAOImpl - Member Status Active
2018-12-20 18:36:05,761 596391 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - FreezeEndDate : Sat Dec 22 12:50:19 IST 2018
2018-12-20 18:36:05,762 596392 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Inside Eligibility Rule check
2018-12-20 18:36:05,762 596392 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Freeze Reason Regular
2018-12-20 18:36:05,762 596392 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - contractStatus Active
2018-12-20 18:36:05,762 596392 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - membershipClass Select Access
2018-12-20 18:36:05,763 596393 [freeze] [A2ML10676] [8011] [http-nio-8011-exec-8] [] [] DEBUG c.e.f.utils.FreezeEligibilityRule - Regular Case
2018-12-20 19:06:37,946 2428576 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application shutdown requested.
2018-12-20 19:06:37,963 2428593 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@765f05af: startup date [Thu Dec 20 18:26:14 IST 2018]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c40365c
2018-12-20 19:06:38,050 2428680 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2018-12-20 19:06:38,052 2428682 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2018-12-20 19:06:38,079 2428709 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8011"]
2018-12-20 19:06:39,565 2430195 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2018-12-20 19:06:39,586 2430216 [freeze] [A2ML10676] [8011] [localhost-startStop-2] [] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2018-12-20 19:06:39,640 2430270 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8011"]
2018-12-20 19:06:39,856 2430486 [freeze] [A2ML10676] [8011] [RMI TCP Connection(2)-127.0.0.1] [] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8011"]
