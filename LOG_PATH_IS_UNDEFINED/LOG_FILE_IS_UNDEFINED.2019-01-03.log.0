2019-01-03 12:50:57,971 1500 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 12:50:57 IST 2019]; root of context hierarchy
2019-01-03 12:50:58,165 1694 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 12:50:58,321 1850 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 12:50:58,384 1913 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ac3eba00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 13:51:52,460 1536 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 13:51:52 IST 2019]; root of context hierarchy
2019-01-03 13:51:52,619 1695 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 13:51:52,760 1836 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 13:51:52,807 1883 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 13:57:01,504 1440 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 13:57:01 IST 2019]; root of context hierarchy
2019-01-03 13:57:01,695 1631 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 13:57:01,851 1787 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 13:57:01,898 1834 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d66acf56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 14:00:08,751 2680 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 14:00:08 IST 2019]; root of context hierarchy
2019-01-03 14:00:08,883 2812 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 14:00:09,039 2968 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 14:00:09,086 3015 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 14:02:15,075 1348 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 14:02:15 IST 2019]; root of context hierarchy
2019-01-03 14:02:15,261 1534 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 14:02:15,391 1664 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 14:02:15,484 1757 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 15:32:28,697 1673 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 15:32:28 IST 2019]; root of context hierarchy
2019-01-03 15:32:28,873 1849 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 15:32:29,014 1990 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 15:32:29,067 2043 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:14:36,133 1777 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 18:14:36 IST 2019]; root of context hierarchy
2019-01-03 18:14:36,328 1972 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 18:14:36,502 2146 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:14:36,577 2221 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:20:01,638 2190 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 18:20:01 IST 2019]; root of context hierarchy
2019-01-03 18:20:01,882 2434 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 18:20:02,087 2639 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:20:02,154 2706 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:25:24,204 1950 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 18:25:24 IST 2019]; root of context hierarchy
2019-01-03 18:25:24,422 2168 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 18:25:24,589 2335 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:25:24,669 2415 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:33:24,564 1805 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 18:33:24 IST 2019]; root of context hierarchy
2019-01-03 18:33:24,760 2001 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 18:33:24,933 2174 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:33:25,019 2260 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:41:40,740 1780 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 18:41:40 IST 2019]; root of context hierarchy
2019-01-03 18:41:40,947 1987 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 18:41:41,129 2169 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:41:41,204 2244 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:41:55,123 1851 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 18:41:55 IST 2019]; root of context hierarchy
2019-01-03 18:41:55,316 2044 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 18:41:55,543 2271 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:41:55,703 2431 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2019-01-03 18:47:09,977 1737 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@131ef10: startup date [Thu Jan 03 18:47:09 IST 2019]; root of context hierarchy
2019-01-03 18:47:10,168 1928 [freeze] [A2ML10676] [8011] [background-preinit] [] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
2019-01-03 18:47:10,374 2134 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2019-01-03 18:47:10,445 2205 [freeze] [A2ML10676] [8011] [main] [] [] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1173aa5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
